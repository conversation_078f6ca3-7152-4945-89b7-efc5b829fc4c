<template>
  <div class="main-container">
    <vehicle-defects
      :resourceId="resourceId"
      :status="field.status"
      @checkValidity="checkValidity"
      @hasValidationError="checkValidationError"
      @setLoading="setLoading"
    />
    <div class="upload-section-vehicle">
      <div class="title">Ավտոմեքենայի նկարներ</div>
      <div>
        <media-view :resourceId="resourceId" :processing="isProcessing()" />
      </div>
    </div>
    <div class="approve-button">
      <button
        class="btn btn-default btn-primary"
        :disabled="reviewDisabled || loading"
        v-if="canReview()"
        @click="review()"
      >
        {{ __('Send for Review') }}
      </button>
      <button
        class="btn btn-default btn-primary"
        v-if="canPledge()"
        @click="openProcessModal"
      >
        {{ __('Pledge') }}
      </button>
      <button
        class="btn btn-default btn-primary"
        :disabled="confirmDisabled || loading"
        v-if="canConfirm()"
        @click="confirm()"
      >
        {{ __('Confirm') }}
      </button>
      <button
        class="btn btn-default btn-primary"
        :disabled="compProcessDisabled || loading"
        v-if="canProcess()"
        @click="process()"
      >
        {{ __('Process') }}
      </button>
      <button
        class="btn btn-default btn-danger"
        :disabled="rejectDisabled || loading"
        v-if="canReject()"
        @click="reject()"
      >
        {{ __('Reject') }}
      </button>
      <button
        class="btn btn-default btn-danger"
        :disabled="rejectDisabled || loading"
        v-if="canCancelPledge()"
        @click="reject()"
      >
        {{ __('Cancel Pledge') }}
      </button>
    </div>
    <loading v-if="loading" large="true" />
    <process-modal
      v-if="showProcessModal"
      :resourceId="resourceId"
      @close="hideProcessModal"
      @process-modal-loading="setProcessModalLoading"
    />
  </div>
</template>

<script>
import EventBus from '../eventBus';

export default {
  computed: {
    compProcessDisabled() {
      return this.processDisabled || this.hasValidationError;
    },
  },

  props: ['resource', 'resourceName', 'resourceId', 'field'],

  data: () => ({
    showProcessModal: false,
    loading: false,
    isMileageAcceptable: true,
    hasValidationError: false,
    reviewDisabled: false,
    processDisabled: false,
    confirmDisabled: false,
    rejectDisabled: false,
    isProcessModalLoading: true,
  }),

  methods: {
    checkValidity(isMileageAcceptable) {
      if (isMileageAcceptable !== undefined) {
        this.isMileageAcceptable = isMileageAcceptable;
      }
    },

    setLoading(value) {
      this.loading = value;
    },

    canReject() {
      if (
        this.field.pledge_flow.value ===
        Nova.config.constants.OVL_STRATEGY.DIRECT
      ) {
        return (
          this.field.canVerify &&
          (this.field.status === Nova.config.constants.LOAN_STATUS.PROCESSING ||
            this.field.status ===
              Nova.config.constants.LOAN_STATUS.PARTIAL_PROCESSED)
        );
      }

      return (
        this.field.canConfirm &&
        this.field.status === Nova.config.constants.LOAN_STATUS.REVIEW
      );
    },

    canCancelPledge() {
      const { pledge_flow, canConfirm, status } = this.field;
      const { OVL_STRATEGY, LOAN_STATUS } = Nova.config.constants;

      if (pledge_flow.value === OVL_STRATEGY.DIRECT) {
        return canConfirm && status === LOAN_STATUS.PROCESSED;
      } else {
        return canConfirm && status === LOAN_STATUS.REVIEW;
      }
    },

    canReview() {
      return (
        this.field.pledge_flow.value ===
          Nova.config.constants.OVL_STRATEGY.SUPERVISED &&
        this.field.canVerify &&
        (this.field.status === Nova.config.constants.LOAN_STATUS.PROCESSING ||
          this.field.status ===
            Nova.config.constants.LOAN_STATUS.PARTIAL_PROCESSED)
      );
    },

    canProcess() {
      if (
        this.field.pledge_flow.value ===
        Nova.config.constants.OVL_STRATEGY.DIRECT
      ) {
        return (
          this.isMileageAcceptable &&
          this.field.canVerify &&
          (this.field.status === Nova.config.constants.LOAN_STATUS.PROCESSING ||
            this.field.status ===
              Nova.config.constants.LOAN_STATUS.PARTIAL_PROCESSED)
        );
      }

      return (
        this.field.canConfirm &&
        this.field.status === Nova.config.constants.LOAN_STATUS.REVIEW
      );
    },

    canPledge() {
      return (
        (this.field.canConfirm || this.field.canVerify) &&
        this.field.status === Nova.config.constants.LOAN_STATUS.PROCESSED &&
        this.isProcessModalLoading
      );
    },

    canConfirm() {
      return (
        this.field.canConfirm &&
        this.field.status === Nova.config.constants.LOAN_STATUS.PLEDGED
      );
    },

    isProcessing() {
      return (
        this.field.status === Nova.config.constants.LOAN_STATUS.PROCESSING ||
        this.field.status ===
          Nova.config.constants.LOAN_STATUS.PARTIAL_PROCESSED
      );
    },

    openProcessModal() {
      this.showProcessModal = true;
    },

    setProcessModalLoading(isLoading) {
      this.isProcessModalLoading = !isLoading;
    },

    hideProcessModal() {
      this.showProcessModal = false;
    },

    async process() {
      EventBus.$emit('validate-defects');
      if (this.hasValidationError) {
        return;
      }

      this.loading = true;
      this.processDisabled = true;

      try {
        await Nova.request().post(
          `/nova-vendor/approve-vehicle-loan/loan/${this.resourceId}/process`
        );

        this.loading = false;

        this.$toasted.show(this.__('The action ran successfully!'), {
          type: 'success',
        });

        this.$router.push('/resources/vehicle-loans');
      } catch (error) {
        this.loading = false;
        this.processDisabled = false;

        const message =
          error.response.data.message || this.__('Something went wrong');
        this.$toasted.show(message, { type: 'error' });
      }
    },

    async review() {
      this.loading = true;
      this.reviewDisabled = true;

      try {
        await Nova.request().post(
          `/nova-vendor/approve-vehicle-loan/loan/${this.resourceId}/review`
        );

        this.loading = false;

        this.$toasted.show(this.__('The action ran successfully!'), {
          type: 'success',
        });

        this.$router.push('/resources/vehicle-loans');
      } catch (error) {
        this.loading = false;
        this.reviewDisabled = false;

        const message =
          error.response.data.message || this.__('Something went wrong');
        this.$toasted.show(message, { type: 'error' });
      }
    },

    async confirm() {
      this.loading = true;
      this.confirmDisabled = true;

      try {
        await Nova.request().post(
          `/nova-vendor/approve-vehicle-loan/loan/${this.resourceId}/confirm`
        );

        this.loading = false;

        this.$toasted.show(this.__('The action ran successfully!'), {
          type: 'success',
        });

        this.$router.push('/resources/vehicle-loans');
      } catch (error) {
        this.loading = false;
        this.confirmDisabled = false;

        const message =
          this.__(error.response.data.message) ||
          this.__('Something went wrong');
        this.$toasted.show(message, { type: 'error' });
      }
    },

    async reject() {
      this.loading = true;
      this.rejectDisabled = true;

      try {
        await Nova.request().post(
          `/nova-vendor/approve-vehicle-loan/loan/${this.resourceId}/reject`
        );

        this.loading = false;

        this.$toasted.show(this.__('The action ran successfully!'), {
          type: 'success',
        });

        this.$router.push('/resources/vehicle-loans');
      } catch (error) {
        this.loading = false;
        this.rejectDisabled = false;

        const message =
          error.response.data.message || this.__('Something went wrong');
        this.$toasted.show(message, { type: 'error' });
      }
    },

    checkValidationError(val) {
      this.hasValidationError = val;
    },
  },
};
</script>
