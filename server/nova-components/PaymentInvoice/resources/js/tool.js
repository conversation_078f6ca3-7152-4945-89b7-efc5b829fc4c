import VueDatepicker from '@northwalker/vue-hotel-datepicker';

Nova.booting((Vue, router, store) => {
  router.addRoutes([
    {
      name: 'payment-invoice',
      path: '/payment-invoice',
      component: require('./components/Tool'),
    },
  ]),
    Vue.component('loading', require('./components/Loading'));
  Vue.component('calendar', require('./components/CalendarIcon'));
  Vue.component('vue-datepicker', VueDatepicker);
});
