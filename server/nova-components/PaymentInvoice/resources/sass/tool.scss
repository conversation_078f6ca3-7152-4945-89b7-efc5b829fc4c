.date-picker-section {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  align-items: center;

  svg {
    margin-right: 5px;
  }

  .date-picker {
    input {
      min-width: 250px;
      border: 1px solid lightgrey;
      padding: 5px;
      border-radius: 0.25rem;
      text-indent: 10px;

      &::placeholder {
        color: gray;
      }

      &:focus {
        border: 1px solid rgb(64, 153, 222);
        outline: none;
      }
    }
  }
}

.auth-section {
  display: flex;
  flex-direction: column;
  margin-top: 40px;

  input {
    min-width: 250px;
    border: 1px solid lightgrey;
    padding: 5px;
    border-radius: 0.25rem;
    height: 44px;
    margin-left: 31px;
    text-indent: 10px;

    &::placeholder {
      color: gray;
    }

    &:focus-visible {
      border: 1px solid rgb(64, 153, 222);
      outline: none;
    }

    &:first-child {
      margin-bottom: 20px;
    }
  }
}
