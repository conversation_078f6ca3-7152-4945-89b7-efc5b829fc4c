<template>
  <div>
    <heading class="mb-6">{{ __('SOAP Monitoring FICO') }}</heading>
    <div class="main-container">
      <div class="inline-flex">
        <input
          ref="file"
          class="form-file-input"
          type="file"
          id="file"
          @change="handleFileUpload"
          :disabled="loading"
        />
        <label
          :disabled="loading"
          for="file"
          class="btn btn-default btn-primary margin cursor-pointer"
        >
          {{ __('Choose File') }}
        </label>
        <select
          id="monitoring-types"
          class="block form-select margin"
          v-model="selectedType"
          :disabled="loading"
        >
          <option v-for="type in types" :value="type.value">
            {{ type.text }}
          </option>
        </select>

        <button
          class="btn btn-default btn-primary margin"
          @click="openConfirmModal"
          :disabled="disableStart"
        >
          {{ __('Start') }}
        </button>
      </div>
      <div>
        <label v-if="fileName" id="file-name" ref="fileName">
          {{ fileName }}
        </label>
      </div>
      <div class="block mx-auto loading">
        <loading v-if="loading" :large="true" />
      </div>
      <div v-if="statusMessage != ''" id="status-message">
        {{ statusMessage }} {{ monitoringName }}
      </div>
      <confirm-modal
        v-if="showModal"
        @close="hideConfirmModal"
        @confirm="confirmed"
      />
    </div>
  </div>
</template>

<script>
const MONITORING_STATUS = Nova.config.constants.MONITORING_STATUSES;

export default {
  data() {
    return {
      loading: true,
      selectedType: '01',
      types: [
        { text: this.__('Monitoring Without Fico'), value: '01' },
        { text: this.__('Monitoring Retro'), value: '02' },
        { text: this.__('Monitoring Only Fico'), value: '03' },
      ],
      monitoringName: null,
      fileName: null,
      interval: null,
      disableStart: true,
      showModal: false,
      statusMessage: '',
      statusMessages: {
        failed: this.__('Action is failed'),
        success: this.__('The action ran successfully!'),
      },
    };
  },

  mounted() {
    this.setInitialValue();
  },

  beforeDestroy() {
    if (this.interval) {
      clearInterval(this.interval);
    }
  },

  methods: {
    openConfirmModal() {
      this.showModal = true;
    },

    hideConfirmModal() {
      this.showModal = false;
    },

    confirmed() {
      this.startMonitoring();
      this.disableStart = true;

      this.hideConfirmModal();
    },

    async setInitialValue() {
      this.loading = true;
      const { status, name } = await this.getMonitoring();
      this.monitoringName = name;
      this.fileName = name;

      if (status == null) {
        this.loading = false;
        this.fileName = null;

        return;
      }
      if (status === MONITORING_STATUS.FAILED) {
        this.loading = false;
        this.fileName = null;
        this.statusMessage = this.statusMessages.failed;

        return;
      }
      if (status === MONITORING_STATUS.COMPLETED) {
        this.loading = false;
        this.fileName = null;
        this.statusMessage = this.statusMessages.success;

        return;
      }

      this.pollStatus();
    },

    handleFileUpload() {
      this.file = this.$refs.file.files[0];
      this.fileName = this.file.name;
      this.disableStart = false;
    },

    async pollStatus() {
      this.interval = setInterval(
        async function() {
          const { status, name } = await this.getMonitoring();
          this.fileName = name;
          this.monitoringName = name;

          if (status === MONITORING_STATUS.COMPLETED) {
            this.loading = false;
            this.fileName = null;
            this.statusMessage = this.statusMessages.success;

            this.$toasted.show(this.statusMessages.success, {
              type: 'success',
            });
            clearInterval(this.interval);
          } else if (status === MONITORING_STATUS.FAILED) {
            this.loading = false;
            this.fileName = null;
            this.statusMessage = this.statusMessages.failed;

            this.$toasted.show(this.statusMessages.failed, { type: 'error' });
            clearInterval(this.interval);
          }
        }.bind(this),
        5000
      );
    },

    async getMonitoring() {
      const response = await Nova.request().get(
        `/nova-vendor/acra-monitoring/get-monitoring`
      );

      return response.data;
    },

    async startMonitoring() {
      try {
        const formData = new FormData();
        this.loading = true;
        this.statusMessage = '';

        formData.append('file', this.file);
        formData.append('type', this.selectedType);

        await axios.post('/nova-vendor/acra-monitoring/start', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });

        this.pollStatus();
      } catch (error) {
        this.loading = false;

        const message =
          this.__(error.response.data.message) || this.statusMessages.failed;
        this.statusMessage = this.statusMessages.failed;
        this.$toasted.show(this.__(`${message}`), { type: 'error' });
      }
    },
  },
};
</script>
