<?php

namespace App\Api\V2\Controllers;

use App\Api\V2\Requests\CalculateLoanMonthlyPaymentRequest;
use App\Api\V2\Requests\CalculateLoanScheduleRequest;
use App\Api\V2\Requests\CardToCardTransferRequest;
use App\Api\V2\Requests\CarVerificationRequest;
use App\Api\V2\Requests\LoanApproveRequest;
use App\Api\V2\Requests\LoanRequest;
use App\Api\V2\Requests\PersonalInfoRequest;
use App\Api\V2\Requests\WalletPaymentTransferRequest;
use App\Api\V2\Transformers\CitizenConfigTransformer;
use App\Api\V2\Transformers\DWLoanTransformer;
use App\Api\V2\Transformers\DWTransactionHistoryTransformer;
use App\Api\V2\Transformers\LoanTransformer;
use App\Exceptions\BlacklistedException;
use App\Exceptions\CardCheckFailureException;
use App\Exceptions\CardNotValidException;
use App\Exceptions\DuplicatePhoneNumberException;
use App\Exceptions\IdramWalletNotValidException;
use App\Exceptions\InternalErrorException;
use App\Exceptions\InvalidEasypayWalletException;
use App\Exceptions\InvalidLoanException;
use App\Exceptions\InvalidSuuidException;
use App\Exceptions\InvalidTelcellWalletException;
use App\Exceptions\LoanNotFoundException;
use App\Exceptions\PaymentFailureException;
use App\Exceptions\SpouseDocumentException;
use App\Exceptions\TransferTypeNotFoundException;
use App\Exceptions\WalletTransferServiceUnavailableException;
use App\Http\Controllers\Controller;
use App\Interfaces\IDocumentService;
use App\Interfaces\ILoanService;
use App\Interfaces\IPaymentService;
use App\Interfaces\ISecurityService;
use App\Interfaces\IWarehouseService;
use App\Services\LoanConfigService;
use Dingo\Api\Http\Request;
use Dingo\Api\Http\Request as DingoRequest;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class LoanController extends Controller
{
    protected $loan_config_service;
    protected $loan_service;
    protected $payment_service;
    protected $security_service;
    protected $warehouse_service;

    /**
     * LoanController constructor.
     */
    public function __construct(
        ILoanService $loan_service,
        ISecurityService $security_service,
        IPaymentService $payment_service,
        LoanConfigService $loan_config_service,
        IWarehouseService $warehouse_service
    ) {
        $this->loan_config_service = $loan_config_service;
        $this->loan_service = $loan_service;
        $this->payment_service = $payment_service;
        $this->security_service = $security_service;
        $this->warehouse_service = $warehouse_service;
    }

    /**
     * Getting configs for citizen depending on fico score.
     */
    public function getConfigsForCitizen(DingoRequest $request)
    {
        try {
            $loan_security = $this->security_service->resolveLoanSecurity();

            $loan_type_id = $request->get('loan_type_id') ?? $loan_security->loan_type_id;

            $loan_configs = $this->fetchConfigs($loan_type_id);

            return fractal()->item($loan_configs)->transformWith(new CitizenConfigTransformer());
        } catch (BlacklistedException $e) {
            Log::error('Get transfer types, BlacklistedException', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw $e;
        } catch (InvalidSuuidException $e) {
            Log::error('Get transfer types, InvalidSuuidException', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw $e;
        } catch (Exception $e) {
            Log::error('Get loan configs, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    private function fetchConfigs($loan_type_id)
    {
        try {
            Log::info('Getting loan configs', ['loan_type_id' => $loan_type_id]);
            $loan_configs = $this->loan_config_service->getConfigs($loan_type_id);
            Log::info('Loan configs response', ['response' => $loan_configs]);

            return $loan_configs;
        } catch (Exception $e) {
            Log::error('Get loan configs, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function approve(LoanApproveRequest $request)
    {
        $payload = $request->only([
            'amount',
            'last_month_payment',
            'monthly_payment',
            'months',
            'total',
            'pipe_type_id',
            'vehicle_number',
            'trade_amount',
            'tech_passport',
            'submitted_apr',
            'top_up_offer',
            'vin',
        ]);

        try {
            $loan_security = $this->security_service->resolveLoanSecurity();

            $payload = array_merge($payload, [
                'document_number' => $loan_security->document_number,
                'email' => $loan_security->email,
                'phone_number' => $loan_security->phone_number,
            ]);

            Log::info('Approve loan', ['payload' => $payload]);
            $loan = $this->loan_service->create($payload);
            Log::info('Loan approve response', ['response' => $loan]);

            return fractal($loan, new LoanTransformer());
        } catch (InvalidSuuidException $e) {
            Log::error('Approve loan, InvalidSuuidException', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw $e;
        } catch (InvalidLoanException $e) {
            Log::error('Approve loan, InvalidLoanException', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw $e;
        } catch (SpouseDocumentException $e) {
            Log::error('Approve loan, SpouseDocumentException', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw $e;
        } catch (BlacklistedException $e) {
            Log::error('Approve loan, BlacklistedException', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw $e;
        } catch (Exception $e) {
            Log::error('Approve loan, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function calculateMonthlyPaymentRange(CalculateLoanMonthlyPaymentRequest $request)
    {
        try {
            $loan_security = $this->security_service->resolveLoanSecurity();

            $payload = [
                'amount' => $request->get('amount'),
                'vehicle_number' => $request->get('vehicle_number'),
                'trade_amount' => $request->get('trade_amount'),
                'tech_passport' => $request->get('tech_passport'),
                'document_number' => $loan_security->document_number,
                'email' => $loan_security->email,
                'phone_number' => $loan_security->phone_number,
                'offered_credit' => $request->get('offered_credit') ?? [],
                'osm' => $request->get('osm'),
                'is_new_ovl_customer' => $request->get('is_new_ovl_customer'),
            ];

            Log::info('Calculate monthly payment range payload', [$payload]);
            $result = $this->loan_service->calculateMonthlyPaymentRange($payload);
            Log::info('Calculated monthly payment range result', [$result]);

            return response()->json($result);
        } catch (InvalidSuuidException | SpouseDocumentException $e) {
            Log::warning('Calculate monthly payment range, '.get_class($e), ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw $e;
        } catch (Exception $e) {
            Log::error('Calculate monthly payment range, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function calculateScheduleDetails(CalculateLoanScheduleRequest $request)
    {
        try {
            $loan_security = $this->security_service->resolveLoanSecurity();

            $payload = [
                'amount' => $request->get('amount'),
                'monthly_payment' => $request->get('monthly_payment'),
                'vehicle_number' => $request->get('vehicle_number'),
                'trade_amount' => $request->get('trade_amount'),
                'tech_passport' => $request->get('tech_passport'),
                'document_number' => $loan_security->document_number,
                'email' => $loan_security->email,
                'phone_number' => $loan_security->phone_number,
                'offered_credit' => $request->get('offered_credit') ?? [],
            ];

            Log::info('Calculate schedule details payload', [$payload]);
            $result = $this->loan_service->calculateScheduleDetails($payload);
            Log::info('Calculated schedule details result', [$result]);

            return response()->json($result);
        } catch (InvalidSuuidException | SpouseDocumentException $e) {
            Log::warning('Calculate schedule details, '.get_class($e), ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw $e;
        } catch (Exception $e) {
            Log::error('Calculate schedule details, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function show()
    {
        try {
            $loan = $this->security_service->resolveLoan();

            return fractal($loan, new LoanTransformer());
        } catch (BlacklistedException $e) {
            Log::warning('Request loan data, BlacklistedException', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw $e;
        } catch (Exception $e) {
            Log::error('Request loan data, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function getPersonalInfo()
    {
        try {
            $loan = $this->security_service->resolveLoan();

            return fractal($loan, new LoanTransformer());
        } catch (BlacklistedException $e) {
            Log::warning('Get personal info, BlacklistedException', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw $e;
        } catch (Exception $e) {
            Log::error('Get personal info failure', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function updatePersonalInfo(PersonalInfoRequest $request)
    {
        $payload = $request->only(['notification_method', 'dispute_solution_method', 'additional_phone_number']);

        try {
            Log::info('Updating personal info', ['payload' => $payload]);
            $loan = $this->security_service->resolveLoan();
            $citizen = $this->loan_service->updatePersonalInfo($loan, $payload);
            $this->loan_service->storeAdditionalPhoneNumber($payload['additional_phone_number']);
            Log::info('Update personal info response', ['response' => $citizen]);

            return response()->json($citizen);
        } catch (DuplicatePhoneNumberException | BlacklistedException $e) {
            Log::warning('Update personal info, '.get_class($e), ['error' => $e->getMessage()]);

            throw $e;
        } catch (Exception $e) {
            Log::error('Update personal info, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function getCashOffices()
    {
        try {
            $loan = $this->security_service->resolveLoan();

            Log::info('Getting cash offices', ['amount' => $loan->amount]);
            $offices = $this->payment_service->getCashOffices($loan->loan_type_id, $loan->amount);

            return response()->json($offices);
        } catch (InvalidSuuidException | BlacklistedException $e) {
            Log::warning('Get cash offices, '.get_class($e), ['error' => $e->getMessage()]);

            throw $e;
        } catch (Exception $e) {
            Log::error('Get cash offices, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function getTransferTypes()
    {
        try {
            $loan = $this->security_service->resolveLoan();

            $this->loan_service->archiveAndCleanUpDocuments($loan);

            Log::debug('Getting transfer types');
            $types = $this->payment_service->getTransferTypes($loan->loan_type_id, $loan->amount, $loan->loan_security->loan_subtype_id);

            Log::debug('Get transfer types response', ['types' => $types]);

            return response()->json($types);
        } catch (InvalidSuuidException | BlacklistedException $e) {
            Log::warning('Get transfer types, '.get_class($e), ['error' => $e->getMessage()]);

            throw $e;
        } catch (Exception $e) {
            Log::error('Get transfer types, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function storeCashTransfer()
    {
        $params['transfer_type'] = constants('TRANSFER_TYPES.CASH_PAYMENT');

        try {
            $loan = $this->security_service->resolveLoan();

            $this->storeTransfer($loan, $params);

            return fractal($loan, new LoanTransformer());
        } catch (InvalidSuuidException | BlacklistedException $e) {
            Log::warning('Store Cash transfer, '.get_class($e), ['error' => $e->getMessage()]);

            throw $e;
        } catch (Exception $e) {
            Log::error('Store Cash transfer, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function storeCardToCardTransfer(CardToCardTransferRequest $request)
    {
        $params = $request->only([
            'card_number',
            'year',
            'month',
        ]);

        $params['transfer_type'] = constants('TRANSFER_TYPES.CARD_TO_CARD');

        try {
            $loan = $this->security_service->resolveLoan();

            $this->storeTransfer($loan, $params);

            return fractal($loan, new LoanTransformer());
        } catch (InvalidSuuidException | BlacklistedException | CardNotValidException | CardCheckFailureException $e) {
            Log::warning('Store Card to card transfer, '.get_class($e), ['error' => $e->getMessage()]);

            throw $e;
        } catch (Exception $e) {
            Log::error('Store Card to card transfer, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function storeEasypayWalletTransfer()
    {
        $params['transfer_type'] = constants('TRANSFER_TYPES.EASYPAY_WALLET');

        try {
            $loan = $this->security_service->resolveLoan();

            $this->storeTransfer($loan, $params);

            return fractal($loan, new LoanTransformer());
        } catch (InvalidSuuidException | BlacklistedException | InvalidEasypayWalletException | WalletTransferServiceUnavailableException $e) {
            Log::warning('Store EasyPay wallet transfer, '.get_class($e), ['error' => $e->getMessage()]);

            throw $e;
        } catch (Exception $e) {
            Log::error('Store EasyPay wallet transfer, Exception', ['error' => $e->getMessage()]);
            throw new InternalErrorException();
        }
    }

    public function storeIdramWalletTransfer(WalletPaymentTransferRequest $request)
    {
        $params = $request->only(['phone_number']);
        $params['transfer_type'] = constants('TRANSFER_TYPES.IDRAM_WALLET');

        try {
            $loan = $this->security_service->resolveLoan();

            $this->storeTransfer($loan, $params);

            return fractal($loan, new LoanTransformer());
        } catch (InvalidSuuidException | BlacklistedException | IdramWalletNotValidException | WalletTransferServiceUnavailableException $e) {
            Log::warning('Store Idram Wallet transfer, '.get_class($e), ['error' => $e->getMessage()]);

            throw $e;
        } catch (Exception $e) {
            Log::error('Store Idram Wallet transfer, Exception', ['error' => $e->getMessage()]);
            throw new InternalErrorException();
        }
    }

    public function storeTelcellWalletTransfer(WalletPaymentTransferRequest $request)
    {
        $params = $request->only(['phone_number']);
        $params['transfer_type'] = constants('TRANSFER_TYPES.TELCELL_WALLET');

        try {
            $loan = $this->security_service->resolveLoan();

            $this->storeTransfer($loan, $params);

            return fractal($loan, new LoanTransformer());
        } catch (InvalidSuuidException | BlacklistedException | InvalidTelcellWalletException | WalletTransferServiceUnavailableException $e) {
            Log::warning('Store Telcell wallet transfer, '.get_class($e), ['error' => $e->getMessage()]);

            throw $e;
        } catch (Exception $e) {
            Log::error('Store Telcell wallet transfer, Exception', ['error' => $e->getMessage()]);
            throw new InternalErrorException();
        }
    }

    public function storeManualTransfer()
    {
        $params['transfer_type'] = constants('TRANSFER_TYPES.MANUAL_TRANSFER');

        try {
            $loan = $this->security_service->resolveLoan();

            $this->storeTransfer($loan, $params);

            return fractal($loan, new LoanTransformer());
        } catch (InvalidSuuidException | BlacklistedException $e) {
            Log::warning('Store Manual transfer, '.get_class($e), ['error' => $e->getMessage()]);

            throw $e;
        } catch (Exception $e) {
            Log::error('Store Manual transfer, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    private function storeTransfer($loan, $params)
    {
        try {
            Log::info('Storing transfer', ['loan' => $loan, 'params' => $params]);
            $this->payment_service->storeTransferMethod($loan, $params);
            Log::info('Transfer stored');

            // Setting sign date is required for document generation
            $loan = $this->loan_service->setSignDateAndApr($loan);

            Log::info('Generating loan Documents');

            /** @var IDocumentService $loan_document_service */
            $loan_document_service = resolve('App\Interfaces\IDocumentService');
            $loan_document_service->getLoanDocumentsJob($loan)->dispatch($loan, $force = true, true);

            Log::info('Loan Documents generation successfully dispatched');
        } catch (PaymentFailureException $e) {
            Log::warning('Store transfer, PaymentFailureException', ['message' => $e->getMessage()]);
            throw $e;
        } catch (WalletTransferServiceUnavailableException $e) {
            Log::warning('Store transfer, WalletTransferServiceUnavailableException', ['message' => $e->getMessage()]);
            throw $e;
        } catch (TransferTypeNotFoundException $e) {
            Log::error('Store transfer, TransferTypeNotFoundException', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw $e;
        }
    }

    public function getDocuments()
    {
        try {
            $document_service = resolve('App\Interfaces\IDocumentService');
            $documents = $document_service->getLoanDocuments();

            return response()->json($documents);
        } catch (BlacklistedException $e) {
            Log::warning('Request loan documents, BlacklistedException', ['message' => $e->getMessage()]);
            throw $e;
        } catch (Exception $e) {
            Log::error('Request loan documents, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function fetchLoan(LoanRequest $request)
    {
        try {
            $loan = $this->loan_service->getLoanByPublicId($request->get('public_id'));

            return fractal($loan, new LoanTransformer());
        } catch (LoanNotFoundException $e) {
            Log::warning('Get loan by public id, LoanNotFoundException', ['message' => $e->getMessage()]);
            throw $e;
        } catch (Exception $e) {
            Log::error('Get loan by public id, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function fetchAgentSchedules()
    {
        try {
            $loan = $this->security_service->resolveLoan();

            $schedules = $this->loan_service->getAgentSchedules();

            return response()->json($schedules);
        } catch (InvalidSuuidException $e) {
            Log::error('Get agents, InvalidSuuidException', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw $e;
        } catch (BlacklistedException $e) {
            Log::error('Get agents, BlacklistedException', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw $e;
        } catch (Exception $e) {
            Log::error('Get agents, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function storeCarVerificationInfo(CarVerificationRequest $request)
    {
        $payload = $request->only([
            'checkup_date',
            'address',
            'notes',
        ]);

        try {
            $loan = $this->security_service->resolveLoan();

            Log::info('Storing car verification info', ['loan' => $loan, 'payload' => $payload]);
            $this->loan_service->storeCarVerificationInfo($loan, $payload);
            Log::info('Car verification info stored');

            return fractal($loan, new LoanTransformer());
        } catch (BlacklistedException $e) {
            Log::error('Storing car verification info, BlacklistedException', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw $e;
        } catch (Exception $e) {
            Log::error('Storing car verification info, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function fetchCashOfficesByPublicId(LoanRequest $request)
    {
        try {
            $loan = $this->loan_service->getLoanByPublicId($request->get('public_id'));

            Log::info('Getting cash offices by public id', ['amount' => $loan->amount]);
            $offices = $this->payment_service->getCashOffices($loan->loan_type_id, $loan->amount);
            Log::info('Get cash offices response by public id', ['offices' => $offices]);

            return response()->json($offices);
        } catch (LoanNotFoundException $e) {
            Log::error('Get cash offices by public id, LoanNotFoundException', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw $e;
        } catch (Exception $e) {
            Log::error('Get cash offices by public id, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function getAll()
    {
        try {
            $user = Auth::guard('api')->user();

            $loans = $this->warehouse_service->getLoansBySsn($user->profile->ssn);
            Log::info('Get all loans', ['ssn' => $user->profile->ssn, 'loans' => $loans]);

            return fractal()->collection($loans)->transformWith(new DWLoanTransformer())->toArray();
        } catch (Exception $e) {
            Log::error('Get all loans, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function getOne(Request $request)
    {
        try {
            $contract_number = $request->contract_number;

            $loan = $this->warehouse_service->getLoan($contract_number);
            Log::info('Get one loan', ['contract_number' => $contract_number, 'loan' => $loan]);

            if (!$loan) {
                throw new LoanNotFoundException();
            }

            return fractal()->item($loan)->transformWith(new DWLoanTransformer())->toArray();
        } catch (LoanNotFoundException $e) {
            Log::error('Get one loan, LoanNotFoundException', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw $e;
        } catch (Exception $e) {
            Log::error('Get one loan, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function getPayments(Request $request)
    {
        try {
            $user = Auth::guard('api')->user();
            $contract_number = $request->contract_number;

            $payments = $this->warehouse_service->getPaymentHistory($contract_number);
            Log::info('Get loan payments', ['ssn' => $user->profile->ssn, 'contract_number' => $contract_number, 'payments' => $payments]);

            return $payments;
        } catch (LoanNotFoundException $e) {
            Log::error('Get loan payments, LoanNotFoundException', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw $e;
        } catch (Exception $e) {
            Log::error('Get loan payments, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function getTransactions(Request $request)
    {
        try {
            $user = Auth::guard('api')->user();
            $contract_number = $request->contract_number;

            $transactions = $this->warehouse_service->getTransactionsHistory($contract_number);
            $sorted_transactions = collect($transactions)->sortBy('date');

            Log::info('Get loan transactions', ['ssn' => $user->profile->ssn, 'contract_number' => $contract_number, 'transactions' => $sorted_transactions]);

            return fractal()->collection($sorted_transactions)->transformWith(new DWTransactionHistoryTransformer())->toArray();
        } catch (Exception $e) {
            Log::error('Get loan transactions, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);

            throw new InternalErrorException();
        }
    }
}
