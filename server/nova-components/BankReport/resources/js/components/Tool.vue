<template>
  <div>
    <heading class="mb-6">{{ __('bank_report') }}</heading>
    <div class="main-container">
      <div class="inline-flex">
        <label id="file-name" ref="fileName" class="margin text-align-middle">
          {{ fileName }}
        </label>
        <input
          ref="file"
          class="form-file-input"
          type="file"
          id="file"
          name="name"
          @change="handleFileUpload"
        />
        <label
          for="file"
          class="form-file-btn btn btn-default btn-primary margin"
        >
          {{ __('Choose File') }}
        </label>
        <select
          id="banks"
          ref="banks"
          class="block form-control-sm form-select margin"
          v-model="selected"
        >
          <option v-for="bank in banks" :value="bank.value">
            {{ bank.text }}
          </option>
        </select>
        <button
          :disabled="downloadDisabled"
          @click="download"
          ref="download"
          class="btn btn-default btn-primary margin active"
        >
          {{ __('Download') }}
        </button>
        <button
          :disabled="saveHCDisabled"
          @click="openConfirmModal"
          ref="saveHC"
          class="btn btn-default btn-primary margin active"
        >
          {{ __('SaveHC') }}
        </button>
      </div>

      <div class="center" ref="table" v-if="tableVisibility">
        <loading v-if="loading" large="loading" />
        <div id="tabs" class="container">
          <div class="tabs">
            <a
              href="#"
              @click.prevent="activetab = 1"
              @click="changeTab"
              :class="[activetab === 1 ? 'active-tab' : '']"
              class="btn btn-default tab-button"
              >{{ __('Right') }}</a
            >
            <a
              href="#"
              @click.prevent="activetab = 2"
              @click="changeTab"
              :class="[activetab === 2 ? 'active-tab' : '']"
              class="btn btn-default tab-button"
              >{{ __('Wrong') }}</a
            >
          </div>
        </div>
        <card>
          <table
            class="table-inline table table-striped large-12 medium-12 small-12 cell"
          >
            <thead class="thead-dark">
              <tr>
                <th>{{ __('Date') }}</th>
                <th>{{ __('Check Number') }}</th>
                <th>{{ __('Contract Number') }}</th>
                <th>{{ __('Payment Type') }}</th>
                <th>{{ __('Amount') }}</th>
                <th>{{ __('Status') }}</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="bankReport in bankReports">
                <td>
                  {{ bankReport.date }}
                </td>
                <td>
                  {{ bankReport.check_number }}
                </td>
                <td @click="bankReport.contract_number_edit = true">
                  <input
                    v-if="activetab === 2 && bankReport.contract_number_edit"
                    v-model="bankReport.contract_number"
                    @blur="updateBankReportContractNumber(bankReport)"
                    @keyup.enter="$event.target.blur()"
                    v-focus
                  />
                  <div v-else>
                    <label>{{ bankReport.contract_number }}</label>
                  </div>
                </td>
                <td>
                  {{
                    Number(bankReport.payment_type)
                      ? __(
                          'Bank Report Payment Type ' + bankReport.payment_type
                        )
                      : bankReport.payment_type
                  }}
                </td>
                <td>
                  {{ bankReport.amount }}
                </td>
                <td>
                  <span
                    :class="
                      bankReport.status.success
                        ? 'status-success'
                        : 'status-fail'
                    "
                  >
                    {{ bankReport.status.title }}
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </card>
      </div>
      <confirm-modal
        v-if="showModal"
        :bankName="selected"
        @close="hideConfirmModal"
        @confirm="confirmed"
      />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      showModal: false,
      bankReports: {},
      bankReportsCorrect: [],
      bankReportsWrong: [],
      selected: 'converse',
      banks: [
        { text: this.__('converse'), value: 'converse' },
        { text: this.__('ameria'), value: 'ameria' },
        { text: this.__('abb'), value: 'abb' },
        { text: this.__('aeb'), value: 'aeb' },
        { text: this.__('evoca'), value: 'evoca' },
        { text: this.__('ardshin'), value: 'ardshin' },
        { text: this.__('ararat'), value: 'ararat' },
      ],
      tableVisibility: false,
      saveHCDisabled: true,
      downloadDisabled: true,
      loading: false,
      activetab: 1,
      fileName: '',
    };
  },

  directives: {
    focus: {
      inserted(el) {
        el.focus();
      },
    },
  },

  methods: {
    openConfirmModal() {
      this.showModal = true;
    },

    hideConfirmModal() {
      this.showModal = false;
    },

    confirmed() {
      this.saveHC();
      this.hideConfirmModal();
    },

    updateBankReportContractNumber(bankReport) {
      bankReport.contract_number_edit = false;

      const validContractNumberPattern = /^([A-Z]{1,2})\d{1,2}-[\d\w]{5,6}$/gi;
      const isValidContractNumber =
        validContractNumberPattern.exec(bankReport.contract_number) != null;

      if (isValidContractNumber && this.activetab === 2) {
        const bankReportWrongIndex = this.bankReportsWrong.indexOf(bankReport);
        const bankReportCorrectIndex = this.getBankReportIndex(
          this.bankReportsCorrect,
          bankReport
        );

        if (bankReportCorrectIndex > -1) {
          this.bankReportsCorrect.splice(bankReportCorrectIndex, 1);
        }

        bankReport.status = {
          title: this.__('Parsed Success'),
          success: true,
        };

        this.bankReportsCorrect.push(bankReport);

        if (bankReportWrongIndex > -1) {
          this.bankReportsWrong.splice(bankReportWrongIndex, 1);
        }
      }

      this.saveHCDisabled = this.bankReportsCorrect.length === 0;
    },

    handleFileUpload() {
      this.bankReportsCorrect = [];
      this.bankReportsWrong = [];
      this.tableVisibility = false;
      this.file = this.$refs.file.files[0];
      this.fileName = this.file.name;
      this.saveHCDisabled = true;
      this.downloadDisabled = true;
      this.loading = true;
      this.parse();
    },

    changeTab() {
      if (this.activetab === 1) {
        this.bankReports = this.bankReportsCorrect;

        this.saveHCDisabled = !this.bankReportsCorrect.length;
      } else if (this.activetab === 2) {
        this.bankReports = this.bankReportsWrong;
        this.saveHCDisabled = true;
      }
    },

    download() {
      this.loading = true;
      this.downloadDisabled = true;

      axios
        .post(
          '/nova-vendor/bank-report/download',
          {
            bankReports: this.bankReports,
            fileName: this.fileName,
          },
          {
            headers: {
              'Content-Type': 'application/json',
            },
          }
        )
        .then(response => {
          var fileLink = document.createElement('a');
          fileLink.href = response.data.download;
          fileLink.setAttribute('download', response.data.name);
          document.body.appendChild(fileLink);
          fileLink.click();
        })
        .catch(error => {
          this.$toasted.show(error.response.data.message, { type: 'error' });
        })
        .finally(() => {
          this.loading = false;
          this.downloadDisabled = false;
        });
    },

    saveHC() {
      this.loading = true;

      if (this.bankReportsCorrect.length === 0) {
        this.$toasted.show(this.__('Something went wrong'), { type: 'error' });
        return;
      }
      this.saveHCDisabled = true;

      axios
        .post(
          '/nova-vendor/bank-report/savehc',
          {
            bankReports: this.bankReportsCorrect,
            bankName: this.selected,
          },
          {
            headers: {
              'Content-Type': 'application/json',
            },
          }
        )
        .then(response => {
          this.loading = false;
          this.saveHCDisabled = false;

          this.bankReportsCorrect.forEach(el => {
            el.status = {
              title: this.__('Saved Success'),
              success: true,
            };
          });
          if (response.data.success) {
            this.$toasted.show(this.__('The action ran successfully!'), {
              type: 'success',
            });
          } else {
            this.$toasted.show(this.__('Unable to save in HC'), {
              type: 'error',
            });

            response.data.failed_bank_reports.forEach(el => {
              const bankWrongReportIndex = this.getBankReportIndex(
                this.bankReportsWrong,
                el
              );
              const bankReportCorrectIndex = this.getBankReportIndex(
                this.bankReportsCorrect,
                el
              );

              // Removing old wrong report if they are the same
              if (bankWrongReportIndex > -1) {
                this.bankReportsWrong.splice(bankWrongReportIndex, 1);
              }

              // Removing old correct report if exists
              if (bankReportCorrectIndex > -1) {
                this.bankReportsCorrect.splice(bankReportCorrectIndex, 1);
              }

              el.status = {
                title: this.__('Saved Fail'),
                success: false,
              };

              el.contract_number_edit = false;
              this.bankReportsWrong.unshift(el);
            });

            this.saveHCDisabled = true;
            this.bankReports = this.bankReportsWrong;
            this.activetab = 2;
          }
        })
        .catch(error => {
          this.loading = false;
          this.saveHCDisabled = false;
          this.$toasted.show(error.response.data.message, { type: 'error' });
        });
    },

    parse() {
      this.loading = true;
      this.bankReportsCorrect = [];
      this.bankReportsWrong = [];
      const formData = new FormData();
      formData.append('file', this.file);

      axios
        .post('/nova-vendor/bank-report/parse', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .then(response => {
          this.loading = false;

          response.data.forEach(bankReport => {
            bankReport.contract_number_edit = false;

            if (
              bankReport.contract_number &&
              bankReport.date &&
              bankReport.amount
            ) {
              bankReport.status = {
                title: this.__('Parsed Success'),
                success: true,
              };

              this.bankReportsCorrect.push(bankReport);
            } else {
              bankReport.status = {
                title: this.__('Parsed Fail'),
                success: false,
              };
              this.bankReportsWrong.push(bankReport);
            }
          });

          this.bankReports = this.bankReportsCorrect;
          this.saveHCDisabled = this.bankReportsCorrect.length === 0;
          const hasError =
            this.bankReportsCorrect.length === 0 &&
            this.bankReportsWrong.length === 0;
          this.tableVisibility = !hasError;
          this.downloadDisabled = hasError;

          if (hasError) {
            this.$toasted.show(this.__('Unable to parse'), { type: 'error' });
          }

          this.activetab = 1;
        })
        .catch(error => {
          this.loading = false;
          this.$toasted.show(error.response.data.message, { type: 'error' });
        });
    },

    getBankReportIndex(bankReports, bankReport) {
      return bankReports.findIndex(
        i =>
          i.contract_number === bankReport.contract_number &&
          i.date === bankReport.date &&
          i.amount === bankReport.amount
      );
    },
  },
};
</script>
