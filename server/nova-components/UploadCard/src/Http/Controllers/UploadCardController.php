<?php

namespace Globalcredit\UploadCard\Http\Controllers;

use App\Exceptions\VehicleExcelValidationException;
use Globalcredit\UploadCard\Services\UploadCardServiceFactory;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Log;
use Laravel\Nova\Http\Requests\NovaRequest;
use Throwable;

class UploadCardController extends Controller
{
    public function storeModels(NovaRequest $request)
    {
        try {
            $request->validate([
                'media' => 'required|mimes:xlsx',
            ]);

            $file = $request->media;
            $loan_type_id = $request->loan_type;

            $service = UploadCardServiceFactory::build($loan_type_id);
            $service->storeModels($file);

            return response()->json(true);
        } catch (VehicleExcelValidationException $e) {
            Log::warning('Upload Card , Store Models, VehicleExcelValidationException', ['message' => $e->getMessage()]);

            return response()->json([
                'message' => 'Validation failed',
                'errors' => ['formatting' => [$e->getMessage()]],
            ], 422);
        } catch (Throwable $e) {
            Log::error('Upload Card , Store Models, Exception', ['message' => $e->getMessage()]);

            throw $e;
        }
    }
}
