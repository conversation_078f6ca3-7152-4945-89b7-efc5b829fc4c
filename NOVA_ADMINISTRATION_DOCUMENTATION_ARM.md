# Global Credit Nova Ադմինիստրացիայի Փաստաթղթավորում

## Ընդհանուր ակնարկ
Այս փաստաթուղթը տրամադրում է համապարփակ փաստաթղթավորում Global Credit համակարգում իրականացված բոլոր գոյություն ունեցող Nova ադմինիստրացիայի գործառույթների համար: Nova-ն ծառայում է որպես վարչական գործառույթների հիմնական միջերես՝ վարկերի, օգտատերերի, գործարքների և տարբեր բիզնես գործառույթների կառավարման համար:

## Բովանդակություն
1. [Հիմնական Nova Ռեսուրսներ](#հիմնական-nova-ռեսուրսներ)
2. [Հատուկ Nova Գործիքներ](#հատուկ-nova-գործիքներ)
3. [Վահանակ և Մետրիկաներ](#վահանակ-և-մետրիկաներ)
4. [Գործողություններ և Օպերացիաներ](#գործողություններ-և-օպերացիաներ)
5. [Ֆիլտրեր և Որոնում](#ֆիլտրեր-և-որոնում)
6. [Թույլտվություններ և Լիազորում](#թույլտվություններ-և-լիազորում)
7. [Հատուկ Բաղադրիչներ](#հատուկ-բաղադրիչներ)

## Հիմնական Nova Ռեսուրսներ

### 1. Վարկային Կառավարման Ռեսուրսներ

#### Վարկ Ռեսուրս (`App\Nova\Loan`)
**Նպատակ**: Վարկային կառավարման հիմնական միջերես
**Մոդել**: `App\Models\Loan`
**Նավիգացիայի Թույլտվություն**: `view-full-history` կամ `view-loans`

**Հիմնական Դաշտեր**:
- Պայմանագրի Համար (որոնելի)
- Քաղաքացու Տեղեկություններ (անուն, ազգանուն, անձնագիր)
- Վարկի Տեսակ (OCL, OVL, OASL, OIDL, PL, OIQL, և այլն)
- Վարկի Գումար և Արժույթ
- Տոկոսադրույք և Պայմաններ
- Կարգավիճակ և Ամսաթվեր
- Լրացուցիչ Տեղեկություններ
- Փաստաթղթերի Առաքման Եղանակ (էլ. փոստ, անձամբ, փոստ)

**Կապեր**:
- `HasMany`: Վարկային Փաստաթղթեր, Գործարքային Փաստաթղթեր
- `BelongsTo`: Քաղաքացի, Վարկի Տեսակ

**Ֆիլտրեր**:
- Վարկի Ստորագրման Ամսաթվի Ֆիլտր
- Վարկի Տեսակի Ֆիլտր
- Լրացուցիչ Վարկի Ֆիլտր

#### ArpiSolarLoan Ռեսուրս (`App\Nova\ArpiSolarLoan`)
**Նպատակ**: Արևային վահանակների վարկային կառավարում
**Մոդել**: `App\Models\Loan` (ֆիլտրված արևային վարկերի համար)
**Նավիգացիայի Թույլտվություն**: `view-oasl-loan`

**Հիմնական Դաշտեր**:
- Պայմանագրի Համար
- Քաղաքացու Տեղեկություններ (անձնագիր, անուններ, հեռախոս)
- Արևային Վահանակի Մանրամասներ
- Տեղադրման Հասցե
- KFW Տեղեկություններ
- Գործակալի Նշանակում
- Կարգավիճակ և Ժամկետ

**Գործողություններ**:
- Փաթեթի Ստեղծում
- Փաստաթղթերի Ներբեռնում
- KFW Վարկերի Ներբեռնում
- Արևային Վահանակի Հասցեի Խմբագրում

**Ֆիլտրեր**:
- Վարկի Ստորագրման Ամսաթվի Ֆիլտր
- OASL Գործակալի Ֆիլտր
- OASL Կարգավիճակի Ֆիլտր

#### VehicleLoan Ռեսուրս (`App\Nova\VehicleLoan`)
**Նպատակ**: Տրանսպորտային միջոցների վարկային կառավարում
**Մոդել**: `App\Models\Loan` (ֆիլտրված տրանսպորտային վարկերի համար)
**Նավիգացիայի Թույլտվություն**: `view-ovl-loan`

**Հիմնական Դաշտեր**:
- Պայմանագրի Համար
- Քաղաքացու Տեղեկություններ
- Տրանսպորտային Միջոցի Մանրամասներ (համար, VIN, մոդել, մարկա)
- Գրավի Տեղեկություններ
- Անվտանգության Մանրամասներ
- Գործակալի Նշանակում

**Ֆիլտրեր**:
- Վարկի Կարգավիճակի Ֆիլտր
- Ուղղորդողի Աղբյուրի Ֆիլտր
- Տրանսպորտային Միջոցի Ստուգման Ամսաթվի Ֆիլտր

#### MortgageLoan Ռեսուրս (`App\Nova\MortgageLoan`)
**Նպատակ**: Անշարժ գույքի գրավային վարկային կառավարում
**Նավիգացիայի Թույլտվություն**: `view-reml`

**Հիմնական Դաշտեր**:
- Ստանդարտ վարկային դաշտեր
- Գնահատող Ընկերության Տեղեկություններ
- Անշարժ Գույքի Մանրամասներ

#### WithdrawnLoan Ռեսուրս (`App\Nova\WithdrawnLoan`)
**Նպատակ**: Ավարտված/ստացված վարկերի կառավարում
**Նավիգացիայի Թույլտվություն**: `view-loan-history`

**Ֆիլտրեր**:
- Ստացման Ամսաթվի Ֆիլտր
- Ստացող Ընկերության Ֆիլտր
- Վարկի Տեսակի Ֆիլտր
- Լրացուցիչ Վարկի Ֆիլտր

#### WithdrawableLoan Ռեսուրս (`App\Nova\WithdrawableLoan`)
**Նպատակ**: Ստացման պատրաստ վարկեր
**Նավիգացիայի Թույլտվություն**: `view-cash`

### 2. Օգտատերերի Կառավարման Ռեսուրսներ

#### User Ռեսուրս (`App\Nova\User`)
**Նպատակ**: Համակարգի օգտատերերի կառավարում
**Մոդել**: `App\Models\User`
**Նավիգացիայի Թույլտվություն**: Ադմին, Upay Ադմին, Telcell Ադմին, կամ `view-oasl-users`

**Հիմնական Դաշտեր**:
- ID (դասակարգելի)
- Ավատար (Gravatar)
- Անուն, Ազգանուն
- Էլ. փոստ (եզակի, դասակարգելի)
- Գաղտնաբառ (միայն ձևերում)
- Ակտիվ Կարգավիճակ
- Հեռախոսի Համար
- Դերերի Նշանակումներ

**Կապեր**:
- `MorphToMany`: Դերեր և Թույլտվություններ

**Հարցման Սահմանափակում**:
- Ադմին: Բոլոր օգտատերերը
- Upay Ադմին: Միայն upay-cashier դերի օգտատերերը
- Telcell Ադմին: Միայն telcell-cashier դերի օգտատերերը
- OASL Թույլտվություն: Միայն oasl-agent դերի օգտատերերը

#### Role Ռեսուրս (`App\Nova\Role`)
**Նպատակ**: Թույլտվությունների դերերի կառավարում
**Ինտեգրացիա**: Nova Թույլտվությունների Գործիք

#### Permission Ռեսուրս (`App\Nova\Permission`)
**Նպատակ**: Թույլտվությունների կառավարում
**Ինտեգրացիա**: Nova Թույլտվությունների Գործիք

### 3. Գործարքային և Վճարային Ռեսուրսներ

#### Transaction Ռեսուրս (`App\Nova\Transaction`)
**Նպատակ**: BNPL գործարքների կառավարում
**Մոդել**: `App\Models\CreditLine\Transaction`
**Նավիգացիայի Թույլտվություն**: Ադմին կամ `view-bnpl-transactions`

**Հիմնական Դաշտեր**:
- Վաճառողի Տեսակ և Անուն
- Պայմանագրի Համար
- Գումար (ֆորմատավորված դրամով)
- Վաճառակետի Տեղեկություններ
- Գործարքի Ամսաթիվ
- Նկարագրություն, Անձնագիր, SSN, Հեռախոս (միայն մանրամասներում)

**Գործողություններ**:
- Գործարքների Արտահանում (ֆայլի անվան և ֆորմատի ընտրությամբ)
#### PurchaseRequest Ռեսուրս (`App\Nova\PurchaseRequest`)
**Նպատակ**: Վարկային գծի գնման հայտերի կառավարում
**Մոդել**: `App\Models\CreditLine\PurchaseRequest`
**Նավիգացիայի Թույլտվություն**: `view-purchase-request` կամ `view-all-purchase-requests`

**Հիմնական Դաշտեր**:
- Վաճառողի Ընտրություն
- Գումար
- Նկարագրություն
- Կարգավիճակ
- QR Կոդի Ստեղծում
- Ստեղծման Ամսաթիվ

**Գործողություններ**:
- Գնման Հայտի Ուղարկում SMS-ով

**Ֆիլտրեր**:
- Գնման Հայտի Ամսաթվի Ֆիլտր
- Գնման Հայտի Կարգավիճակի Ֆիլտր
- Վաճառողի Ֆիլտր

### 4. Վաճառակետերի և QR Կոդերի Ռեսուրսներ

#### Merchant Ռեսուրս (`App\Nova\Merchant`)
**Նպատակ**: Վաճառակետերի կառավարում
**Մոդել**: `App\Models\Merchant`
**Նավիգացիայի Թույլտվություն**: `view-merchant` կամ Ադմին

**Հիմնական Դաշտեր**:
- Վաճառողի Կապ
- Վաճառակետի Անուն և Վերնագիր
- Հասցե
- Հարկային Տեղեկություններ (Armsoft Mi Tax Payer ID, Հաշվի Համար)
- Կոնտակտային Տեղեկություններ (հեռախոսներ)
- Տեսակ (Հիվանդանոց, Տրանսպորտային Ներմուծում)

**Կապեր**:
- `BelongsTo`: Վաճառող
- `BelongsToMany`: Գործակալներ

#### QrCode Ռեսուրս (`App\Nova\QrCode`)
**Նպատակ**: Վճարումների QR կոդերի կառավարում
**Մոդել**: `App\Models\QrCode`
**Նավիգացիայի Թույլտվություն**: `view-qr-codes`

**Հիմնական Դաշտեր**:
- QR Տեսակ (Զեղչ, Սեփականատեր-հիմնված)
- Վարկի Տեսակի Կապ
- Զեղչի Տոկոս (պայմանական)
- QR Կոդի Նկար և Հղում
- Սեփականատիրոջ Տեղեկություններ (պայմանական)

**Կապեր**:
- `HasOne`: QR Սեփականատեր

### 5. Փաստաթղթերի Կառավարման Ռեսուրսներ

#### LoanDocument Ռեսուրս (`App\Nova\LoanDocument`)
**Նպատակ**: Վարկային փաստաթղթերի կառավարում
**Մոդել**: `App\Models\LoanDocument`

#### TransactionDocument Ռեսուրս (`App\Nova\TransactionDocument`)
**Նպատակ**: Գործարքային փաստաթղթերի կառավարում
**Մոդել**: `App\Models\TransactionDocument`

#### LoanDocumentHistory Ռեսուրս (`App\Nova\LoanDocumentHistory`)
**Նպատակ**: Փաստաթղթերի պատմության հետևում
**Մոդել**: `App\Models\LoanDocumentHistory`

### 6. Մասնագիտացված Վարկային Ռեսուրսներ

#### COVID19 Աջակցության Ռեսուրսներ
- **SupportLoan** (`App\Nova\COVID19\SupportLoan`): COVID-19 աջակցության վարկերի կառավարում
- **SupportLoanDocument** (`App\Nova\COVID19\SupportLoanDocument`): Կապակցված փաստաթղթերի կառավարում
- **SupportBlacklist** (`App\Nova\COVID19\SupportBlacklist`): COVID-19 հատուկ սև ցուցակ

#### Կայունացման Ռեսուրսներ
- **StabilizationLoan** (`App\Nova\Stabilization\StabilizationLoan`): Տնտեսական կայունացման վարկեր

#### Տրանսպորտ-Հատուկ Ռեսուրսներ
- **VehicleImportLoan** (`App\Nova\VehicleImportLoan`): Տրանսպորտային ներմուծման ֆինանսավորում
- **VehicleClosedLoan** (`App\Nova\VehicleClosedLoan`): Ավարտված տրանսպորտային վարկեր
- **VehicleModel** (`App\Nova\VehicleModel`): Տրանսպորտային մոդելների կառավարում
- **VehiclePrice** (`App\Nova\VehiclePrice`): Տրանսպորտային գնային տեղեկություններ

### 7. Բիզնես Տրամաբանության Ռեսուրսներ

#### Blacklist Ռեսուրս (`App\Nova\Blacklist`)
**Նպատակ**: Հաճախորդների սև ցուցակի կառավարում
**Մոդել**: `App\Models\Blacklist`

#### BlacklistReason Ռեսուրս (`App\Nova\BlacklistReason`)
**Նպատակ**: Սև ցուցակի պատճառների կատեգորիզացիա
**Մոդել**: `App\Models\BlacklistReason`

#### CreditLine Ռեսուրս (`App\Nova\CreditLine`)
**Նպատակ**: Վարկային գծի կառավարում
**Մոդել**: `App\Models\CreditLine`

#### CreditOffer Ռեսուրս (`App\Nova\CreditOffer`)
**Նպատակ**: Վարկային առաջարկների կառավարում
**Մոդել**: `App\Models\CreditOffer`

#### ReferralCode Ռեսուրս (`App\Nova\ReferralCode`)
**Նպատակ**: Ուղղորդման կոդերի համակարգի կառավարում
**Մոդել**: `App\Models\ReferralCode`

#### ReferralCodeRepayment Ռեսուրս (`App\Nova\ReferralCodeRepayment`)
**Նպատակ**: Ուղղորդման-հիմնված մարման հետևում
**Մոդել**: `App\Models\ReferralCodeRepayment`

### 8. Կոնֆիգուրացիայի Ռեսուրսներ

#### Rule Ռեսուրս (`App\Nova\Rule`)
**Նպատակ**: Բիզնես կանոնների կառավարում
**Մոդել**: `App\Models\Rule`

#### RuleGroup Ռեսուրս (`App\Nova\RuleGroup`)
**Նպատակ**: Կանոնների խմբավորում և կազմակերպում
**Մոդել**: `App\Models\RuleGroup`

#### Package Ռեսուրս (`App\Nova\Package`)
**Նպատակ**: Փաստաթղթային փաթեթների կառավարում
**Մոդել**: `App\Models\Package`

#### SkylineConfig Ռեսուրս (`App\Nova\SkylineConfig`)
**Նպատակ**: Համակարգի կոնֆիգուրացիայի կառավարում
**Մոդել**: `App\Models\SkylineConfig`

### 9. Աջակցության և Ժամանակացույցի Ռեսուրսներ

#### Աջակցության Ժամանակացույցի Ռեսուրսներ
- **SupportWorkSchedule** (`App\Nova\SupportWorkSchedule`): Աշխատանքային ժամանակացույցի կառավարում
- **SupportWorkingHour** (`App\Nova\SupportWorkingHour`): Աշխատանքային ժամերի կոնֆիգուրացիա
- **SupportHoliday** (`App\Nova\SupportHoliday`): Տոների կառավարում
- **SupportScheduleException** (`App\Nova\SupportScheduleException`): Ժամանակացույցի բացառություններ

#### Profile Ռեսուրս (`App\Nova\Profile`)
**Նպատակ**: Օգտատերերի պրոֆիլների կառավարում
**Մոդել**: `App\Models\Pallaton\Profile`

### 10. Մեդիա և Մարքեթինգի Ռեսուրսներ

#### MarketingMedia Ռեսուրս (`App\Nova\MarketingMedia`)
**Նպատակ**: Մարքեթինգային նյութերի կառավարում
**Մոդել**: `App\Models\MarketingMedia`

#### StoryMedia Ռեսուրս (`App\Nova\StoryMedia`)
**Նպատակ**: Պատմության բովանդակության կառավարում
**Մոդել**: `App\Models\StoryMedia`

### 11. Լրացուցիչ Բիզնես Ռեսուրսներ

#### Vendor Ռեսուրս (`App\Nova\Vendor`)
**Նպատակ**: Վաճառողների/մատակարարների կառավարում
**Մոդել**: `App\Models\Vendor`
**Նավիգացիայի Թույլտվություն**: Բիզնես պահանջների հիման վրա

#### CitizenLock Ռեսուրս (`App\Nova\CitizenLock`)
**Նպատակ**: Քաղաքացիների հաշիվների արգելափակման կառավարում
**Մոդել**: `App\Models\CitizenLock`
**Նավիգացիայի Թույլտվություն**: Ադմին մուտք

#### LoanExplorer Ռեսուրս (`App\Nova\LoanExplorer`)
**Նպատակ**: Վարկերի առաջադեմ հետազոտություն և վերլուծություն
**Մոդել**: `App\Models\Loan`
**Նավիգացիայի Թույլտվություն**: Առաջադեմ օգտատիրոջ թույլտվություններ

#### LoanHistory Ռեսուրս (`App\Nova\LoanHistory`)
**Նպատակ**: Վարկային պատմության համապարփակ հետևում
**Մոդել**: `App\Models\LoanHistory`
**Նավիգացիայի Թույլտվություն**: Պատմության դիտման թույլտվություններ

#### LoanUpdateHistory Ռեսուրս (`App\Nova\LoanUpdateHistory`)
**Նպատակ**: Վարկային փոփոխությունների և թարմացումների հետևում
**Մոդել**: `App\Models\LoanUpdateHistory`
**Նավիգացիայի Թույլտվություն**: Ադմին կամ աուդիտի թույլտվություններ

#### QrLoan Ռեսուրս (`App\Nova\QrLoan`)
**Նպատակ**: QR կոդ-հիմնված վարկային կառավարում
**Մոդել**: `App\Models\Loan` (ֆիլտրված QR վարկերի համար)
**Նավիգացիայի Թույլտվություն**: QR վարկային մուտքի թույլտվություններ

#### QrOwner Ռեսուրս (`App\Nova\QrOwner`)
**Նպատակ**: QR կոդի սեփականության կառավարում
**Մոդել**: `App\Models\QrOwner`
**Նավիգացիայի Թույլտվություն**: QR կառավարման թույլտվություններ

#### TelcellLoan Ռեսուրս (`App\Nova\TelcellLoan`)
**Նպատակ**: Telcell-հատուկ վարկային կառավարում
**Մոդել**: `App\Models\Loan` (ֆիլտրված Telcell վարկերի համար)
**Նավիգացիայի Թույլտվություն**: Telcell գործառույթների թույլտվություններ

### 12. Անշարժ Գույք և Գրավային Ռեսուրսներ

#### RealEstateSeller Ռեսուրս (`App\Nova\RealEstateSeller`)
**Նպատակ**: Անշարժ գույքի վաճառողների տեղեկությունների կառավարում
**Մոդել**: `App\Models\RealEstateSeller`
**Նավիգացիայի Թույլտվություն**: Անշարժ գույքի գործառույթներ

#### MortgageLoanApplication Ռեսուրս (`App\Nova\MortgageLoanApplication`)
**Նպատակ**: Գրավային վարկային հայտերի մշակում
**Մոդել**: `App\Models\MortgageLoanApplication`
**Նավիգացիայի Թույլտվություն**: `view-reml-loan-applications`

### 13. Վաճառակետային և Գործակալական Ռեսուրսներ

#### MerchantAgent Ռեսուրս (`App\Nova\MerchantAgent`)
**Նպատակ**: Վաճառակետ-գործակալ հարաբերությունների կառավարում
**Մոդել**: `App\Models\MerchantAgent`
**Նավիգացիայի Թույլտվություն**: Վաճառակետային կառավարման թույլտվություններ

#### MerchantBlacklist Ռեսուրս (`App\Nova\MerchantBlacklist`)
**Նպատակ**: Վաճառակետային սև ցուցակի կառավարում
**Մոդել**: `App\Models\MerchantBlacklist`
**Նավիգացիայի Թույլտվություն**: Վաճառակետային ադմինիստրացիա

### 14. Համակարգային Կոնֆիգուրացիայի Ռեսուրսներ

#### AffectedRule Ռեսուրս (`App\Nova\AffectedRule`)
**Նպատակ**: Կանոնների ազդեցության հետևում և կառավարում
**Մոդել**: `App\Models\AffectedRule`
**Նավիգացիայի Թույլտվություն**: Կանոնների կառավարման թույլտվություններ

#### PipeType Ռեսուրս (`App\Nova\PipeType`)
**Նպատակ**: Գծանցքի տեսակի կոնֆիգուրացիա
**Մոդել**: `App\Models\PipeType`
**Նավիգացիայի Թույլտվություն**: Համակարգային կոնֆիգուրացիա

#### VeloxProductCategory Ռեսուրս (`App\Nova\VeloxProductCategory`)
**Նպատակ**: Velox արտադրանքի կատեգորիզացիա
**Մոդել**: `App\Models\VeloxProductCategory`
**Նավիգացիայի Թույլտվություն**: Արտադրանքի կառավարում

### 15. Քաղաքացիների և Հաճախորդների Ռեսուրսներ

#### Citizen Ռեսուրս (`App\Nova\Citizen`)
**Նպատակ**: Հաճախորդների/քաղաքացիների տեղեկությունների կառավարում
**Մոդել**: `App\Models\Citizen`
**Նավիգացիայի Թույլտվություն**: Հաճախորդական տվյալների մուտքի թույլտվություններ

**Հիմնական Դաշտեր**:
- Անձնական Տեղեկություններ (Անուն, Ազգանուն, Անձնագիր)
- Կապի Մանրամասներ (Հեռախոս, Էլ. փոստ, Հասցե)
- Նույնականացման Համարներ (SSN, Հարկային ID)
- Դեմոգրաֆիկ Տեղեկություններ
- Փաստաթղթերի Կարգավիճակ

#### CitizenDocument Ռեսուրս (`App\Nova\CitizenDocument`)
**Նպատակ**: Հաճախորդների փաստաթղթերի կառավարում
**Մոդել**: `App\Models\CitizenDocument`
**Նավիգացիայի Թույլտվություն**: Փաստաթղթային մուտքի թույլտվություններ

#### CitizenPhone Ռեսուրս (`App\Nova\CitizenPhone`)
**Նպատակ**: Հաճախորդների հեռախոսի համարների կառավարում
**Մոդել**: `App\Models\CitizenPhone`
**Նավիգացիայի Թույլտվություն**: Կապի կառավարման թույլտվություններ

### 16. Տրանսպորտային և Փոխադրման Ռեսուրսներ

#### Vehicle Ռեսուրս (`App\Nova\Vehicle`)
**Նպատակ**: Տրանսպորտային միջոցների տեղեկությունների կառավարում
**Մոդել**: `App\Models\Vehicle`
**Նավիգացիայի Թույլտվություն**: Տրանսպորտային գործառույթների թույլտվություններ

**Հիմնական Դաշտեր**:
- Տրանսպորտային Նույնականացում (VIN, Համարանիշ)
- Մարկա, Մոդել, Տարի
- Տեխնիկական Բնութագրեր
- Ապահովագրական Տեղեկություններ
- Գրանցման Մանրամասներ

#### VehicleBrand Ռեսուրս (`App\Nova\VehicleBrand`)
**Նպատակ**: Տրանսպորտային մարկաների կառավարում
**Մոդել**: `App\Models\VehicleBrand`
**Նավիգացիայի Թույլտվություն**: Տրանսպորտային կոնֆիգուրացիա

#### VehicleType Ռեսուրս (`App\Nova\VehicleType`)
**Նպատակ**: Տրանսպորտային տեսակների կատեգորիզացիա
**Մոդել**: `App\Models\VehicleType`
**Նավիգացիայի Թույլտվություն**: Տրանսպորտային կոնֆիգուրացիա

#### VehicleImportCompany Ռեսուրս (`App\Nova\VehicleImportCompany`)
**Նպատակ**: Տրանսպորտային ներմուծող ընկերությունների կառավարում
**Մոդել**: `App\Models\VehicleImportCompany`
**Նավիգացիայի Թույլտվություն**: Ներմուծման գործառույթներ

### 17. Գնահատման և Գնահատական Ռեսուրսներ

#### EvaluationCompany Ռեսուրս (`App\Nova\EvaluationCompany`)
**Նպատակ**: Գույքի գնահատող ընկերությունների կառավարում
**Մոդել**: `App\Models\EvaluationCompany`
**Նավիգացիայի Թույլտվություն**: Գնահատման գործառույթներ

**Հիմնական Դաշտեր**:
- Ընկերության Տեղեկություններ
- Կապի Մանրամասներ
- Վկայագրման Կարգավիճակ
- Ծառայության Տարածքներ
- Գնահատման Տեսակներ

#### EvaluationCompanyUser Ռեսուրս (`App\Nova\EvaluationCompanyUser`)
**Նպատակ**: Գնահատող ընկերության օգտատերերի կառավարում
**Մոդել**: `App\Models\EvaluationCompanyUser`
**Նավիգացիայի Թույլտվություն**: Գնահատող օգտատերերի կառավարում

### 18. Վարկային Տեսակի և Կատեգորիայի Ռեսուրսներ

#### LoanType Ռեսուրս (`App\Nova\LoanType`)
**Նպատակ**: Վարկային արտադրանքի տեսակների կառավարում
**Մոդել**: `App\Models\LoanType`
**Նավիգացիայի Թույլտվություն**: Արտադրանքի կոնֆիգուրացիա

**Հիմնական Դաշտեր**:
- Վարկի Տեսակի Կոդ (OCL, OVL, OASL, և այլն)
- Նկարագրություն և Պայմաններ
- Տոկոսադրույքներ
- Իրավասության Չափանիշներ
- Փաստաթղթավորման Պահանջներ

#### LoanCategory Ռեսուրս (`App\Nova\LoanCategory`)
**Նպատակ**: Վարկային կատեգորիզացիայի կառավարում
**Մոդել**: `App\Models\LoanCategory`
**Նավիգացիայի Թույլտվություն**: Արտադրանքի կոնֆիգուրացիա

### 19. Ընկերությունների և Կազմակերպությունների Ռեսուրսներ

#### Company Ռեսուրս (`App\Nova\Company`)
**Նպատակ**: Ընկերությունների/կազմակերպությունների կառավարում
**Մոդել**: `App\Models\Company`
**Նավիգացիայի Թույլտվություն**: Ընկերության կառավարման թույլտվություններ

**Հիմնական Դաշտեր**:
- Ընկերության Տեղեկություններ
- Գրանցման Մանրամասներ
- Կապի Տեղեկություններ
- Բիզնես Տեսակ
- Կարգավիճակ և Հաստատում

#### CompanyUser Ռեսուրս (`App\Nova\CompanyUser`)
**Նպատակ**: Ընկերության օգտատիրոջ հարաբերությունների կառավարում
**Մոդել**: `App\Models\CompanyUser`
**Նավիգացիայի Թույլտվություն**: Ընկերության օգտատերերի կառավարում

### 20. Լրացուցիչ Մասնագիտացված Ռեսուրսներ

#### Agent Ռեսուրս (`App\Nova\Agent`)
**Նպատակ**: Գործակալների կառավարում և նշանակում
**Մոդել**: `App\Models\Agent`
**Նավիգացիայի Թույլտվություն**: Գործակալների կառավարման թույլտվություններ

#### Bank Ռեսուրս (`App\Nova\Bank`)
**Նպատակ**: Բանկային հաստատությունների կառավարում
**Մոդել**: `App\Models\Bank`
**Նավիգացիայի Թույլտվություն**: Բանկային գործառույթներ

#### BankBranch Ռեսուրս (`App\Nova\BankBranch`)
**Նպատակ**: Բանկային մասնաճյուղերի տեղեկությունների կառավարում
**Մոդել**: `App\Models\BankBranch`
**Նավիգացիայի Թույլտվություն**: Բանկային գործառույթներ

#### Collateral Ռեսուրս (`App\Nova\Collateral`)
**Նպատակ**: Գրավային ակտիվների կառավարում
**Մոդել**: `App\Models\Collateral`
**Նավիգացիայի Թույլտվություն**: Գրավային գործառույթներ

#### CollateralType Ռեսուրս (`App\Nova\CollateralType`)
**Նպատակ**: Գրավային տեսակների կատեգորիզացիա
**Մոդել**: `App\Models\CollateralType`
**Նավիգացիայի Թույլտվություն**: Գրավային կոնֆիգուրացիա

#### Currency Ռեսուրս (`App\Nova\Currency`)
**Նպատակ**: Արժույթների կառավարում և փոխարժեքներ
**Մոդել**: `App\Models\Currency`
**Նավիգացիայի Թույլտվություն**: Ֆինանսական կոնֆիգուրացիա

#### District Ռեսուրս (`App\Nova\District`)
**Նպատակ**: Աշխարհագրական շրջանների կառավարում
**Մոդել**: `App\Models\District`
**Նավիգացիայի Թույլտվություն**: Աշխարհագրական տվյալների կառավարում

#### EducationLevel Ռեսուրս (`App\Nova\EducationLevel`)
**Նպատակ**: Կրթական մակարդակի կատեգորիզացիա
**Մոդել**: `App\Models\EducationLevel`
**Նավիգացիայի Թույլտվություն**: Հաճախորդական տվյալների կոնֆիգուրացիա

#### FamilyStatus Ռեսուրս (`App\Nova\FamilyStatus`)
**Նպատակ**: Ընտանեկան կարգավիճակի կատեգորիզացիա
**Մոդել**: `App\Models\FamilyStatus`
**Նավիգացիայի Թույլտվություն**: Հաճախորդական տվյալների կոնֆիգուրացիա

#### Gender Ռեսուրս (`App\Nova\Gender`)
**Նպատակ**: Սեռի կատեգորիզացիա
**Մոդել**: `App\Models\Gender`
**Նավիգացիայի Թույլտվություն**: Հաճախորդական տվյալների կոնֆիգուրացիա

#### IncomeSource Ռեսուրս (`App\Nova\IncomeSource`)
**Նպատակ**: Եկամտի աղբյուրի կատեգորիզացիա
**Մոդել**: `App\Models\IncomeSource`
**Նավիգացիայի Թույլտվություն**: Ֆինանսական գնահատում

#### JobTitle Ռեսուրս (`App\Nova\JobTitle`)
**Նպատակ**: Աշխատանքային պաշտոնների կառավարում
**Մոդել**: `App\Models\JobTitle`
**Նավիգացիայի Թույլտվություն**: Զբաղվածության տվյալներ

#### LoanPurpose Ռեսուրս (`App\Nova\LoanPurpose`)
**Նպատակ**: Վարկի նպատակի կատեգորիզացիա
**Մոդել**: `App\Models\LoanPurpose`
**Նավիգացիայի Թույլտվություն**: Վարկային կոնֆիգուրացիա

#### MaritalStatus Ռեսուրս (`App\Nova\MaritalStatus`)
**Նպատակ**: Ամուսնական կարգավիճակի կատեգորիզացիա
**Մոդել**: `App\Models\MaritalStatus`
**Նավիգացիայի Թույլտվություն**: Հաճախորդական տվյալների կոնֆիգուրացիա

#### Nationality Ռեսուրս (`App\Nova\Nationality`)
**Նպատակ**: Ազգությունների կառավարում
**Մոդել**: `App\Models\Nationality`
**Նավիգացիայի Թույլտվություն**: Հաճախորդական տվյալների կոնֆիգուրացիա

#### Occupation Ռեսուրս (`App\Nova\Occupation`)
**Նպատակ**: Մասնագիտությունների կատեգորիզացիա
**Մոդել**: `App\Models\Occupation`
**Նավիգացիայի Թույլտվություն**: Զբաղվածության տվյալներ

#### Region Ռեսուրս (`App\Nova\Region`)
**Նպատակ**: Աշխարհագրական մարզերի կառավարում
**Մոդել**: `App\Models\Region`
**Նավիգացիայի Թույլտվություն**: Աշխարհագրական տվյալների կառավարում

#### Relationship Ռեսուրս (`App\Nova\Relationship`)
**Նպատակ**: Հարաբերությունների տեսակների կառավարում
**Մոդել**: `App\Models\Relationship`
**Նավիգացիայի Թույլտվություն**: Հաճախորդական հարաբերությունների տվյալներ

#### ResidenceType Ռեսուրս (`App\Nova\ResidenceType`)
**Նպատակ**: Բնակության տեսակի կատեգորիզացիա
**Մոդել**: `App\Models\ResidenceType`
**Նավիգացիայի Թույլտվություն**: Հաճախորդական տվյալների կոնֆիգուրացիա

#### SocialStatus Ռեսուրս (`App\Nova\SocialStatus`)
**Նպատակ**: Սոցիալական կարգավիճակի կատեգորիզացիա
**Մոդել**: `App\Models\SocialStatus`
**Նավիգացիայի Թույլտվություն**: Հաճախորդական տվյալների կոնֆիգուրացիա
## Հատուկ Nova Գործիքներ

### 1. Թույլտվությունների Կառավարման Գործիք
**Գործիք**: `Vyuldashev\NovaPermission\NovaPermissionTool`
**Նպատակ**: Դերերի և թույլտվությունների կառավարում
**Մուտք**: Միայն ադմին դեր
**Գործառույթներ**:
- Դերերի ստեղծում և կառավարում
- Թույլտվությունների նշանակում
- Օգտատերերի դերերի նշանակում

### 2. Շեղված Ժամանակացույցների Գործիք
**Գործիք**: `Globalcredit\DeviatedSchedules\DeviatedSchedules`
**Նպատակ**: Ժամանակացույցի ուղղումների կառավարում
**Մուտք**: `correct-devated-schedule` թույլտվություն
**Գործառույթներ**:
- Ժամանակացույցի շեղումների հայտնաբերում
- Ուղղման աշխատանքային հոսքեր

### 3. Բանկային Հաշվետվության Գործիք
**Գործիք**: `Globalcredit\BankReport\BankReport`
**Նպատակ**: Բանկային հաշվետվությունների ստեղծում և կառավարում
**Մուտք**: `bank-report` թույլտվություն
**Գործառույթներ**:
- Հաշվետվությունների ստեղծում
- Տվյալների արտահանման հնարավորություններ

### 4. ACRA Մոնիտորինգի Գործիք
**Գործիք**: `Globalcredit\AcraMonitoring\AcraMonitoring`
**Նպատակ**: ACRA վարկային բյուրոյի մոնիտորինգ
**Մուտք**: `acra-monitoring` թույլտվություն
**Գործառույթներ**:
- Վարկային հաշվետվությունների մոնիտորինգ
- Տվյալների սինխրոնիզացիա

### 5. Կարգավորումների Գործիք
**Գործիք**: `Globalcredit\Settings\Settings`
**Նպատակ**: Համակարգի կարգավորումների կառավարում
**Մուտք**: Ադմին կամ `gc-customer-admin` դեր
**Գործառույթներ**:
- Համակարգի կոնֆիգուրացիա
- Պարամետրերի կառավարում

### 6. Վճարային Հաշիվ-Ապրանքագրի Գործիք
**Գործիք**: `Globalcredit\PaymentInvoice\PaymentInvoice`
**Նպատակ**: Վճարային հաշիվ-ապրանքագրերի կառավարում
**Մուտք**: `view-payment-invoice` թույլտվություն
**Գործառույթներ**:
- Հաշիվ-ապրանքագրերի ստեղծում
- Վճարումների հետևում

## Վահանակ և Մետրիկաներ

### Վահանակի Քարտեր
Nova վահանակը տրամադրում է արագ մուտք հիմնական բիզնես ոլորտներ հատուկ քարտերի միջոցով:

#### Տնային Քարտեր (`Globalcredit\Home\Home`)
- **Չվճարված Վարկեր**: Հղում դեպի ստացման պատրաստ վարկեր (`view-cash` թույլտվությամբ օգտատերերի համար)
- **Վճարված Վարկեր**: Հղում դեպի ստացված վարկեր (`view-loan-history` թույլտվությամբ օգտատերերի համար)
- **Բոլոր Վարկերը**: Հղում դեպի բոլոր վարկերը (ադմիններ և `view-loans` թույլտվությամբ օգտատերերի համար)
- **Տրանսպորտային Վարկեր**: Հղում դեպի տրանսպորտային վարկեր (`view-ovl-loan` թույլտվությամբ օգտատերերի համար)
- **Տրանսպորտային Ներմուծման Վարկեր**: Հղում դեպի տրանսպորտային ներմուծման վարկեր (`view-ovl-loan` թույլտվությամբ օգտատերերի համար)
- **Վարկային Գծեր**: Հղում դեպի վարկային գծեր (միայն ադմին)
- **Օգտատերեր**: Հղում դեպի օգտատերերի կառավարում (ադմին, upay ադմին, telcell ադմին, կամ `view-oasl-users` թույլտվություն)
- **Արևային Վարկեր**: Հղում դեպի արևային վարկեր (`view-oasl-loan` թույլտվությամբ օգտատերերի համար)
- **Կայունացման Վարկեր**: Հղում դեպի կայունացման վարկեր (`stabilization-loan` թույլտվությամբ օգտատերերի համար)
- **Գրավային Վարկային Հայտեր**: Հղում դեպի գրավային հայտեր (`view-reml-loan-applications` թույլտվությամբ օգտատերերի համար)
- **Գրավային Վարկեր**: Հղում դեպի գրավային վարկեր (`view-reml-loan-applications` թույլտվությամբ օգտատերերի համար)

#### Մետրիկաների Քարտեր
- **LoansPartition** (`App\Nova\Metrics\LoansPartition`): Վարկերի բաշխման մետրիկաներ (միայն ադմին)

## Գործողություններ և Օպերացիաներ

### Վարկային Գործողություններ

#### Փաստաթղթերի Կառավարման Գործողություններ
- **DownloadDocuments** (`App\Nova\Actions\DownloadDocuments`): Վարկային փաստաթղթերի ներբեռնում
- **SendDocuments** (`App\Nova\Actions\SendDocuments`): Փաստաթղթերի ուղարկում հաճախորդներին
- **RegenerateDocuments** (`App\Nova\Actions\RegenerateDocuments`): Վարկային փաստաթղթերի վերաստեղծում
- **RegenerateOVILDocuments** (`App\Nova\Actions\RegenerateOVILDocuments`): OVIL-հատուկ փաստաթղթերի վերաստեղծում
- **RegenerateWithdrawCheck** (`App\Nova\Actions\RegenerateWithdrawCheck`): Ստացման ստուգումների վերաստեղծում

#### Մասնագիտացված Վարկային Գործողություններ
- **DownloadKFWLoans** (`App\Nova\Actions\DownloadKFWLoans`): KFW վարկային տվյալների ներբեռնում
- **DownloadQrLoans** (`App\Nova\Actions\DownloadQrLoans`): QR-հիմնված վարկային տվյալների ներբեռնում
- **DownloadLoanApplications** (`App\Nova\Actions\DownloadLoanApplications`): Վարկային հայտերի ներբեռնում
- **DownloadLoans** (`App\Nova\Actions\DownloadLoans`): Վարկային տվյալների ներբեռնում

#### Վարկային Կառավարման Գործողություններ
- **EditEmailAndRegenerateDocuments** (`App\Nova\Actions\EditEmailAndRegenerateDocuments`): Էլ. փոստի թարմացում և փաստաթղթերի վերաստեղծում
- **EditSolarPanelAddress** (`App\Nova\Actions\EditSolarPanelAddress`): Արևային վահանակի տեղադրման հասցեի թարմացում
- **RegenerateOaslSchedule** (`App\Nova\Actions\RegenerateOaslSchedule`): OASL վճարային ժամանակացույցների վերաստեղծում
- **ReassignAgent** (`App\Nova\Actions\ReassignAgent`): Վարկերի վերանշանակում այլ գործակալների

### Փաթեթների Կառավարման Գործողություններ
- **CreatePackage** (`App\Nova\Actions\CreatePackage`): Փաստաթղթային փաթեթների ստեղծում
- **ChangePackageState** (`App\Nova\Actions\ChangePackageState`): Փաթեթի կարգավիճակի փոփոխում
- **DownloadPackageDocuments** (`App\Nova\Actions\DownloadPackageDocuments`): Փաթեթի փաստաթղթերի ներբեռնում

### Գործարքային Գործողություններ
- **ExportTransactions** (`App\Nova\Actions\ExportTransactions`): Գործարքային տվյալների արտահանում ֆորմատի ընտրությամբ

### Հաղորդակցության Գործողություններ
- **SendPurchaseRequestBySms** (`App\Nova\Actions\SendPurchaseRequestBySms`): Գնման հայտերի ուղարկում SMS-ով

### Ուղղորդման Գործողություններ
- **DownloadReferralCodes** (`App\Nova\Actions\DownloadReferralCodes`): Ուղղորդման կոդերի տվյալների ներբեռնում

### Կայունացման Գործողություններ
- **DownloadLoanDocuments** (`App\Nova\Actions\Stabilization\DownloadLoanDocuments`): Կայունացման վարկային փաստաթղթերի ներբեռնում

## Ֆիլտրեր և Որոնում

### Ամսաթվային Ֆիլտրեր
- **LoanSignDateFilter** (`App\Nova\Filters\LoanSignDateFilter`): Վարկերի ֆիլտրում ստորագրման ամսաթվով
- **LoanApplicationDateFilter** (`App\Nova\Filters\LoanApplicationDateFilter`): Ֆիլտրում հայտի ամսաթվով
- **LoanWithdrawDateFilter** (`App\Nova\Filters\LoanWithdrawDateFilter`): Ֆիլտրում ստացման ամսաթվով
- **WithdrawDateFilter** (`App\Nova\Filters\WithdrawDateFilter`): Ֆիլտրում ստացման ամսաթվով
- **PurchaseRequestDateFilter** (`App\Nova\Filters\PurchaseRequestDateFilter`): Գնման հայտերի ֆիլտրում ամսաթվով
- **TransactionPaidDateFilter** (`App\Nova\Filters\TransactionPaidDateFilter`): Գործարքների ֆիլտրում վճարման ամսաթվով
- **VehicleCheckupDateFilter** (`App\Nova\Filters\VehicleCheckupDateFilter`): Ֆիլտրում տրանսպորտային ստուգման ամսաթվով
- **VehicleImportDateFilter** (`App\Nova\Filters\VehicleImportDateFilter`): Ֆիլտրում տրանսպորտային ներմուծման ամսաթվով

### Կարգավիճակի և Տեսակի Ֆիլտրեր
- **LoanStatusFilter** (`App\Nova\Filters\LoanStatusFilter`): Վարկերի ֆիլտրում կարգավիճակով
- **LoanTypeFilter** (`App\Nova\Filters\LoanTypeFilter`): Ֆիլտրում վարկի տեսակով (OCL, OVL, OASL, և այլն)
- **OASLStatusFilter** (`App\Nova\Filters\OASLStatusFilter`): OASL վարկերի ֆիլտրում կարգավիճակով
- **PurchaseRequestStatusFilter** (`App\Nova\Filters\PurchaseRequestStatusFilter`): Գնման հայտերի ֆիլտրում կարգավիճակով
- **OVLTypeFilter** (`App\Nova\Filters\OVLTypeFilter`): OVL վարկերի ֆիլտրում ենթատեսակով

### Բիզնես Սուբյեկտների Ֆիլտրեր
- **VendorFilter** (`App\Nova\Filters\VendorFilter`): Ֆիլտրում վաճառողով
- **VendorTypeFilter** (`App\Nova\Filters\VendorTypeFilter`): Ֆիլտրում վաճառողի տեսակով
- **WithdrawCompanyFilter** (`App\Nova\Filters\WithdrawCompanyFilter`): Ֆիլտրում ստացող ընկերությամբ
- **OASLAgentFilter** (`App\Nova\Filters\OASLAgentFilter`): OASL վարկերի ֆիլտրում գործակալով
- **MortgageLoanReferrerSourceFilter** (`App\Nova\Filters\MortgageLoanReferrerSourceFilter`): Գրավային վարկերի ֆիլտրում ուղղորդողով
- **ReferrerSourceFilter** (`App\Nova\Filters\ReferrerSourceFilter`): Ֆիլտրում ուղղորդողի աղբյուրով

### Հատուկ Ֆիլտրեր
- **TopUpLoanFilter** (`App\Nova\Filters\TopUpLoanFilter`): Լրացուցիչ վարկերի ֆիլտր
- **QRCodeFilter** (`App\Nova\Filters\QRCodeFilter`): QR կոդերի ֆիլտր
- **QrTypeFilter** (`App\Nova\Filters\QrTypeFilter`): Ֆիլտրում QR տեսակով
- **TransferTypeFilter** (`App\Nova\Filters\TransferTypeFilter`): Ֆիլտրում փոխանցման տեսակով

### Որոնման Հնարավորություններ
Ռեսուրսների մեծ մասը աջակցում է առաջադեմ որոնմանը, ներառյալ:
- **Պայմանագրի Համարի Որոնում**: Հասանելի վարկային ռեսուրսներում
- **Քաղաքացու Որոնում**: Որոնում անձնագրի համարով, անուններով
- **Տրանսպորտային Որոնում**: Որոնում տրանսպորտային համարով, VIN-ով
- **Կապակցված Որոնում**: Որոնում կապակցված մոդելներում
## Թույլտվություններ և Լիազորում

### Մուտքի Վերահսկման Համակարգ
Nova-ն օգտագործում է Laravel-ի թույլտվությունների համակարգը դեր-հիմնված մուտքի վերահսկմամբ:

#### Ադմինիստրատիվ Դերեր
- **admin**: Համակարգի ամբողջական մուտք
- **gc-customer-admin**: Հաճախորդների ադմինիստրատիվ մուտք
- **upay-admin**: Upay համակարգի ադմինիստրացիա
- **telcell-admin**: Telcell համակարգի ադմինիստրացիա

#### Գործառնական Դերեր
- **upay-cashier**: Upay դրամարկղային գործառույթներ
- **telcell-cashier**: Telcell դրամարկղային գործառույթներ
- **oasl-agent**: Արևային վարկի գործակալի գործառույթներ

### Հիմնական Թույլտվություններ

#### Վարկային Կառավարման Թույլտվություններ
- `view-loans`: Վարկային գրառումների դիտում
- `view-full-history`: Ամբողջական վարկային պատմության դիտում
- `view-loan-history`: Վարկային պատմության դիտում
- `view-cash`: Դրամական գործառույթների դիտում
- `view-oasl-loan`: Արևային վարկերի դիտում
- `view-ovl-loan`: Տրանսպորտային վարկերի դիտում
- `verify-ovl-loan`: Տրանսպորտային վարկերի ստուգում
- `view-reml`: Գրավային վարկերի դիտում
- `view-reml-loan-applications`: Գրավային հայտերի դիտում
- `stabilization-loan`: Կայունացման վարկերի մուտք

#### Գործարքային Թույլտվություններ
- `view-bnpl-transactions`: BNPL գործարքների դիտում
- `view-purchase-request`: Գնման հայտերի դիտում
- `view-all-purchase-requests`: Բոլոր գնման հայտերի դիտում

#### Համակարգային Թույլտվություններ
- `view-nova`: Nova միջերեսի մուտք
- `view-merchant`: Վաճառակետային տվյալների դիտում
- `view-qr-codes`: QR կոդերի դիտում
- `view-oasl-users`: OASL օգտատերերի դիտում
- `view-all-details`: Մանրամասն տեղեկությունների դիտում

#### Գործիքների Թույլտվություններ
- `correct-devated-schedule`: Ժամանակացույցի ուղղման գործիքի մուտք
- `bank-report`: Բանկային հաշվետվության գործիքի մուտք
- `acra-monitoring`: ACRA մոնիտորինգի գործիքի մուտք
- `view-payment-invoice`: Վճարային հաշիվ-ապրանքագրի գործիքի մուտք

### Լիազորման Դարպասներ
- **viewNova**: Nova մուտքը վերահսկող հիմնական դարպաս
- Ռեսուրս-մակարդակի լիազորում `availableForNavigation()` մեթոդների միջոցով
- Դաշտ-մակարդակի լիազորում `canSee()` callback-ների միջոցով
- Գործողություն-մակարդակի լիազորում `canRun()` callback-ների միջոցով

## Հատուկ Բաղադրիչներ

### Nova Բաղադրիչներ (nova-components գրացուցակ)

#### Հաստատման Բաղադրիչներ
- **ApproveArpiSolarLoan**: Արևային վարկի հաստատման աշխատանքային հոսքի բաղադրիչ
- **ApproveVehicleLoan**: Տրանսպորտային վարկի հաստատման աշխատանքային հոսքի բաղադրիչ

#### UI Բաղադրիչներ
- **ActionButton**: Հատուկ գործողության կոճակի բաղադրիչ
- **CancelButton**: Չեղարկման գործողության կոճակի բաղադրիչ
- **CustomLoadingCard**: Հատուկ բեռնման ցուցիչի քարտ
- **UploadCard**: Ֆայլ վերբեռնման քարտի բաղադրիչ
- **MediaView**: Մեդիա դիտման բաղադրիչ

#### Ձևի Բաղադրիչներ
- **EvaluationCompanyForm**: Գնահատող ընկերության ձևի բաղադրիչ
- **SecretKey**: Գաղտնի բանալու կառավարման բաղադրիչ

#### Ֆիլտրի Բաղադրիչներ
- **NovaDateRangeFilter**: Ամսաթվային միջակայքի ֆիլտրման բաղադրիչ
- **PerPageFilter**: Էջի տարրերի քանակի ֆիլտրի բաղադրիչ
- **ResetAllFilter**: Բոլոր ֆիլտրերի վերակայման բաղադրիչ

#### Մոնիտորինգ և Հաշվետվություն
- **LoanExpirationCard**: Վարկի ժամկետի մոնիտորինգի քարտ
- **RuleHistory**: Կանոնների կատարման պատմության բաղադրիչ

### Դաշտերի Տեսակներ և Հարմարեցումներ

#### Հատուկ Դաշտերի Տեսակներ
- **TextCopy**: Պատճենելի տեքստային դաշտ
- **BooleanTick**: Բարելավված բուլյան ցուցադրում
- **Indicator**: Կարգավիճակի ցուցիչի դաշտ
- **NovaDependencyContainer**: Պայմանական դաշտի կոնտեյներ
- **Select2**: Բարելավված ընտրության դաշտ որոնմամբ

#### Նկարի և Մեդիա Դաշտեր
- **Image**: Բարելավված նկարի դաշտ AWS S3 ինտեգրացիայով
- **Qrcode**: QR կոդի ստեղծման և ցուցադրման դաշտ

### Կոնֆիգուրացիա և Տեղայնացում

#### Տեղայնացման Աջակցություն
- Բազմալեզու աջակցություն `env('NOVA_LOCALE')` միջոցով
- Թարգմանության բանալիներ բոլոր օգտատեր-ուղղված տեքստի համար
- Հայկական ժամային գոտու աջակցություն (`constants('ARM_TIMEZONE')`)

#### Հատուկ Ոճավորում
- Հատուկ CSS: `/css/admin_custom.css`
- Հատուկ JavaScript: `/js/custom.js`

### Ինտեգրացիայի Կետեր

#### AWS Ինտեգրացիա
- S3 պահեստ փաստաթղթերի և նկարների համար
- QR կոդի պահեստ և վերականգնում
- Փաստաթղթերի ստեղծում և պահեստ

#### Արտաքին Ծառայությունների Ինտեգրացիա
- ACRA վարկային բյուրոյի ինտեգրացիա
- Բանկային հաշվետվությունների մշակում
- Վճարային դարպասների ինտեգրացիաներ (Upay, Telcell, IDram, Easypay)

#### Տվյալների Բազայի Ինտեգրացիա
- Մոդելի դիտարկիչներ տվյալների ամբողջականության համար
- Կապերի կառավարում
- Հարցումների օպտիմիզացիա eager loading-ով

## Տեխնիկական Իրականացման Մանրամասներ

### Nova Կոնֆիգուրացիա
- **Ուղի**: `/nova` (կարգավորելի)
- **Պահապան**: Կարգավորելի նույնականացման պահապան
- **Միջանկյալ Ծրագրերի Շտապել**:
  - Web միջանկյալ ծրագրեր
  - Նույնականացում
  - Լիազորում
  - Թույլտվությունների քեշավորում
  - Գործիքների bootstrap

### Ռեսուրսների Գրանցում
- Ավտոմատ ռեսուրսների հայտնաբերում `app/Nova` գրացուցակում
- Դինամիկ ռեսուրսների բեռնում թույլտվությունների հիման վրա
- Պայմանական նավիգացիա օգտատերերի դերերի հիման վրա

### Արդյունավետության Օպտիմիզացիաներ
- Eager loading `$with` հատկությամբ
- Հարցումների սահմանափակում մեծ տվյալների բազաների համար
- Էջակիցներ կարգավորելի էջի տարրերի տարբերակներով
- Ինդեքսի օպտիմիզացիա որոնելի դաշտերի համար

### Անվտանգության Գործառույթներ
- Դեր-հիմնված մուտքի վերահսկում
- Թույլտվություն-հիմնված դաշտերի տեսանելիություն
- Գործողությունների լիազորում
- CSRF պաշտպանություն
- Մուտքային տվյալների վալիդացիա և մաքրում

## Եզրակացություն

Global Credit Nova ադմինիստրացիայի համակարգը տրամադրում է համապարփակ միջերես վարկային և ֆինանսական ծառայությունների բիզնեսի բոլոր ասպեկտների կառավարման համար: Իր դեր-հիմնված մուտքի վերահսկմամբ, ընդարձակ ֆիլտրման հնարավորություններով և ինտեգրված աշխատանքային հոսքերի գործիքներով այն ծառայում է որպես ադմինիստրատիվ գործառույթների կենտրոնական կենտրոն:

Համակարգի մոդուլային դիզայնը թույլ է տալիս հեշտ սպասարկում և ապագա բարելավումներ՝ միաժամանակ ապահովելով անվտանգություն և արդյունավետություն: Փաստաթղթավորման կանոնավոր օգտագործումը և օգտագործման ուղեցույցների պահպանումը կապահովի համակարգի օպտիմալ արդյունավետություն և օգտատերերի փորձ:
