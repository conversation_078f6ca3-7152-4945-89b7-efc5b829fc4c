ՀԾ բանկ համակարգում զիջված վարկեր մարումների հետևանքով խախտված գրաֆիկենրի ուղղում (նշումներ Note4:կայունացում)

1. appbufer տվյալների շտեմարանում անհրաժեշտ է run անել կցված սկրիպտերը հետռևյալ հաջորդականությամբ
	1.1 [dbo].[HC_graf] 			(table valued function)
	1.2 [dbo].[Get_50PercRestructuredLIst] 	(table valued function)
	1.3 [dbo].[fix_payment50Perc]		(stored procedure)
	1.4 Fix_All_50p				(stored procedure)

Վերջին սկրիպտով ստեղծված պրոցեդուրան իրականացնում է շեղված վարկերի ուղղումը, օգտագործում

exec Fix_All_50p , բոլոր շեղվածների համար
կամ
exec Fix_All_50p 'loanid' առանձին վարկի համար:

Շեղվածների ամբողջական ցուցակը տեսնելու համար`

select * from [dbo].[Get_50PercRestructuredLIst] ()

Վարկերը ՀԾ ում ուղղելու համար անհրաժեշտ է աշխատեցնել հետևյալ առաջադրանքի տարրերը նշված հերթականությամբ

1. imC1Updt
2. imC1DtGz





