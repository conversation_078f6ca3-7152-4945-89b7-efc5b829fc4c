// Nova Tool CSS
.main-container {
  text-align: center;

  .approve-button {
    width: 100%;
    text-align: right;
    margin-top: 50px;
    padding: 5px;
  }

  .image {
    display: inline-block;
    background-repeat: no-repeat;
    width: 200px;
    height: 100px;
    background-position: center;
    background-size: contain;
    margin: 3px 5px;
    background-color: #eee;
    background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg xmlns='http://www.w3.org/2000/svg' version='1.1' width='64' height='64'%3E%3Ccircle cx='32' cy='32' r='25' stroke='white' stroke-width='7' fill='black' fill-opacity='0.2'/%3E%3Cpolygon points='26,22 26,42 43,32' fill='white'/%3E%3C/svg%3E");
  }

  .upload-section-vehicle {
    text-align: center;
    margin-top: 20px;
    box-shadow: 0 0px 5px 0px #ccc;
    padding: 10px;
    border-radius: 5px;

    .title {
      text-align: left;
    }
  }

  #blueimp-gallery {
    .close {
      display: block;
    }
  }

  .gallery-section {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
  }

  .title {
    font-size: 1.5rem;
    font-weight: 400;
  }

  .defects_section {
    text-align: left;
    margin-top: 50px;
    border-radius: 5px;
    box-shadow: 0 0px 5px 0px #ccc;
    padding: 10px;

    table {
      border-collapse: collapse;
      width: 100%;
      margin-top: 20px;

      td,
      th {
        border: 1px solid #dddddd;
        text-align: left;
        padding: 0px 5px;
        height: 35px;

        .comment_input {
          width: 100%;
          height: 100%;
        }

        input:focus {
          outline: none;
        }
      }
    }

    .mileage {
      border: 1px solid #ccc;
      border-radius: 5px;
      height: 30px;
      margin-left: 10px;
      margin-top: 30px;
    }

    .submit-container {
      width: 100%;
      text-align: right;
    }
  }

  input[type='radio']:disabled,
  input[type='tel']:disabled {
    cursor: not-allowed;
  }
}
