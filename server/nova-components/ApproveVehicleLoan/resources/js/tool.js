import VueGallery from 'vue-gallery';
import MediaView from '../../../MediaView/resources/js/components/Tool';
import EventBus from './eventBus';

Nova.booting((Vue, router, store) => {
  Vue.component('process-modal', require('./components/ProcessModal'));
  Vue.component('approve-vehicle-loan', require('./components/Tool'));
  Vue.component('vehicle-defects', require('./components/VehicleDefects'));
  Vue.component('loading', require('./components/Loading'));
  Vue.component('gallery', VueGallery);
  Vue.component('media-view', MediaView);
  Vue.use(EventBus);
});
