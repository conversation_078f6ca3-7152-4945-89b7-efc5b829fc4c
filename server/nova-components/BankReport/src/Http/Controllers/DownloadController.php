<?php

namespace Globalcredit\BankReport\Http\Controllers;

use App\Exceptions\InternalErrorException;
use App\Http\Controllers\Controller;
use <PERSON>vel\Nova\Actions\Action;
use Laravel\Nova\Http\Requests\NovaRequest;
use Log;
use Rap2hpoutre\FastExcel\FastExcel;

class DownloadController extends Controller
{
    public function __construct()
    {
    }

    public function download(NovaRequest $request)
    {
        try {
            $json = $request->all();
            $request_file = pathinfo($json['fileName']);
            $reports = $json['bankReports'];

            $file_ext = $request_file['extension'];
            $file_name = $request_file['filename'];
            // storing
            $path = env('NOVA_DOWNLOADS_PATH');
            $directory = public_path($path);
            $now = now()->setTimezone(constants('ARM_TIMEZONE'))->format(constants('FILE_TIME_FORMAT'));

            $file = "parsed_{$file_name}_$now.$file_ext";
            if ($file_ext === 'xml') {
                $file = "parsed_{$file_name}_$now".'.xls';
            }

            (new FastExcel($reports))->export($directory."/$file", function ($report) {
                return [
                    __('Date') => $report['date'],
                    __('Check Number') => $report['check_number'],
                    __('Contract Number') => $report['contract_number'],
                    __('Payment Type') => $report['payment_type'],
                    __('Amount') => $report['amount'],
                ];
            });

            return Action::download("$path/$file", $file);
        } catch (Exception $e) {
            Log::error('Exception', ['error' => $e->getMessage()]);
            throw new InternalErrorException();
        }
    }
}
