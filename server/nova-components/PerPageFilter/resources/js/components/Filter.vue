<template>
  <div>
    <h3 class="text-sm uppercase tracking-wide text-80 bg-30 p-3">
      {{ filter.name }}
    </h3>

    <div class="p-2">
      <select
        :dusk="filter.name + '-filter-select'"
        class="block w-full form-control-sm form-select"
        :value="value"
        @change="handleChange"
      >
        <option
          v-for="(option, index) in filter.options"
          :key="option + index"
          :value="option.value"
        >
          {{ option.name }}
        </option>
      </select>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    resourceName: {
      type: String,
      required: true,
    },
    filterKey: {
      type: String,
      required: true,
    },
  },

  mounted() {
    this.$nextTick(() => {
      this.filter.currentValue =
        this.$route.query[this.getPerPageName()] ||
        this.filter.options[0].value;
    });
  },

  methods: {
    handleChange(event) {
      const perPage = `${this.resourceName}_per_page`;

      this.$store.commit(`${this.resourceName}/updateFilterState`, {
        filterClass: this.filterKey,
        value: event.target.value,
      });

      this.$emit('change');

      this.$router.push({
        path: `/resources/${this.resourceName}`,
        query: {
          ...this.$route.query,
          [perPage]: event.target.value,
        },
      });
    },

    getPerPageName() {
      const { resourceName } = this.$route.params;
      return `${resourceName}_per_page`;
    },
  },
  computed: {
    filter() {
      return this.$store.getters[`${this.resourceName}/getFilter`](
        this.filterKey
      );
    },

    value() {
      return this.filter.currentValue;
    },
  },
};
</script>
