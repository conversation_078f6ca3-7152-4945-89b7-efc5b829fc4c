<template>
  <modal @modal-close="handleClose">
    <form
      @submit.prevent="handleConfirm"
      class="bg-white rounded-lg shadow-lg overflow-hidden"
      style="width: 460px"
    >
      <slot>
        <div class="p-8">
          <heading :level="2" class="mb-6">{{ __('Withdraw') }}</heading>

          <span class="form-file mr-4">
            <input
              ref="fileField"
              class="form-file-input"
              type="file"
              id="vendorFile"
              name="name"
              @change="fileChange"
            />
            <label
              for="vendorFile"
              class="form-file-btn btn btn-default btn-primary"
            >
              {{ __('Choose File') }}
            </label>
          </span>

          <span class="text-gray-50"> {{ currentLabel }} </span>

          <p v-if="hasError" class="text-xs mt-2 text-danger">
            {{ firstError.code || firstError }}
          </p>
        </div>
      </slot>

      <div class="bg-30 px-6 py-3 flex">
        <div class="ml-auto">
          <button
            type="button"
            @click.prevent="handleClose"
            class="btn text-80 font-normal h-9 px-3 mr-3 btn-link"
          >
            {{ __('Cancel') }}
          </button>
          <button
            type="submit"
            class="btn btn-default btn-danger"
            :disabled="disabled"
          >
            {{ __('Withdraw') }}
          </button>
        </div>
      </div>
    </form>
  </modal>
</template>

<script>
import { Errors } from 'laravel-nova';

export default {
  props: ['resourceId'],

  data: () => ({
    disabled: false,
    file: null,
    fileName: '',
    uploadErrors: new Errors(),
  }),

  methods: {
    handleClose() {
      this.$emit('close');
    },

    handleConfirm() {
      this.submitFile();
    },

    fileChange(event) {
      const path = event.target.value;
      const fileName = path.match(/[^\\/]*$/)[0];

      this.fileName = fileName;
      this.file = this.$refs.fileField.files[0];

      this.uploadErrors = new Errors();
    },

    composeFormData() {
      const formData = new FormData();
      if (this.file) {
        formData.append('vendor_file', this.file);
      }

      return formData;
    },

    async submitFile() {
      const formData = this.composeFormData();

      // Disable Withdraw button
      this.disabled = true;

      try {
        this.$emit('confirm-loading', true);

        const resp = await Nova.request().post(
          `/nova-vendor/action-button/withdraw/${this.resourceId}`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          }
        );

        this.$emit('confirm', resp);
      } catch (error) {
        if (error.response.status == 400) {
          this.$toasted.show(this.__('Something went wrong'), {
            type: 'error',
          });
        }

        if (error.response.status == 422) {
          this.uploadErrors = new Errors(error.response.data.errors);
        }
      } finally {
        this.disabled = false;
        this.$emit('confirm-loading', false);
      }
    },
  },

  mounted() {
    this.$refs.fileField.focus();
  },

  computed: {
    currentLabel() {
      return this.fileName || this.__('no file selected');
    },

    hasError() {
      return this.uploadErrors.has('vendor_file');
    },

    firstError() {
      if (this.hasError) {
        return this.uploadErrors.first('vendor_file');
      }
    },
  },
};
</script>
