<template>
  <div class="main-container">
    <div class="upload-section">
      <div class="upload">
        <p>{{ __('Media Upload') }}</p>
        <media-view
          :resourceId="resourceId"
          :processing="canUpload()"
          @mediaUploaded="mediaUploaded"
        ></media-view>
      </div>
      <div class="upload">
        <p>{{ __('Passport Upload') }}</p>
        <media-view
          :resourceId="resourceId"
          :processing="canUpload()"
          fileType="image/*,application/pdf"
          documentType="passport"
          @mediaUploaded="passportUploaded"
        ></media-view>
      </div>
      <div class="upload">
        <p>{{ __('Agreement Upload') }}</p>
        <media-view
          :resourceId="resourceId"
          :processing="canUpload()"
          fileType="image/*,application/pdf"
          documentType="agreement"
          @mediaUploaded="agreementUploaded"
        ></media-view>
      </div>
      <div class="upload" v-if="!this.field.isKFW">
        <p>{{ __('Report Upload') }}</p>
        <media-view
          :resourceId="resourceId"
          :processing="canUpload()"
          fileType="application/pdf"
          documentType="report"
          @mediaUploaded="reportUploaded"
        ></media-view>
      </div>
      <div class="upload">
        <p>{{ __('Reference Upload') }}</p>
        <media-view
          :resourceId="resourceId"
          :processing="canUpload()"
          fileType="image/*,application/pdf"
          documentType="reference"
        ></media-view>
      </div>
      <div class="expired-loan" v-if="isExpiredLoan()">
        {{ __('Expired Loan') }}
      </div>
    </div>
    <div class="approve-button" v-if="isProcessing()">
      <button
        class="btn btn-default btn-primary"
        :disabled="isConfirmDisabled()"
        v-on:click="confirm()"
      >
        {{ __('Confirm') }}
      </button>
      <button class="btn btn-default btn-danger" v-on:click="reject()">
        {{ __('Reject') }}
      </button>
    </div>
    <loading v-if="loading" large="true" />
  </div>
</template>

<script>
export default {
  props: ['resourceName', 'resourceId', 'field'],

  data: () => ({
    loading: false,
    confirmDisabled: false,
    isMediaUploaded: false,
    isPassportUploaded: false,
    isAgreementUploaded: false,
    isReportUploaded: false,
  }),

  methods: {
    isConfirmDisabled() {
      return (
        this.confirmDisabled ||
        !this.isMediaUploaded ||
        !this.isPassportUploaded ||
        !this.isAgreementUploaded ||
        !this.checkReportUploaded()
      );
    },

    checkReportUploaded() {
      if (this.field.isKFW) return true;

      return this.isReportUploaded;
    },

    mediaUploaded(uploaded) {
      if (uploaded !== undefined) {
        this.isMediaUploaded = uploaded;
      }
    },

    passportUploaded(uploaded) {
      if (uploaded !== undefined) {
        this.isPassportUploaded = uploaded;
      }
    },

    agreementUploaded(uploaded) {
      if (uploaded !== undefined) {
        this.isAgreementUploaded = uploaded;
      }
    },

    reportUploaded(uploaded) {
      if (uploaded !== undefined) {
        this.isReportUploaded = uploaded;
      }
    },

    isProcessing() {
      return (
        this.field.isOASLAdmin &&
        this.field.status === Nova.config.constants.LOAN_STATUS.PROCESSING &&
        !this.field.isExpiredLoan
      );
    },

    canUpload() {
      return (
        (this.field.isOASLGCAdmin &&
          this.field.status === Nova.config.constants.LOAN_STATUS.CONFIRMED) ||
        this.isProcessing()
      );
    },

    isExpiredLoan() {
      return (
        this.field.status !== Nova.config.constants.LOAN_STATUS.CONFIRMED &&
        this.field.isExpiredLoan
      );
    },

    async confirm() {
      this.loading = true;
      this.confirmDisabled = true;

      try {
        await Nova.request().post(
          `/nova-vendor/approve-arpi-solar-loan/loan/${this.resourceId}/confirm`
        );

        this.loading = false;

        this.$toasted.show(this.__('The action ran successfully!'), {
          type: 'success',
        });

        this.$router.push('/resources/arpi_solar_loans');
      } catch (error) {
        this.loading = false;
        this.confirmDisabled = false;

        const message =
          error.response.data.message || this.__('Something went wrong');
        this.$toasted.show(message, { type: 'error' });
      }
    },

    async reject() {
      this.loading = true;
      this.rejectDisabled = true;

      try {
        await Nova.request().post(
          `/nova-vendor/approve-arpi-solar-loan/loan/${this.resourceId}/reject`
        );

        this.loading = false;

        this.$toasted.show(this.__('The action ran successfully!'), {
          type: 'success',
        });

        this.$router.push('/resources/arpi_solar_loans');
      } catch (error) {
        this.loading = false;
        this.rejectDisabled = false;

        const message =
          error.response.data.message || this.__('Something went wrong');
        this.$toasted.show(message, { type: 'error' });
      }
    },
  },
};
</script>
