USE [appbuffer]
GO
/****** <PERSON><PERSON><PERSON> Mkrtich Gets all  affected loans with distortrd service fee schedule ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER FUNCTION [dbo].[Get_50PercRestructuredLIst] ()
RETURNS TABLE
AS
  RETURN
  (
  SELECT

    CN.fDGCODE,
    PAID.PaidAmount todayPaid,
    init_grap.diff,
    crdata.totsv,
    crdata.nmm,
    constdate.fDATE [Restructuring date]
  FROM [asvark].dbo.CONTRACTS CN WITH (NOLOCK)
  INNER JOIN [asvark].dbo.[DOCSG] DCC WITH (NOLOCK)
    ON DCC.fISN = CN.fDGISN
  OUTER APPLY (SELECT
    ISNULL(SUM(iif(fDBCR = 'C'
    AND fOP = 'DBT', fCURSUM, 0)), 0) PaidAmount
  FROM [asvark].dbo.HIR WITH (NOLOCK)
  WHERE fOBJECT = fDGISN
  AND fTYPE IN (
  'R1'
  , 'R2'
  , 'R@'
  , 'R3'
  , 'RF'
  , 'RL'
  , 'RT'
  )
  AND DATEDIFF(D, fDATE, GETDATE()) = 0) PAID
  CROSS APPLY (SELECT TOP (1)
    fDATE
  ---
  FROM [asvark].[dbo].[AGRSCHEDULE] dca
  OUTER APPLY (SELECT
    COUNT(*) rc
  FROM (SELECT
    prin.fDATE,
    prin.fSUM,
    prin.fVALUETYPE,
    ISNULL(SFval.fSUM, 0) sfva
  FROM [asvark].[dbo].[AGRSCHEDULEVALUES] prin
  CROSS APPLY (SELECT
    fDATE
  FROM [asvark].[dbo].[AGRSCHEDULE]
  WHERE fAGRISN = CN.fDGISN
  AND fINC = dca.fINC
  AND fTYPE <> 7) dc
  OUTER APPLY (SELECT
    MAX(fINC) mx
  FROM [asvark].[dbo].[AGRSCHEDULE]
  WHERE fAGRISN = CN.fDGISN
  AND fDATE <= dc.fDATE
  AND fTYPE = 7) sff
  OUTER APPLY (SELECT
    SUM(fSUM) AS fSUM
  FROM [asvark].[dbo].[AGRSCHEDULEVALUES]
  WHERE fAGRISN = CN.fDGISN
  AND fVALUETYPE = 7
  AND fDATE = prin.fDATE
  AND fINC = sff.mx) SFval
  WHERE prin.fAGRISN = CN.fDGISN
  AND prin.fVALUETYPE = 1
  AND prin.fINC = dca.fINC) cs
  WHERE sfva = 0) ss
  WHERE fAGRISN = CN.fDGISN
  AND fTYPE <> 7
  AND EXISTS (SELECT
    fDATE
  FROM [asvark].[dbo].[AGRSCHEDULE]
  WHERE fAGRISN = CN.fDGISN
  AND fDATE = dca.fDATE
  AND fTYPE = 7)
  AND ss.rc > 2
  AND fDATE > '2020-11-10'
  ORDER BY fDATE) constdate
  OUTER APPLY (SELECT
    COUNT(*) diff
  FROM [dbo].[HC_graf](CN.fDGCODE, GETDATE())

  WHERE ABS(sfvan - (SELECT TOP (1)
    sfvan
  FROM [dbo].[HC_graf](CN.fDGCODE, GETDATE())
  WHERE sfvan > 0
  ORDER BY fdate)
  ) > 10
  AND sfvan > 0) init_grap

  OUTER APPLY (SELECT
    SUM(@@rowcount) AS nmmm
  FROM [dbo].[HC_graf](CN.fDGCODE, GETDATE()) dm) totnm
  OUTER APPLY (SELECT
    SUM(dm.sfvan) totsv,

    (totnm.nmmm - SUM(CASE dam.sfvan
      WHEN 0 THEN 1
      ELSE 0
    END)) nmm
  FROM [dbo].[HC_graf](CN.fDGCODE, GETDATE()) dm
  INNER JOIN (SELECT
    *
  FROM [dbo].[HC_graf](CN.fDGCODE, constdate.fDATE)) dam
    ON dm.fdate = dam.fdate) crdata



  WHERE [fROW] = 0--
  AND DCC.[fCOL] = 'VALUE'
  AND LTRIM(RTRIM(DCC.[fVALUE])) LIKE '%Ï³Ûáõ%'
  AND init_grap.diff > 0
  )