<template>
  <div class="seller-container">
    <div class="row flex flex-wrap">
      <formulate-input
        type="text"
        name="firstName"
        :label="__('First Name')"
        :placeholder="__('First Name')"
        v-model="seller.firstName"
        validation="required:trim"
        :validation-name="__('First Name')"
        :disabled="isFieldDisabled"
      />

      <formulate-input
        type="text"
        name="lastName"
        :label="__('Last Name')"
        :placeholder="__('Last Name')"
        v-model="seller.lastName"
        validation="required:trim"
        :validation-name="__('Last Name')"
        :disabled="isFieldDisabled"
      />

      <formulate-input
        type="text"
        name="middleName"
        :label="__('Middle Name')"
        :placeholder="__('Middle Name')"
        v-model="seller.middleName"
        validation="required:trim"
        :validation-name="__('Middle Name')"
        :disabled="isFieldDisabled"
      />

      <formulate-input
        type="tel"
        name="phoneNumber"
        :label="__('Phone Number')"
        :placeholder="__('Phone Number')"
        v-model="seller.phoneNumber"
        v-mask="phoneNumberMask"
        :help="PHONE_NUMBER_HELPER"
        validation="required:trim|max:20,length"
        :validation-name="__('Phone Number')"
        :disabled="isFieldDisabled"
        :element-class="['phone-number-input']"
        ref="phoneNumberInput"
      >
        <template v-slot:prefix>
          <span class="prefix">
            {{ PHONE_NUMBER_PREFIX }}
          </span>
        </template>
      </formulate-input>

      <formulate-input
        name="birthday"
        type="date"
        :label="__('Birthday')"
        v-model="seller.birthday"
        validation="required:trim"
        :validation-name="__('Birthday')"
        :disabled="isFieldDisabled"
      />

      <formulate-input
        type="text"
        name="passportNumber"
        :label="__('Passport Number')"
        :placeholder="__('Passport Number')"
        v-model="seller.passportNumber"
        validation="required:trim|size:9,length"
        :validation-name="__('Passport Number')"
        :disabled="isFieldDisabled"
      />

      <formulate-input
        type="date"
        name="givenDate"
        :label="__('Given At')"
        v-model="seller.givenDate"
        validation="required:trim"
        :validation-name="__('Given At')"
        :disabled="isFieldDisabled"
      />

      <formulate-input
        type="text"
        name="from"
        :label="__('Passport From')"
        :placeholder="__('Passport From')"
        v-model="seller.from"
        validation="required:trim|size:3,length"
        :validation-name="__('Passport From')"
        :disabled="isFieldDisabled"
      />

      <formulate-input
        type="text"
        name="registrationAddress"
        :label="__('Registration Address')"
        :placeholder="__('Registration Address')"
        v-model="seller.registrationAddress"
        validation="required:trim"
        :validation-name="__('Registration Address')"
        :disabled="isFieldDisabled"
      />

      <formulate-input
        type="text"
        name="bank"
        :label="__('Bank')"
        :placeholder="__('Bank')"
        v-model="seller.bank"
        :disabled="isFieldDisabled"
      />

      <formulate-input
        type="number"
        name="bankAccount"
        :label="__('Bank Account')"
        :placeholder="__('Bank Account')"
        v-model="seller.bankAccount"
        validation="size:16,length"
        :validation-name="__('Bank Account')"
        :disabled="isFieldDisabled"
      />
    </div>
  </div>
</template>

<script>
import { phoneNumberMask } from '../helpers/masks';

const PHONE_NUMBER_HELPER = '+374 (XX) XXX XXX';
const PHONE_NUMBER_PREFIX = '+374';

export default {
  props: ['seller', 'isFieldDisabled'],

  data() {
    return {
      phoneNumberMask,
      PHONE_NUMBER_HELPER,
      PHONE_NUMBER_PREFIX,
    };
  },
};
</script>
