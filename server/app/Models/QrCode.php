<?php

namespace App\Models;

use App\Helpers\EnvironmentHelper;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class QrCode extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'token',
        'file_name',
        'qr_image_size',
        'discount',
        'loan_type_id',
        'name',
        'description',
        'start_date',
        'end_date',
    ];

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        'start_date',
        'end_date',
    ];

    protected $appends = ['qr_link'];

    /**
     * Get the type of loan.
     */
    public function loan_types()
    {
        return $this->belongsTo('App\Models\LoanType', 'loan_type_id');
    }

    public function qr_type()
    {
        return $this->belongsTo('App\Models\QrType');
    }

    public function qr_owner()
    {
        return $this->hasOne('App\Models\QrOwner');
    }

    public static function getDiscount($token, $loan_type_id)
    {
        $qr_code = self::where('token', $token)->whereIn('loan_type_id', self::getLoanTypes($loan_type_id))
            ->where('end_date', '>=', Carbon::now())
            ->where('qr_type_id', QrType::RATE_DISCOUNT)
            ->first();

        return $qr_code->discount ?? 0;
    }

    public static function getTypeName($token)
    {
        $qr_code = self::where('token', $token)->first();

        return $qr_code->qr_type->name ?? null;
    }

    protected static function getLoanTypes($loan_type_id)
    {
        switch ($loan_type_id) {
            case constants('LOAN_TYPES.OCL'):
            case constants('LOAN_TYPES.OVL'):
            case constants('LOAN_TYPES.COMMON'):
                return [
                    constants('LOAN_TYPES.OCL'),
                    constants('LOAN_TYPES.OVL'),
                    constants('LOAN_TYPES.COMMON'),
                ];
            case constants('LOAN_TYPES.OIQL'):
            case constants('LOAN_TYPES.OASL'):
            case constants('LOAN_TYPES.OIDL'):
            case constants('LOAN_TYPES.OTCL'):
            case constants('LOAN_TYPES.OEPL'):
            case constants('LOAN_TYPES.OUPL'):
            case constants('LOAN_TYPES.PL'):
            case constants('LOAN_TYPES.REML'):
            case constants('LOAN_TYPES.VLX'):
            case constants('LOAN_TYPES.BNPL'):
            case constants('LOAN_TYPES.OIWL'):
            case constants('LOAN_TYPES.OBL'):
            case constants('LOAN_TYPES.OFSL'):
                return [$loan_type_id];
        }
    }

    public function scopeName($query, $qr_token)
    {
        return $query->where('token', $qr_token)->pluck('name')->first();
    }

    public function getQrLinkAttribute()
    {
        // TODO: This is a temporary solution for the referral QR
        if ($this->token == 'nndptbbm') {
            return 'https://bit.ly/3Fl1m5H';
        }

        return EnvironmentHelper::generateUrl("/app/qr/$this->token");
    }
}
