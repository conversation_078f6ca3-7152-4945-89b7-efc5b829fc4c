<?php

namespace App\Factory;

use App;
use App\Exceptions\InternalErrorException;
use Log;

class OWLServiceFactory
{
    public static function build($loan_type_id)
    {
        if ($loan_type_id == constants('LOAN_TYPES.OIDL')) {
            return App::environment('production') ? new \App\Services\OIDLService() : new \App\Services\DevOIDLService();
        } elseif ($loan_type_id == constants('LOAN_TYPES.OTCL')) {
            return App::environment('production') ? new \App\Services\OTCLService() : new \App\Services\DevOTCLService();
        } elseif ($loan_type_id == constants('LOAN_TYPES.OEPL')) {
            return App::environment('production') ? new \App\Services\OEPLService() : new \App\Services\DevOEPLService();
        } elseif ($loan_type_id == constants('LOAN_TYPES.OIWL')) {
            return App::environment('production') ? new \App\Services\OIWLService() : new \App\Services\DevOIWLService();
        } elseif ($loan_type_id == constants('LOAN_TYPES.OUPL')) {
            return App::environment('production') ? new \App\Services\OUPLService() : new \App\Services\DevOUPLService();
        } elseif ($loan_type_id == constants('LOAN_TYPES.OFSL')) {
            return App::environment('production') ? new \App\Services\OFSLService() : new \App\Services\DevOFSLService();
        } else {
            Log::error('Invalid loan type, Exception');
            throw new InternalErrorException();
        }
    }
}
