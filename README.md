# globalcredit

### Run react app along with semantic ui watch

```
yarn start
```

### Running the server

```
1. cd docker
2. cp env-example .env
3. docker-compose up -d nginx postgres redis workspace
4. docker-compose exec --user=laradock workspace bash
5. cp .env.dev .env
6. composer install
7. php artisan key:generate
8. php artisan jwt:secret -f
9. php artisan migrate --seed
10. php artisan cache:forget spatie.permission.cache (just in case)

```

### Running the web
```
At first check is the node installed in your machine 
(the node version will be 10-14)
1. cd web
2. cp .env.example .env
3. npm install yarn 
4. yarn install
5. yarn start

Then will open the http://localhost:3000/ url 
```

### Running tests
```
Run this commands to run project main tests and reuqired package tests.
1. phpunit
2. phpunit vendor/globalcredit/rule-engine/tests
```

### To run OIDL and OTCL

```
1. cd server
2. yarn install
3. yarn run watch
```

### Running jobs
#### Development env
Start
php artisan queue:work database --daemon --tries=3 --timeout=0 &

Stop
ps aux | grep php
kill -9 process-pid

#### Production env
sudo supervisorctl start globalcredit-worker:*

### Creating Pivot Table with Generators package

```
Generators package adds new commands in the make:* namespace section(to see them run 'php artisan')
Now we can create pivot table with this command

php artisan make:migration:pivot table1 table2

```

### PHP Debugger setup (VS Code)

```
1. Make sure to have in docker/.env
   WORKSPACE_INSTALL_XDEBUG=true
   PHP_FPM_INSTALL_XDEBUG=true
2. If not on Mac:
   a. Modify docker/php-fpm/xdebug.ini
      xdebug.remote_host = YOUR-HOST-IP
   b. Modify docker/workspace/xdebug.ini
      xdebug.remote_host = YOUR-HOST-IP
3. Rebuild workspace and php-fpm to pick up new xdebug.ini values
   docker-compose build workspace php-fpm

4. docker-compose up -d nginx postgres redis workspace
5. cmd + shift + d in VS Code (View -> Debug)
6. Create PHP config by clicking on gear icon
7. Make sure the content is like this:
{
  "version" : "0.2.0" ,
  "configurations" : [
    {
      "name" : "Listen for XDebug" ,
      "type" : "php" ,
      "request" : "launch" ,
      "pathMappings" : {
        "/var/www": "${workspaceRoot}/server",
      },
      "log" : true,
      "port" : 9000
    }
  ]
}
8. Start debugger and put breakpoints
9. Reload the app

ISSUES:
1. If breakpoint doesn't trigger, try to change ${workspaceRoot} to the full
   path on the disk. Like this for example:
   "pathMappings" : {
     "/var/www": "/Users/<USER>/globalcredit/server",
   },
```

#### NOTE:

```
In production set in docker/.env:
   WORKSPACE_INSTALL_XDEBUG=false
   PHP_FPM_INSTALL_XDEBUG=false

Or in docker/php-fpm/xdebug.ini and docker/workspace/xdebug.ini:
   xdebug.remote_autostart=0
   xdebug.remote_enable=0
```

### Linter setup in VS Code

Please follow the instructions here:
https://prettier.io/docs/en/editors.html#visual-studio-code

[![code style: prettier](https://img.shields.io/badge/code_style-prettier-ff69b4.svg?style=flat-square)](https://github.com/prettier/prettier)

### Supervisor config in production

```
1. sudo vi /etc/supervisord.conf
2. Edit to have this in the program section:

[group:globalcredit-worker]
programs=globalcredit-worker-default,globalcredit-worker-sms

[program:globalcredit-worker-default]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/globalcredit/server/artisan queue:work database --tries=3 --timeout=0
autostart=true
autorestart=true
user=nginx
numprocs=11
redirect_stderr=true
stdout_logfile=/var/log/globalcredit-worker.log
stderr_logfile=/var/log/globalcredit-worker-err.log

[program:globalcredit-worker-sms]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/globalcredit/server/artisan queue:work database --queue=sms --tries=3 --timeout=0
autostart=true
autorestart=true
user=nginx
numprocs=1
redirect_stderr=true
stdout_logfile=/var/log/globalcredit-worker.log
stderr_logfile=/var/log/globalcredit-worker-err.log

sudo supervisorctl update

3. sudo supervisorctl reread
4. sudo supervisorctl update
5. sudo supervisorctl start globalcredit-worker:*

```

### Windows 10 docker fix

...

`docker volume create postgres_database`

And then you link it like that :

services:
  postgres:
    restart: always
    volumes: - postgres_database:/var/lib/postgresql/data:Z

volumes:
  postgres_database:
    external: true

...

### WIndows 10 husky fix

...
"pre-commit": "lint-staged && set CI=true&&npm test CI=true yarn test"
...

### Access IQOS virtual host

...
Modify /etc/hosts on OSX or Linux and C:\Windows\System32\drivers\etc\hosts on Windows
to look like this:
127.0.0.1 iqos.cashme.com

Open http://iqos.cashme.com
...

### Production deployment

1. Merge release to production:
⋅⋅* after merging everything to master branch, create new branch from up-to-date master branch
⋅⋅* git checkout -b release/v{version_number}
⋅⋅* git rebase origin/production
⋅⋅* git push origin release/v{version_number}
..* open Github PR to production & merge it
..* create Release in Github, create tag & associate with release version number
2. ssh into **************
3. pg_dump aobyte > gc_dump_*s
4. cd /var/www/globalcredit/
5. git checkout production
6. git fetch
7. git rebase origin/production
8. cd web (yarn install if needed)
9. yarn build
10. cd ../server
11. yarn install
12. yarn prod
13. composer install
14. php artisan migrate
15. sudo supervisorctl restart globalcredit-worker:*

### Running multiple app instances in dev environment
#### docker/.env changes
* COMPOSE_PROJECT_NAME=new-name
* WORKSPACE_SSH_PORT=new-unused-port
* WORKSPACE_BROWSERSYNC_HOST_PORT=new-unused-port
* WORKSPACE_BROWSERSYNC_UI_HOST_PORT=new-unused-port
* NGINX_HOST_HTTP_PORT=new-unused-port
* NGINX_HOST_HTTPS_PORT=new-unused-port
* POSTGRES_PORT=new-unused-port
* VARNISH_BACKEND_PORT=new-unused-port

#### docker/docker-compose.yml
* Rename postgres to postgres-new-name

#### server/.env
* Postgres DB_HOST=postgres-new-name

## Project related account

### Google Analytics account

u: <EMAIL>
p: ForeverAnalytics

### Running the server with browsersync
1. cd docker
2. make sure that copied 'WORKSPACE_BROWSERSYNC_' prefixed variables from 'env-example' to '.env'
3. docker-compose up -d nginx(other images too if needed)
4. docker-compose exec --user=laradock workspace bash
5. make sure that copied 'MIX_' prefixed variables from '.env.dev' to '.env'
6. yarn install(be sure that using node version 14 or lower)
7. yarn watch
8. open new tab in the browser and past http://localhost:3005 url
9. then make any changes into server/resources folder, and it will automatically reload the page