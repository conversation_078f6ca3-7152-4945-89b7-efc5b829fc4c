<?php

namespace Globalcredit\ActionButton\Http\Controllers;

use App\Dtos\Notifier\PushNotificationDto;
use App\Dtos\Notifier\SmsNotificationDto;
use App\Exceptions\LoanNotFoundException;
use App\Exceptions\LoanWithdrawnException;
use App\Factory\DocumentServiceFactory;
use App\Factory\HcServiceFactory;
use App\Factory\LoanServiceFactory;
use App\Factory\TopUpServiceFactory;
use App\Jobs\GenerateWithdrawCheck;
use App\Models\CashPayment;
use App\Models\HC\HcGCCRDTCODE;
use App\Models\Loan;
use App\Models\LoanDocument;
use App\Models\Pallaton\UserDevice;
use Exception;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Laravel\Nova\Http\Requests\NovaRequest;
use Throwable;

class ActionButtonController extends Controller
{
    protected $loan_document_service;

    public function withdrawLoan(NovaRequest $request)
    {
        Log::info('Start withdraw process', ['cash_payment_id' => $request->id]);

        $payment = CashPayment::whereId($request->id)->first();

        $loan_id = $payment->loan[0]->id;

        $loan = Loan::getLoanWithDocuments($loan_id);
        if (!$loan || HcGCCRDTCODE::isCreditClosed($loan->contract_number)) {
            Log::warning('Loan not found or closed in HC', ['loan' => $loan]);

            throw new LoanNotFoundException();
        }

        $this->loan_document_service = DocumentServiceFactory::build($loan->loan_type_id);

        if ($loan->payment->withdrawn) {
            Log::warning('Loan Already Withdrawn', ['loan' => $loan]);
            throw new LoanWithdrawnException();
        }

        $hc_service = HcServiceFactory::build($loan->loan_type_id);
        $payment_service = resolve('App\Interfaces\IPaymentService');

        if ($loan->isTopUp()) {
            Log::info('Started top-up loan withdraw process', ['loan' => $loan]);

            $top_up_service = TopUpServiceFactory::build($loan->loan_type_id);

            $top_up_service->checkWithdrawEligibility($loan);

            $top_up_service->regenerateScheduleAndDocuments($loan);
            $loan = $loan->fresh();

            $hc_service->saveTopUp($loan);
        }

        $this->validateVendorFile($request);

        if ($request->file('vendor_file')) {
            $this->storeVendorFile($loan, $request->vendor_file);
            Log::info('Vendor File stored');
        }

        try {
            $loan = $payment_service->withdrawTransfer($loan, auth()->user());
            Log::info('Cash Transfer Withdraw success', ['loan' => $loan]);

            $this->notifyAboutLoanWithdrawn($loan);
        } catch (Exception $e) {
            Log::error('Withdraw Transfer Exception', ['message' => $e->getMessage()]);
            throw $e;
        }

        $path = GenerateWithdrawCheck::dispatchNow($loan);
        Log::info('Withdraw Check Generated By Path', ['path' => $path]);

        try {
            if ($loan->withdrawal_fee) {
                $hc_service->updateLoanDisbursementFee($loan);
            }
            $hc_service->updateLoanDisbursement($loan);
        } catch (Throwable $e) {
            Log::critical('Storing to Armsoft Cash Transfer failed', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString(), 'loan' => $loan]);
        }

        $redirect_url = env('APP_URL').'/nova/resources/withdrawn-loans/'.$loan->payment->id;

        return ['path' => $path, 'redirect_url' => $redirect_url];
    }

    protected function validateVendorFile($request)
    {
        $user = $request->user();

        $validator = Validator::make($request->all(), [
            'vendor_file' => 'mimes:pdf,png,jpg,jpeg',
        ]);

        $validator->sometimes('vendor_file', 'required', function ($input) use ($user) {
            return !$user->hasRole('gc-cashier') && !$user->hasRole('admin');
        });

        $validator->validate();
    }

    protected function notifyAboutLoanWithdrawn($loan)
    {
        if ($loan->loan_type_id !== constants('LOAN_TYPES.OVL')) {
            return;
        }

        $this->notifyBySms($loan);
        $this->notifyByPush($loan);
    }

    private function notifyBySms($loan)
    {
        $user_uuid = $loan->citizen->getSocCard()['passport_number'];

        $sms_dto = new SmsNotificationDto();
        $sms_dto->setValue('referenceId', $loan->id);
        $sms_dto->setValue('userUuId', $user_uuid);
        $sms_dto->setValue('recipientNumber', $loan->citizen->phone_number);
        $sms_dto->setValue('template', env('OVL_WITHDRAW_LOAN_SMS_TEMPLATE_ID'));
        $sms_dto->setValue('properties', [
            'contract_number' => $loan->contract_number,
        ]);

        $sms_notification_service = resolve('App\Services\Notifier\SmsNotificationService');
        $sms_notification_service->createSmsNotification($sms_dto->toArray());

        Log::debug('Citizen notified about Loan Withdrawn via SMS');
    }

    private function notifyByPush($loan)
    {
        $user_uuid = $loan->citizen->getSocCard()['passport_number'];
        $user_device = UserDevice::where('user_uuid', $user_uuid)->first();

        if (!$user_device) {
            return;
        }

        $push_dto = new PushNotificationDto();
        $push_dto->setValue('subject', __('notification.amount_withdrawn'));
        $push_dto->setValue('userUuId', $user_uuid);
        $push_dto->setValue('properties', [
            'contract_number' => $loan->contract_number,
        ]);
        $push_dto->setValue('template', env('OVL_WITHDRAW_LOAN_PUSH_TEMPLATE_ID'));

        $push_notification_service = resolve('App\Services\Notifier\PushNotificationService');
        $push_notification_service->create($push_dto->toArray());

        Log::debug('Citizen notified about Loan Withdrawn via Push');
    }

    public function storeVendorFile($loan, $file)
    {
        $type = LoanDocument::VENDOR_FILE;
        // We need to store the latest vendor file in history coz of top-up loan could have multiple withdrawals
        if ($loan->isTopUp()) {
            $loan->archiveLoanDocuments($type['name'], true);
        }

        $this->loan_document_service->storeExistingDirectoryDocument($loan, $type, $file);
    }

    public function isExpiredLoan(NovaRequest $request)
    {
        $payment = CashPayment::whereId($request->id)->first();

        $loan = $payment->loan[0];

        $loan_service = LoanServiceFactory::build($loan->loan_type_id);
        $is_credit_closed = HcGCCRDTCODE::isCreditClosed($loan->contract_number);

        return response()->json(['expired' => $loan_service->isExpiredLoan($loan) || $is_credit_closed]);
    }
}
