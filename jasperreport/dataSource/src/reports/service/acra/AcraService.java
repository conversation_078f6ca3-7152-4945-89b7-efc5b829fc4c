package reports.service.acra;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.json.JSONTokener;
import reports.ReportLogger;
import reports.helpers.DateHelper;
import reports.helpers.MathHelper;

import java.util.Date;
import java.util.Map;

import static org.apache.commons.math.util.MathUtils.round;

public class AcraService {
    protected AcraData acraData = new AcraData();

    public AcraService(String acra_loans) {
        this.setLoans(acra_loans);
    }

    public AcraService(String acra_loans, String acra_participent, String acra_guarantees) {
        this.setLoans(acra_loans);
        this.setGuarantees(acra_guarantees);
        this.setParticipent(acra_participent);
    }

    protected void setLoans(String acra_loans) {
        if (acra_loans == null) {
            ReportLogger.logger.info("Acra Loans data is empty");
            return;
        }

        try {
            Object acraJson = new JSONTokener(acra_loans).nextValue();
            if (acraJson instanceof JSONArray) {
                for (int i = 0; i < ((JSONArray) acraJson).length(); i++) {
                    acraData.setLoans((JSONObject) ((JSONArray) acraJson).get(i));
                }
            } else if (acraJson instanceof JSONObject) {
                acraData.setLoans((JSONObject) acraJson);
            }
        } catch (JSONException e) {
            ReportLogger.logger.error("Acra Service Set Loans JSONException", e);
        }
    }

    protected void setGuarantees(String acra_guarantees) {
        if (acra_guarantees == null) {
            ReportLogger.logger.info("Acra Guarantees data is empty");
            return;
        }

        try {
            Object acraJson = new JSONTokener(acra_guarantees).nextValue();
            if (acraJson instanceof JSONArray) {
                for (int i = 0; i < ((JSONArray) acraJson).length(); i++) {
                    acraData.setGuarantees((JSONObject) ((JSONArray) acraJson).get(i));
                }
            } else if (acraJson instanceof JSONObject) {
                acraData.setGuarantees((JSONObject) acraJson);
            }
        } catch (JSONException e) {
            ReportLogger.logger.error("Acra Service Set Guarantees JSONException", e);
        }
    }

    protected void setParticipent(String acra_participent) {
        if (acra_participent == null) {
            ReportLogger.logger.info("Acra Participent data is empty");
            return;
        }

        try {
            JSONObject acraJson = new JSONObject(acra_participent);

            if (acraJson.has("RequestQuantity")) {
                acraData.setRequestQuantity(acraJson.getInt("RequestQuantity"));
            }
        } catch (JSONException e) {
            ReportLogger.logger.error("Acra Service Set Participent JSONException", e);
        }
    }

    /**
     * Acra first loan crediting date
     *
     * @return String
     */
    public String acraHistoryStartDate() {
        Date minCreditingDate = null;

        for (JSONObject loan : this.acraData.getLoans()) {
            try {
                minCreditingDate = DateHelper.minEntryDate(minCreditingDate, getDate(loan.getString("CreditingDate")));
            } catch (JSONException e) {
                ReportLogger.logger.error("Acra History Start Date JSONException", e);
            }
        }

        return DateHelper.formatDateString(minCreditingDate, DateHelper.DATE);
    }

    /**
     * Acra Loans count with status 'գործող'
     *
     * @return int
     */
    public int activeLoansCount() {
        int activeLoansCount = 0;

        for (JSONObject loan : this.acraData.getLoans()) {
            try {
                if (loan.getString("CreditStatus").equals(AcraData.ACRA_ACTIVE_LOAN)) {
                    ++activeLoansCount;
                }
            } catch (JSONException e) {
                ReportLogger.logger.error("Active Loans Count JSONException", e);
            }
        }

        return activeLoansCount;
    }

    /**
     * Acra loans count
     *
     * @return int
     */
    public int loansCount() {
        return acraData.getLoansCount();
    }

    public int requestQuantity() {
        return acraData.getRequestQuantity();
    }

    /**
     * Acra overdue loans count
     *
     * @return int
     */
    public int overdueLoansCount() {
        int overdueLoansCount = 0;

        for (JSONObject loan : this.acraData.getLoans()) {
            try {
                if (loan.getString("CreditStatus").equals(AcraData.ACRA_ACTIVE_LOAN) &&
                        loan.getDouble("AmountOverdue") > 0) {
                    ++overdueLoansCount;
                }
            } catch (JSONException e) {
                ReportLogger.logger.error("Overdue Loans Count JSONException", e);
            }
        }

        return overdueLoansCount;
    }

    /**
     * Acra all loans the biggest one Amount
     *
     * @return double
     */
    public double loanBiggestAmount() {
        double amount = 0;

        for (JSONObject loan : this.acraData.getLoans()) {
            try {
                if (loan.getDouble("Amount") > amount) {
                    amount = loan.getDouble("Amount");
                }
            } catch (JSONException e) {
                ReportLogger.logger.error("Loan Biggest Amount JSONException", e);
            }
        }

        return amount;
    }

    /**
     * Check Acra loan existence with listed Pledge Subjects
     *
     * @return boolean
     */
    public boolean haveRealEstate() {
        for (JSONObject loan : this.acraData.getLoans()) {
            try {
                String pledgeSubject = loan.getString("PledgeSubject").toLowerCase();
                if (acraData.RealEstatePledgeSubjects.parallelStream().anyMatch(pledgeSubject::contains)) {
                    return true;
                }
            } catch (JSONException e) {
                ReportLogger.logger.error("Have Real Estate JSONException", e);
            }
        }

        return false;
    }

    /**
     * Check Acra loan existence with listed Credit Scopes
     *
     * @return boolean
     */
    public boolean haveMortgage() {
        for (JSONObject loan : this.acraData.getLoans()) {
            try {
                String creditScope = loan.getString("CreditScope").toLowerCase();
                if (acraData.creditScopes.parallelStream().anyMatch(creditScope::contains)) {
                    return true;
                }
            } catch (JSONException e) {
                ReportLogger.logger.error("Have Mortgage JSONException", e);
            }
        }

        return false;
    }

    /**
     * Sum of DelayPaymentQuantity1-12 of active loans
     *
     * @return int
     */
    public int delayPaymentQuantityInYear() {
        int totalDelayQuantity = 0;
        int delayQuantity = 0;

        for (JSONObject loan : this.acraData.getLoans()) {
            try {
                // Handling case when DelayPaymentQuantity is empty array
                try {
                    delayQuantity = loan.getInt("DelayPaymentQuantity1-12");
                } catch (JSONException e) {
                    delayQuantity = 0;
                }

                totalDelayQuantity += delayQuantity;
            } catch (Exception e) {
                ReportLogger.logger.error("Delay Payment Quantity In Year JSONException", e);
            }
        }

        return totalDelayQuantity;
    }

    /**
     * Sum of DelayPaymentFrequency1-12 of active loans
     *
     * @return int
     */
    public int delayPaymentFrequencyInYear() {
        int totalDelayFrequency = 0;
        int delayFrequency = 0;

        for (JSONObject loan : this.acraData.getLoans()) {
            try {
                // Handling case when DelayPaymentFrequency is empty array
                try {
                    delayFrequency = loan.getInt("DelayPaymentFrequency1-12");
                } catch (JSONException e) {
                    delayFrequency = 0;
                }

                totalDelayFrequency += delayFrequency;
            } catch (Exception e) {
                ReportLogger.logger.error("Delay Payment Frequency In Year JSONException", e);
            }
        }

        return totalDelayFrequency;
    }

    /**
     * Ratio of delayPaymentQuantityInYear, delayPaymentFrequencyInYear
     *
     * @return int
     */
    public int averageExpiredDaysInYear() {
        int delayPaymentQuantity = this.delayPaymentQuantityInYear();
        int delayPaymentFrequency = this.delayPaymentFrequencyInYear();

        return MathHelper.getRatio(delayPaymentQuantity, delayPaymentFrequency);
    }

    /**
     * Acra loans the worst one from TheWorstClassLoan1-12
     *
     * @return String
     */
    public String worstClassLoanInYear() {
        int worstClassLoan = 0;
        int loanClass;

        for (JSONObject loan : this.acraData.getLoans()) {
            try {
                try {
                    loanClass = acraData.loanClasses.get(loan.getString("TheWorstClassLoan1-12"));
                } catch (JSONException e) {
                    continue;
                }

                if (loanClass > worstClassLoan) {
                    worstClassLoan = loanClass;
                }
            } catch (Exception e) {
                ReportLogger.logger.error("Worst Class Loan In Year JSONException", e);
            }
        }

        return acraData.loanClasses.getKey(worstClassLoan);
    }

    /**
     * Acra guarantees the worst one from TheWorstClassGuarantee1-12
     *
     * @return String
     */
    public String worstClassGuaranteeInYear() {
        int worstClassGuarantee = 0;
        int loanClass;

        for (JSONObject guarantee : this.acraData.getGuarantees()) {
            try {
                try {
                    loanClass = acraData.loanClasses.get(guarantee.getString("TheWorstClassGuarantee1-12"));
                } catch (JSONException e) {
                    continue;
                }

                if (loanClass > worstClassGuarantee) {
                    worstClassGuarantee = loanClass;
                }
            } catch (Exception e) {
                ReportLogger.logger.error("Worst Class Guarantee In Year JSONException", e);
            }
        }

        return acraData.loanClasses.getKey(worstClassGuarantee);
    }

    /**
     * Acra non pledge and all loans balances ratio
     *
     * @return double
     */
    public double nonPledgeAndTotalBalanceRatio() {
        double totalBalance = 0;
        double nonPledgedBalance = 0;

        double balance = 0;
        for (JSONObject loan : this.acraData.getLoans()) {
            try {
                if (loan.getString("CreditStatus").equals(AcraData.ACRA_ACTIVE_LOAN)) {
                    balance = loan.getDouble("Balance");
                    if ((loan.get("PledgeSubject") instanceof JSONArray) || loan.getString("PledgeSubject").isEmpty() ||
                            acraData.nonPledgeSubjects.parallelStream().anyMatch(((String) loan.get("PledgeSubject")).toLowerCase()::contains)) {
                        nonPledgedBalance += balance;
                    }

                    totalBalance += balance;
                }
            } catch (JSONException e) {
                ReportLogger.logger.error("Non Pledge And Total Amount Ratio JSONException", e);
            }
        }

        return MathHelper.getRatio(nonPledgedBalance, totalBalance);
    }

    public MonthlyPayment monthlyPayment(Map citizenInfo) {
        MonthlyPayment monthlyPayment = new MonthlyPayment(citizenInfo, this.acraData.getLoans());

        return monthlyPayment;
    }

    private Date getDate(String date) {
        return DateHelper.convertToDate(date, DateHelper.ACRA_DATE);
    }
}