<template>
  <div class="flex border-b border-40" v-if="field.visible">
    <div class="w-1/4 py-4">
      <h4 class="font-normal text-80">{{ field.label }}</h4>
    </div>
    <div class="w-3/4 py-4">
      <span>
        <withdraw-modal
          v-if="showWithdrawModal"
          :resourceId="resourceId"
          @close="hideWithdrawModal"
          @confirm="confirmed"
          @confirm-loading="confirmLoading"
        />

        <button
          @click="openWithdrawModal"
          v-if="showWithdrawButton"
          class="bg-transparent hover:bg-transparent text-danger font-semibold hover:text-info py-2 px-4 border border-danger hover:border-info rounded"
        >
          {{ __('Withdraw') }}
        </button>
        <h4 v-if="isExpiredLoan" class="mt-2 text-danger">
          {{ __('Expired Withdraw') }}
        </h4>
      </span>
    </div>
    <loading v-if="loading" :large="true" class="flex mr-8" />
  </div>
</template>

<script>
export default {
  props: ['resource', 'resourceName', 'resourceId', 'field'],

  computed: {
    showWithdrawButton() {
      return this.showButton && !this.isExpiredLoan && !this.loading;
    },
  },

  data: () => ({
    showButton: true,
    showWithdrawModal: false,
    isExpiredLoan: false,
    loading: false,
  }),

  async mounted() {
    await this.checkIsExpiredLoan();
  },

  methods: {
    async checkIsExpiredLoan() {
      try {
        this.loading = true;
        const {
          data: { expired },
        } = await Nova.request().get(
          `/nova-vendor/action-button/is-expired-loan/${this.resourceId}`
        );

        this.isExpiredLoan = expired;
      } catch (error) {
        const message = this.__('Something went wrong');
        this.$toasted.show(message, { type: 'error' });
      } finally {
        this.loading = false;
      }
    },

    openWithdrawModal() {
      this.showWithdrawModal = true;
    },

    hideWithdrawModal() {
      this.showWithdrawModal = false;
    },

    confirmed(response) {
      this.showButton = false;
      this.hideWithdrawModal();
      this.openWithdrawCheck(response.data.path);

      // Redirect the screen to see already withdrawn data
      window.location.replace(response.data.redirect_url);
    },

    confirmLoading(loading) {
      this.loading = loading;
    },

    openWithdrawCheck(path) {
      const win = window.open();
      // A hack to open new tab in async callback
      win.location = path;
      win.opener = null;
      win.blur();
      window.focus();
    },
  },
};
</script>
