Create or alter procedure Fix_All_50p (
  @loanid varchar(50)= Null
) as Begin Declare @fDGCODE as varchar(30), 
@totsv money, 
@nmm int, 
@rdate as datetime DECLARE fix CURSOR FOR 
select 
  fDGCODE, 
  totsv, 
  nmm, 
  [Restructuring date] 
from 
  [dbo].[Get_50PercRestructuredLIst]() 
where 
  (
    fDGCODE = @loanid 
    and @loanid is not null
  ) 
  or (@loanid is null) OPEN fix;
FETCH NEXT 
FROM 
  fix INTO @fDGCODE, 
  @totsv, 
  @nmm, 
  @rdate WHILE @@FETCH_STATUS = 0 BEGIN --select @fDGCODE,@nmm,@totsv,@rdate
  exec [dbo].[fix_payment50Perc] @fDGCODE, 
  @nmm, 
  @totsv, 
  @rdate FETCH NEXT 
FROM 
  fix INTO @fDGCODE, 
  @totsv, 
  @nmm, 
  @rdate;
END;
CLOSE fix;
DEALLOCATE fix;
end
