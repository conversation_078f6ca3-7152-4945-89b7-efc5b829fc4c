<template>
  <section class="defects_section">
    <div class="title">Ավտոմեքենայի թերություններ</div>
    <table>
      <tr>
        <th>Հ/Հ անվանում</th>
        <th>Առկա չէ</th>
        <th>Առկա է</th>
        <th>Մեկնաբանություն</th>
      </tr>
      <tr>
        <td>Արտաքին թերություն</td>
        <td>
          <input
            type="radio"
            name="externalDefects"
            :value="false"
            :checked="!defects.externalDefects"
            @click="defects.externalDefects = false"
            :disabled="!isProcessing()"
          />
        </td>
        <td>
          <input
            type="radio"
            name="externalDefects"
            :value="true"
            :checked="defects.externalDefects"
            @click="defects.externalDefects = true"
            :disabled="!isProcessing()"
          />
        </td>
        <td>
          <input
            type="text"
            class="comment_input"
            name="externalDefectsComment"
            v-model="defects.externalDefectsComment"
            :disabled="!isProcessing()"
          />
        </td>
      </tr>
      <tr>
        <td>Սրահի թերություն</td>
        <td>
          <input
            type="radio"
            name="interiorDefects"
            :value="false"
            :checked="!defects.interiorDefects"
            @click="defects.interiorDefects = false"
            :disabled="!isProcessing()"
          />
        </td>
        <td>
          <input
            type="radio"
            name="interiorDefects"
            :value="true"
            :checked="defects.interiorDefects"
            @click="defects.interiorDefects = true"
            :disabled="!isProcessing()"
          />
        </td>
        <td>
          <input
            type="text"
            class="comment_input"
            name="interiorDefectsComment"
            v-model="defects.interiorDefectsComment"
            :disabled="!isProcessing()"
          />
        </td>
      </tr>
      <tr>
        <td>Շարժիչի թերություն</td>
        <td>
          <input
            type="radio"
            name="engineDefects"
            :value="false"
            :checked="!defects.engineDefects"
            @click="defects.engineDefects = false"
            :disabled="!isProcessing()"
          />
        </td>
        <td>
          <input
            type="radio"
            name="engineDefects"
            :value="true"
            :checked="defects.engineDefects"
            @click="defects.engineDefects = true"
            :disabled="!isProcessing()"
          />
        </td>
        <td>
          <input
            type="text"
            class="comment_input"
            name="engineDefectsComment"
            v-model="defects.engineDefectsComment"
            :disabled="!isProcessing()"
          />
        </td>
      </tr>
      <tr>
        <td>Փոխանցման տուփի թեր.</td>
        <td>
          <input
            type="radio"
            name="transmissionDefects"
            :value="false"
            :checked="!defects.transmissionDefects"
            @click="defects.transmissionDefects = false"
            :disabled="!isProcessing()"
          />
        </td>
        <td>
          <input
            type="radio"
            name="transmissionDefects"
            :value="true"
            :checked="defects.transmissionDefects"
            @click="defects.transmissionDefects = true"
            :disabled="!isProcessing()"
          />
        </td>
        <td>
          <input
            type="text"
            class="comment_input"
            name="transmissionDefectsComment"
            v-model="defects.transmissionDefectsComment"
            :disabled="!isProcessing()"
          />
        </td>
      </tr>
      <tr>
        <td>Լուսային ցուցիչներ</td>
        <td>
          <input
            type="radio"
            name="lightSensors"
            :value="false"
            :checked="!defects.lightSensors"
            @click="defects.lightSensors = false"
            :disabled="!isProcessing()"
          />
        </td>
        <td>
          <input
            type="radio"
            name="lightSensors"
            :value="true"
            :checked="defects.lightSensors"
            @click="defects.lightSensors = true"
            :disabled="!isProcessing()"
          />
        </td>
        <td>
          <input
            type="text"
            class="comment_input"
            name="lightSensorsComment"
            v-model="defects.lightSensorsComment"
            :disabled="!isProcessing()"
          />
        </td>
      </tr>
    </table>

    <div>
      <label>Վազքը</label>
      <input
        type="tel"
        class="mileage"
        name="mileage"
        v-model="defects.mileage"
        @keydown="mileageOnChange"
        :disabled="!isProcessing()"
      />
      <p v-if="hasError" class="text-xs mt-2 text-danger">
        {{ __(errorMessage) }}
      </p>
    </div>
    <div class="submit-container">
      <button
        class="btn btn-default btn-primary"
        v-if="isProcessing()"
        @click="submitForm"
      >
        {{ __('Save') }}
      </button>
    </div>
  </section>
</template>

<script>
import _ from 'lodash';
import EventBus from '../eventBus';

const ERROR_MSG = {
  INVALID_MILEAGE: 'Invalid Mileage',
  MILEAGE_REQUIRED: 'Required Field',
  SOMETHING_WENT_WRONG: 'Something went wrong',
  SUCCESSFULLY_ACTION: 'The action ran successfully!',
};

export default {
  props: ['resourceId', 'status'],

  data: () => ({
    defects: {
      externalDefects: false,
      externalDefectsComment: '',
      interiorDefects: false,
      interiorDefectsComment: '',
      engineDefects: false,
      engineDefectsComment: '',
      transmissionDefects: false,
      transmissionDefectsComment: '',
      lightSensors: false,
      lightSensorsComment: '',
      mileage: '',
      isMileageAcceptable: true,
    },
    hasError: false,
    errorMessage: '',
  }),

  async mounted() {
    const vehicleInfo = await this.getVehicleCondition();

    this.defects = { ...this.defects, ...this.camelizeKeys(vehicleInfo) };
    EventBus.$on('validate-defects', () => {
      if (
        this.isMileageEmpty() ||
        this.defects.isMileageAcceptable === undefined
      ) {
        this.hasError = true;

        this.$emit('checkValidity', false);
        this.errorMessage = ERROR_MSG['MILEAGE_REQUIRED'];

        return this.$emit('hasValidationError', true);
      }
      this.$emit('hasValidationError', false);
    });
  },

  methods: {
    async getVehicleCondition() {
      const resp = await Nova.request().get(
        `/nova-vendor/approve-vehicle-loan/loan/${this.resourceId}/vehicle-condition`
      );

      this.defects.isMileageAcceptable = resp.data.is_mileage_acceptable;

      this.$emit('checkValidity', this.defects.isMileageAcceptable);
      if (!this.defects.isMileageAcceptable) {
        this.hasError = true;
        this.errorMessage =
          this.defects.isMileageAcceptable === undefined
            ? ''
            : ERROR_MSG['INVALID_MILEAGE'];
      }
      return resp.data;
    },

    isProcessing() {
      return (
        this.status === Nova.config.constants.LOAN_STATUS.PROCESSING ||
        this.status === Nova.config.constants.LOAN_STATUS.PARTIAL_PROCESSED
      );
    },

    isMileageEmpty() {
      return !this.defects.mileage || this.defects.mileage.length === 0;
    },

    mileageOnChange(e) {
      const BACKSPACE = 8;
      const code = e.which || e.keyCode;
      const reg = new RegExp(/^[0-9]*$/);

      if (code !== BACKSPACE && !reg.test(e.key)) {
        e.preventDefault();
      } else {
        this.defects.mileage = e.target.value;
      }
    },

    async submitForm() {
      try {
        this.$emit('setLoading', true);

        const vehicleInfo = this.decamelizeKeys(this.defects);

        await Nova.request().post(
          `/nova-vendor/approve-vehicle-loan/loan/${this.resourceId}/vehicle-condition`,
          vehicleInfo
        );

        this.defects = { ...this.defects, isMileageAcceptable: true };
        this.$emit('checkValidity', true);
        this.$emit('hasValidationError', false);
        this.hasError = false;

        this.$toasted.show(this.__(ERROR_MSG['SUCCESSFULLY_ACTION']), {
          type: 'success',
        });
      } catch (error) {
        this.$emit('checkValidity', false);
        if (error.response.status === 422) {
          const message = error.response.data.errors.mileage;
          this.errorMessage =
            (message && message[0]) ||
            this.__(ERROR_MSG['SOMETHING_WENT_WRONG']);
          this.hasError = true;
        } else {
          const message =
            error.response.data.message ||
            this.__(ERROR_MSG['SOMETHING_WENT_WRONG']);
          this.$toasted.show(message, { type: 'error' });
        }
      } finally {
        this.$emit('setLoading', false);
      }
    },

    camelizeKeys(data) {
      return _.reduce(
        data,
        (acc, value, key) => {
          acc[_.camelCase(key)] = value;
          return acc;
        },
        {}
      );
    },

    decamelizeKeys(data) {
      return _.reduce(
        data,
        (acc, value, key) => {
          acc[_.snakeCase(key)] = value;
          return acc;
        },
        {}
      );
    },
  },
};
</script>
