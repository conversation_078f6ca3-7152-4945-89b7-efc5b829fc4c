1. Install git
sudo yum install git

2. Add ssh key
https://medium.com/riow/deploy-to-production-server-with-git-using-php-ab69b13f78ad

3. Git clone the repo to /var/www

4. Install php, php-fpm and nginx
https://tecadmin.net/install-php7-on-centos7/ (For php and php modules(including php-fpm))
Need this too:
sudo yum --enablerepo=remi-php72 install php-pgsql
sudo systemctl restart php-fpm

https://www.digitalocean.com/community/tutorials/how-to-install-linux-nginx-mysql-php-lemp-stack-on-centos-7
https://www.digitalocean.com/community/tutorials/how-to-install-nginx-on-centos-7
Configure nginx using nginx.conf and default.conf

For php-fpm config look:
https://www.digitalocean.com/community/tutorials/how-to-install-linux-nginx-mysql-php-lemp-stack-on-centos-7
But IMPORTANT:
listen.owner = nginx
listen.group = nginx

IMPORTANT: disable selinux
https://linuxize.com/post/how-to-disable-selinux-on-centos-7/

Also install:
sudo yum install zip unzip php7.2-zip


5. sudo chown -R nginx:nginx globalcredit/
   sudo chown -R aobyte:nginx web/
   sudo chown -R aobyte:nginx server/

6. Install redis
https://linuxize.com/post/how-to-install-and-configure-redis-on-centos-7/

7. Install postgres
sudo yum install https://download.postgresql.org/pub/repos/yum/11/redhat/rhel-7-x86_64/pgdg-centos11-11-2.noarch.rpm
sudo yum install postgresql11
sudo yum install postgresql11-server
sudo /usr/pgsql-11/bin/postgresql-11-setup initdb


// Enable the new one
sudo systemctl enable postgresql-11
sudo systemctl start postgresql-11
sudo su - postgres
createuser aobyte
createdb -O aobyte aobyte
psql
ALTER USER aobyte WITH PASSWORD 'secret';

Note: Find config file with sudo find / -name "pg_hba*"
sudo vi /var/lib/pgsql/11/data/pg_hba.conf
Change ident to md5
sudo systemctl restart postgresql-11

8. Install mssql driver
https://www.microsoft.com/en-us/sql-server/developer-get-started/php/rhel/

Partially correct!
cd /usr/local/src
wget https://pecl.php.net/get/sqlsrv-4.3.0.tgz
tar -zxvf sqlsrv-4.3.0.tgz
cd sqlsrv-4.3.0
sudo yum --enablerepo=remi-php72 install php-devel
sudo yum install unixODBC-devel
phpize
./configure
Make sure gcc is at least version 5, if not update it using:
https://stackoverflow.com/questions/36327805/how-to-install-gcc-5-3-with-yum-on-centos-7-2
make
make install
grep "pdo_sqlsrv.so" /etc/php.ini || echo "extension=pdo_sqlsrv.so" >> /etc/php.ini
rm -rf sqlsrv* pdo*
systemctl restart httpd


9. Install supervisor
https://computingforgeeks.com/configure-celery-supervisord-centos-7-django-virtualenv/

yum install supervisor

Create /etc/supervisord.d/globalcredit.ini with globalcredit.ini content

sudo systemctl start supervisord
sudo systemctl enable supervisord

sudo supervisorctl start globalcredit-worker:*

After changing conf run:
sudo supervisorctl reread
sudo supervisorctl update


10. Postgres replication setup

For PostgreSQL 12
https://medium.com/omis-engineering/django-multiple-database-system-using-postgresql-master-slave-database-architecture-f71d4e9e53ba

For PostgreSQL 11
Usefull articles:
https://linuxhint.com/setup_postgresql_replication/
https://blog.vpscheap.net/how-to-setup-replication-for-postgresql-in-centos-7/

For both master and slave follow postgres setup instructions (7)

Master setup
1. Create replication user in postgres
  sudo su - postgres
  psql
  CREATE USER replication REPLICATION LOGIN CONNECTION LIMIT 1 ENCRYPTED PASSWORD 'ask4493self2record11';

2. Modify sudo vi /var/lib/pgsql/11/data/postgresql.conf
At the end append:

listen_addresses = 'localhost,**************'
wal_level = replica
max_wal_senders = 10
wal_keep_segments = 64

************** is the master IP

3. Modify sudo vi /var/lib/pgsql/11/data/pg_hba.conf
Append:

host    replication     replication     **************/0   md5

************** is the slave IP

4. sudo systemctl restart postgresql-11


Slave setup
1. Stop postgres
sudo systemctl stop postgresql-11

2. Backup data directory and create new one
  cd /var/lib/pgsql/11
  mv data data-backup

  mkdir -p data
  chmod 700 data
  chown -R postgres:postgres data

3. Fetch all data of master database to slave database
  pg_basebackup -h ************** -D /var/lib/pgsql/11/data -P -U replication --wal-method=fetch

  ************** is master IP
  It will prompt for password for "replication" PostgreSQL user, that is "ask4493self2record11"

4. Modify sudo vi /var/lib/pgsql/11/data/postgresql.conf
At the end append:

listen_addresses = 'localhost,**************'
wal_level = replica
max_wal_senders = 10
wal_keep_segments = 64
hot_standby = on

************** is the slave IP

5. Modify sudo vi /var/lib/pgsql/11/data/pg_hba.conf
Append:

host    replication     replication     **************/0   md5

************** is the master IP

6. Create recovery.conf
  sudo touch /var/lib/pgsql/11/data/recovery.conf
  sudo vi /var/lib/pgsql/11/data/recovery.conf

Add the following:
standby_mode          = 'on'
primary_conninfo      = 'host=************** port=5432 user=replication password=ask4493self2record11'
trigger_file = '/tmp/MasterNow'

************** is the master IP

7. Start postgres
  sudo systemctl start postgresql-11


Jasper setup

Follow the steps in:
https://community.jaspersoft.com/documentation/jasperreports-server-install-guide/v56/installing-war-file-using-js-install-scripts#warfiledist_4279087660_1148723

To start jasper database
sudo docker run -d -p 5433:5432 --name jasper-pg -e POSTGRES_PASSWORD=ask4493self2record11 -v /var/lib/docker/volumes/2917017c477489954b22a6dc9c826189ef912574df664dea0ef5e90fc840f22b/_data:/var/lib/postgresql/data postgres

docker start jasper-pg (if container already exists)

// To enter container
sudo docker exec -it jasper-pg /bin/bash

To change default JVM settings:
Create setenv.sh file in /home/<USER>/tomcat/bin with following content:
export CATALINA_OPTS="$CATALINA_OPTS -Xms512m"
export CATALINA_OPTS="$CATALINA_OPTS -Xmx8192m"
export CATALINA_OPTS="$CATALINA_OPTS -XX:MaxPermSize=256m"

To use default JVM settings make sure there is no setenv.sh file in /home/<USER>/tomcat/bin or at least
setenv.sh file doesn't contain any JVM specific settings

You can also try following settings:
export CATALINA_OPTS="$CATALINA_OPTS -Xms8g"
export CATALINA_OPTS="$CATALINA_OPTS -Xmx8g"
export CATALINA_OPTS="$CATALINA_OPTS -Xss2M"
export CATALINA_OPTS="$CATALINA_OPTS -XX:PermSize=32m"
export CATALINA_OPTS="$CATALINA_OPTS -XX:MaxPermSize=512m"

To start tomcat:
cd /home/<USER>/tomcat/bin
./startup.sh

ELK setup
************** (ELK)
1. Expose logstash 5044 port by adding "5044:5044" in docker-compose logstash ports
2. Modify logstash.conf
input {
	tcp {
		port => 5000
		codec => multiline { pattern => "\[[\d]{4}" negate => "true" what => "previous" }
	}

	udp {
		port => 4379
		codec => multiline { pattern => "\[[\d]{4}" negate => "true" what => "previous" }
  }

	beats {
    port => 5044
  }
}

filter {
	mutate {
    remove_field => [ "host" ]
  }
	grok {
		match => { "message" => "\[%{TIMESTAMP_ISO8601:timestamp}\] %{DATA:environment}\.%{LOGLEVEL:severity}:" }
	}
}

output {
	elasticsearch {
		hosts => "elasticsearch:9200"
		user => "elastic"
		password => "changeme"
	}
}

**************(cashme)
1. Install filebeats
https://kifarunix.com/install-filebeat-on-fedora-30-fedora-29-centos-7/

1. sudo rpm --import https://packages.elastic.co/GPG-KEY-elasticsearch
2. Create /etc/yum.repos.d/elastic-7.x.repo and add this:
[elastic-7.x]
name=Elastic repository for 7.x packages
baseurl=https://artifacts.elastic.co/packages/7.x/yum
gpgcheck=1
gpgkey=https://artifacts.elastic.co/GPG-KEY-elasticsearch
enabled=1
autorefresh=1
type=rpm-md
3. yum install filebeat
4. Modify /etc/filebeat/filebeat.yml
5. sudo systemctl enable filebeat
6. sudo systemctl start filebeat


Jenkins
1. Add ssh key for git https://medium.com/riow/deploy-to-production-server-with-git-using-php-ab69b13f78ad
2. sudo usermod -aG docker aobyte
3. For remote ssh, run on host
  ssh-keygen -f remote-key
  mv ./remote-key.pub ~/.ssh/authorized_keys
  sudo chmod 600 ~/.ssh/authorized_keys
  Add private key to jenkins ssh configuration in http://**************:8090/credentials/store/system/domain/_/