<?php

namespace Globalcredit\UploadCard\Services;

class UploadCardServiceFactory
{
    public static function build($type)
    {
        // 57 is Support Loan Type
        if ($type == 57) {
            return resolve('Globalcredit\UploadCard\Services\StabilizationLoanUploadService');
        }

        if ($type == constants('LOAN_TYPES.OVL')) {
            return resolve('Globalcredit\UploadCard\Services\VehicleUploadService');
        }
    }
}
