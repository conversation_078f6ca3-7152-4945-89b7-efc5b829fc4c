package reports.loansContextReport.loanContextRepository;

import reports.ReportLogger;
import reports.db.QueryExecutor;
import reports.helpers.DateHelper;

import java.sql.Connection;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class LoanContextRepository implements ILoanContextRepository {

    private final Integer START_DATE_QUERY_POSITION = 1;
    private final Integer END_DATE_QUERY_POSITION = 2;

    private final String KEY_BY_COLUMN = "loan_securities_id";

    private QueryExecutor queryExecutor;

    StringBuilder LOAN_SECURITIES_QUERY = new StringBuilder("select loan_securities.document_number, loan_securities.ssn,\n" +
            "loan_securities.phone_number , loan_securities.id as loan_securities_id, loan_securities.created_at, loan_securities.loan_type_id,\n" +
            "loans.status as loan_status, loans.monthly_payment,\n" +
            "citizens.gender, citizens.birth_date, citizens.phone_number as citizen_phone_number,\n" +
            "citizen_passports.passport_number as citizen_ssn, \n" +
            "citizen_face_recognitions.status as  face_recognition_status\n" +
            "from loan_securities \n" +
            "left join citizen_face_recognitions on citizen_face_recognitions.loan_security_id = loan_securities.id\n" +
            "left join loans on loans.id =loan_securities.loan_id\n" +
            "left join citizens on citizens.loan_id = loans.id\n" +
            "left join citizen_passports on citizens.id = citizen_passports.citizen_id\n" +
            "where loan_securities.created_at > ?\n" +
            "and loan_securities.created_at < ?\n" +
            "and (citizen_passports.type='SOC_CARD' or citizen_passports.type IS NULL)"
    );

    StringBuilder LOAN_SECURITIES_CONTEXT_QUERY = new StringBuilder("select loan_securities.id as loan_securities_id,\n" +
            "loan_security_metas.\"content\"->'input'->>'isDead' as is_dead,\n" +
            "loan_security_metas.\"content\"->'input'->>'age' as age,\n" +
            "loan_security_metas.\"content\"->'input'->>'hasLongHistory' as has_long_history,\n" +
            "loan_security_metas.\"content\"->'input'->>'acraStatus' as acra_status,\n" +
            "loan_security_metas.\"content\"->'input'->>'vehicleMarketPrice' as vehicle_market_price,\n" +
            "loan_security_metas.\"content\"->'input'->>'vehicleVin' as vehicle_vin,\n" +
            "loan_security_metas.\"content\"->'input'->>'loanLoad' as loan_load,\n" +
            "loan_security_metas.\"content\"->'input'->>'loanClass' as loan_class,\n" +
            "loan_security_metas.\"content\"->'input'->>'hasOverdueLoans' as has_overdue_loans,\n" +
            "loan_security_metas.\"content\"->'input'->>'guaranteeClass' as guarantee_class,\n" +
            "loan_security_metas.\"content\"->'input'->>'delayQuantity' as delay_quantity,\n" +
            "loan_security_metas.\"content\"->'input'->>'requestQuantity' as request_quantity_30,\n" +
            "loan_security_metas.\"content\"->'input'->>'hasCredit' as has_credit,\n" +
            "loan_security_metas.\"content\"->'input'->>'isBlocked' as is_blocked,\n" +
            "loan_security_metas.\"content\"->'input'->>'allowedLending' as allowed_lending,\n" +
            "loan_security_metas.\"content\"->'input'->>'allowedDuration' as allowed_duration,\n" +
            "loan_security_metas.\"content\"->'input'->>'fico' as fico,\n" +
            "loan_security_metas.\"content\"->'input'->>'existing_fico' as existing_fico,\n" +
            "loan_security_metas.\"content\"->'input'->>'salary' as salary\n" +
            "from loan_securities \n" +
            "left join loan_security_metas on loan_security_metas.loan_security_id = loan_securities.id\n" +
            "where (loan_security_metas.step = 'CONTEXT_INFO')\n" +
            "and loan_securities.created_at > ?\n" +
            "and loan_securities.created_at < ?");

    StringBuilder CITIZEN_INFO_QUERY = new StringBuilder("select loan_securities.id as loan_securities_id,\n" +
            "loan_security_metas    .\"content\"->'nork'->>'WorkData' as work_data,\n" +
            "loan_security_metas.\"content\"->'acra'->'PARTICIPIENT'->'Loans'->'Loan' as acra_loans,\n" +
            "loan_security_metas.\"content\"->'acra'->'PARTICIPIENT'->'Guarantees'->'Guarantee' as acra_guarantees,\n" +
            "loan_security_metas.\"content\"->'acra'->'PARTICIPIENT' as acra_participent,\n" +
            "loan_security_metas.\"content\"->'ekeng' as ekeng,\n" +
            "loans.sign_date as sign_date," +
            "loan_securities.created_at as loan_securities_created_at," +
            "citizens.salary as citizen_salary,\n" +
            "citizens.gender as citizen_gender,\n" +
            "citizens.birth_date as citizen_birth_date,\n" +
            "loans.status as gc_loan_status,\n" +
            "loans.months as gc_loan_months,\n" +
            "loans.apr as gc_loan_apr,\n" +
            "loans.amount as gc_loan_amount\n" +
            "from loan_securities \n" +
            "left join loans on loans.id = loan_securities.loan_id \n" +
            "inner join loan_security_metas on loan_security_metas.loan_security_id = loan_securities.id \n" +
            "left join citizens on citizens.loan_id = loans.id\n" +
            "where \n" +
            "loan_securities.created_at >= ? \n" +
            "and loan_securities.created_at <= ? \n" +
            "and loan_security_metas.step = 'CITIZEN_INFO'");

    StringBuilder CITIZEN_ESTIMATED_INCOME = new StringBuilder("select avg(gc_statistics.salary) as estimated_salary\n" +
            "from \n" +
            "(select loans.id as loan_id, citizens.gender, citizens.birth_date , citizens .salary, (loans.amount*loans.apr/1200)/(1-power((1+ loans .apr /1200), - loans .months ) ) as calculated_monthly_payment\n" +
            "\tfrom loans inner join citizens on loans.id = citizens .loan_id \n" +
            "\twhere loans.created_at > (current_date - interval '6' month) )\n" +
            "as gc_statistics \n" +
            "where gc_statistics.gender = ?\n" +
            "and DATE_PART('year', AGE(current_date, gc_statistics .birth_date)) > ?\n" +
            "and DATE_PART('year', AGE(current_date, gc_statistics .birth_date)) < ?\n" +
            "and gc_statistics.calculated_monthly_payment > ?\n" +
            "and gc_statistics.calculated_monthly_payment < ?");


    public LoanContextRepository(Connection conn) {
        this.queryExecutor = new QueryExecutor(conn);
    }

    public Map<Integer, Map<String, Object>> getLoanSecuritiesByParameters(Map<String, Object> parameters) {
        Map<Integer, Object> params = prepareInputValues(parameters);

        return this.queryExecutor.executeSelectQueryByClauseMapKeyByInt(LOAN_SECURITIES_QUERY.toString(), params, KEY_BY_COLUMN);
    }

    public Map<Integer, Map<String, Object>> getLoanSecuritiesContextByParameters(Map<String, Object> parameters) {
        Map<Integer, Object> params = prepareInputValues(parameters);

        return this.queryExecutor.executeSelectQueryByClauseMapKeyByInt(LOAN_SECURITIES_CONTEXT_QUERY.toString(), params, KEY_BY_COLUMN);
    }

    public Map<Integer, Map<String, Object>> getLoanCitizenInfoByParameters(Map<String, Object> parameters) {
        Map<Integer, Object> params = prepareInputValues(parameters);

        return this.queryExecutor.executeSelectQueryByClauseMapKeyByInt(CITIZEN_INFO_QUERY.toString(), params, KEY_BY_COLUMN);
    }

    public ArrayList<Map<String, Object>> getCitizenEstimatedIncomeByParameters(Map<String, Object> parameters) {
        Map<Integer, Object> params = new HashMap<>();
        params.put(1, parameters.get("gender"));
        params.put(2, parameters.get("age_min"));
        params.put(3, parameters.get("age_max"));
        params.put(4, parameters.get("monthly_payment_min"));
        params.put(5, parameters.get("monthly_payment_max"));

        return this.queryExecutor.executeSelectQueryByClauseMap(CITIZEN_ESTIMATED_INCOME.toString(), params);
    }

    /**
     * Preparing query parameters
     */
    private Map<Integer, Object> prepareInputValues(Map<String, Object> parameters) {
        Map<Integer, Object> params = new HashMap<Integer, Object>();
        params.put(START_DATE_QUERY_POSITION, (Timestamp) DateHelper.modifyInputDate((Date) parameters.get("start_date"), DateHelper.START_DAY_DEVIATION, DateHelper.UTC_TIME_DEVIATION));
        params.put(END_DATE_QUERY_POSITION, (Timestamp) DateHelper.modifyInputDate((Date) parameters.get("end_date"), DateHelper.END_DAY_DEVIATION, DateHelper.UTC_TIME_DEVIATION));

        return params;
    }
}
