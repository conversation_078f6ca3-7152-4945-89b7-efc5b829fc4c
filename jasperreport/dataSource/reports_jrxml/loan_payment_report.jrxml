<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.11.0.final using JasperReports Library version 6.11.0-0c4056ccaa4d25a5a8c45672d2f764ea3498bebb  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="idram_daily_report" pageWidth="700" pageHeight="500" columnWidth="660" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="a0414ea1-ffde-4c4b-b813-e5e594c1951b">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="Sample DB"/>
	<property name="ireport.jasperserver.url" value="http://192.168.11.229:8080/jasperserver/"/>
	<property name="ireport.jasperserver.user" value="jasperadmin"/>
	<property name="ireport.jasperserver.report.resource" value="/Reports/Report/daily_report_files/idram_daily_report.jrxml"/>
	<property name="ireport.jasperserver.reportUnit" value="/Reports/Report/daily_report"/>
	<property name="net.sf.jasperreports.export.xls.break.before.row" value="true"/>
	<property name="net.sf.jasperreports.export.xls.break.after.row" value="true"/>
	<property name="net.sf.jasperreports.export.xls.one.page.per.sheet" value="true"/>
	<property name="net.sf.jasperreports.export.xls.ignore.graphics" value="false"/>
	<property name="net.sf.jasperreports.page.break.no.pagination" value="apply"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<propertyExpression name="net.sf.jasperreports.export.xls.sheet.names.sheet1"><![CDATA["Ըստ Վճարման"]]></propertyExpression>
	<queryString language="SQL">
		<![CDATA[select 
date_trunc('day', loans.sign_date) as day,
sum(oidl_loans.amount) as oidl_loans_amount,
sum(otcl_loans.amount) as otcl_loans_amount,
sum(oepl_loans.amount) as oepl_loans_amount,
sum(card_loans.amount) as card_loans_amount,
sum(cash_loans.amount) as cash_loans_amount
from loans
left outer join loans as oidl_loans on loans.id = oidl_loans.id and oidl_loans.status = 'CONFIRMED' and oidl_loans.loan_type_id = 6
left outer join loans as otcl_loans on loans.id = otcl_loans.id and otcl_loans.status = 'CONFIRMED' and otcl_loans.loan_type_id = 7
left outer join loans as oepl_loans on loans.id = oepl_loans.id and oepl_loans.status = 'CONFIRMED' and oepl_loans.loan_type_id = 10
left outer join loans as card_loans on loans.id = card_loans.id and card_loans.status = 'CONFIRMED' and card_loans.payment_type = 'App\Models\CardToCardPayment'
left outer join loans as cash_loans on loans.id = cash_loans.id and cash_loans.status = 'CONFIRMED' and cash_loans.payment_type = 'App\Models\CashPayment'
and cash_loans.payment_id in (select id from cash_payments  where cash_payments.withdrawn is not null)
where loans.sign_date > (current_date - interval '14' day)
GROUP BY 1
ORDER BY 1;]]>
	</queryString>
	<field name="day" class="java.sql.Date"/>
	<field name="oidl_loans_amount" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="pending"/>
	</field>
	<field name="otcl_loans_amount" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="pending"/>
	</field>
	<field name="cash_loans_amount" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="pending"/>
	</field>
	<field name="card_loans_amount" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="pending"/>
	</field>
	<field name="oepl_loans_amount" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="pending"/>
	</field>
	<variable name="oidl_loans_amount1" class="java.lang.Long" calculation="Sum">
		<variableExpression><![CDATA[$F{oidl_loans_amount}]]></variableExpression>
	</variable>
	<variable name="otcl_loans_amount1" class="java.lang.Long" calculation="Sum">
		<variableExpression><![CDATA[$F{otcl_loans_amount}]]></variableExpression>
	</variable>
	<variable name="cash_loans_amount1" class="java.lang.Long" calculation="Sum">
		<variableExpression><![CDATA[$F{cash_loans_amount}]]></variableExpression>
	</variable>
	<variable name="card_loans_amount1" class="java.lang.Long" calculation="Sum">
		<variableExpression><![CDATA[$F{card_loans_amount}]]></variableExpression>
	</variable>
	<variable name="oepl_loans_amount1" class="java.lang.Long" calculation="Sum">
		<variableExpression><![CDATA[$F{oepl_loans_amount}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="55" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<textField>
				<reportElement x="25" y="15" width="100" height="30" uuid="99de7399-74e4-4aad-998c-70d8097a025e"/>
				<textElement textAlignment="Center">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Ըստ Վճարման"]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="61">
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.spreadsheet.SpreadsheetLayout"/>
			<staticText>
				<reportElement x="0" y="0" width="100" height="61" uuid="d5dfd4c3-6261-43d4-95a4-3b3b76c8be02">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="499a5baa-d885-489b-967c-db35bacb9f5f"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Ամսաթիվ]]></text>
			</staticText>
			<staticText>
				<reportElement x="100" y="0" width="100" height="61" uuid="0dcf7b40-f82e-4ae9-9842-c78de2001ec8">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="738cf615-2a2a-4f8e-bbda-912c1a157b69"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Իդրամ]]></text>
			</staticText>
			<staticText>
				<reportElement x="200" y="0" width="100" height="61" uuid="f724cda9-62ec-40fd-9aba-15b4f8c8f179">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="9df6d05a-b2c5-4c68-a9e7-90708453f7f1"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[TelCell]]></text>
			</staticText>
			<staticText>
				<reportElement x="300" y="0" width="100" height="61" uuid="553e02a6-04b8-47fd-8c6a-da7a5ee8a9ee">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="3cc75e23-39ca-47d0-b45c-bcb86aeac213"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Կանխիկ]]></text>
			</staticText>
			<staticText>
				<reportElement x="400" y="0" width="100" height="61" uuid="edd265f8-0947-4843-aaa9-036b2f54a08c">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="b9a1c8b8-e146-4ec4-b67d-7b3e785eee66"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Քարտային]]></text>
			</staticText>
			<staticText>
				<reportElement x="500" y="0" width="100" height="61" uuid="d0464b84-61e9-49e4-bd1b-927d75e13014">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="21a3809b-e26f-48d9-9e7e-10dea721d466"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Easy Pay]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="64">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.spreadsheet.SpreadsheetLayout"/>
			<textField isBlankWhenNull="true">
				<reportElement isPrintRepeatedValues="false" x="0" y="0" width="100" height="64" uuid="185c2082-92c7-4406-820d-419f367f71a0">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="499a5baa-d885-489b-967c-db35bacb9f5f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center"/>
				<textFieldExpression><![CDATA[$F{day}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="100" y="0" width="100" height="64" uuid="6106f195-4e85-4394-8748-3b53710c18c4">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="738cf615-2a2a-4f8e-bbda-912c1a157b69"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center"/>
				<textFieldExpression><![CDATA[$F{oidl_loans_amount}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="200" y="0" width="100" height="64" uuid="93778d9b-389f-433b-8eb0-eed50d606e4f">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="9df6d05a-b2c5-4c68-a9e7-90708453f7f1"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center"/>
				<textFieldExpression><![CDATA[$F{otcl_loans_amount}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="300" y="0" width="100" height="64" uuid="77aead36-cacf-40c1-956c-7cf3ae0b8a35">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="3cc75e23-39ca-47d0-b45c-bcb86aeac213"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center"/>
				<textFieldExpression><![CDATA[$F{cash_loans_amount}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="400" y="0" width="100" height="64" uuid="25cd1a70-7cb4-478f-9951-8be9e419c4d0">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="b9a1c8b8-e146-4ec4-b67d-7b3e785eee66"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center"/>
				<textFieldExpression><![CDATA[$F{card_loans_amount}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="500" y="0" width="100" height="64" uuid="ae2a92dd-6c6f-47a3-829b-e50284cba4be">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="21a3809b-e26f-48d9-9e7e-10dea721d466"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center"/>
				<textFieldExpression><![CDATA[$F{oepl_loans_amount}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="70" splitType="Prevent">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<textField isBlankWhenNull="true">
				<reportElement x="100" y="0" width="100" height="60" uuid="f6fe8201-d7d9-42e3-8b77-8061c45f8e72"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left"/>
				<textFieldExpression><![CDATA[$V{oidl_loans_amount1}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="200" y="0" width="100" height="60" uuid="1a16758b-2e25-4139-be4b-e3aeb770e205"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left"/>
				<textFieldExpression><![CDATA[$V{otcl_loans_amount1}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="300" y="0" width="100" height="60" uuid="d54e968a-3ccc-4914-bfe4-73236c272bcd"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left"/>
				<textFieldExpression><![CDATA[$V{cash_loans_amount1}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="400" y="0" width="100" height="60" uuid="d2faafe5-a0dc-42b3-ada3-88d97a7ac428"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left"/>
				<textFieldExpression><![CDATA[$V{card_loans_amount1}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="0" width="100" height="60" uuid="14281a47-a39f-48e8-b78c-871b27b311a9"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="500" y="0" width="100" height="60" uuid="c62df098-6d43-4958-b056-f6807566ed30"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left"/>
				<textFieldExpression><![CDATA[$V{oepl_loans_amount1}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
