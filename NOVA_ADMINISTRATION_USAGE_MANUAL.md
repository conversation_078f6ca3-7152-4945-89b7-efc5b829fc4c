# Global Credit Nova Administration Usage Manual

## Table of Contents
1. [Getting Started](#getting-started)
2. [User Management](#user-management)
3. [Loan Management](#loan-management)
4. [Transaction Management](#transaction-management)
5. [Merchant and QR Code Management](#merchant-and-qr-code-management)
6. [Document Management](#document-management)
7. [Reporting and Analytics](#reporting-and-analytics)
8. [System Administration](#system-administration)
9. [Troubleshooting](#troubleshooting)

## Getting Started

### Accessing Nova
1. Navigate to your application URL + `/nova` (e.g., `https://yourdomain.com/nova`)
2. Log in with your credentials
3. You'll see the Nova dashboard with cards based on your permissions

### Understanding the Interface
- **Navigation Sidebar**: Lists all resources you have access to
- **Dashboard**: Shows key metrics and quick access cards
- **Resource Views**: List, detail, create, and edit views for each resource
- **Actions**: Bulk operations available on selected records
- **Filters**: Narrow down data using various criteria

### Dashboard Overview
The dashboard shows different cards based on your role:
- **Admins**: See all loan types, users, and system metrics
- **Loan Officers**: See loan-specific cards and metrics
- **Cashiers**: See transaction and cash operation cards
- **Agents**: See assigned loans and relevant operations

## User Management

### Managing Users (Admin Only)

#### Creating a New User
1. Click **Users** in the sidebar
2. Click **Create User** button
3. Fill in required fields:
   - First Name and Last Name
   - Email (must be unique)
   - Password (minimum 8 characters)
   - Phone Number
   - Active status
4. Click **Create User**

#### Assigning Roles and Permissions
1. Go to **Users** → Select a user → **Edit**
2. In the **Roles** section, select appropriate roles:
   - `admin`: Full system access
   - `upay-cashier`: Upay operations
   - `telcell-cashier`: Telcell operations
   - `oasl-agent`: Solar loan operations
   - `gc-customer-admin`: Customer administration
3. Save changes

#### Managing Roles (Admin Only)
1. Click **Permission Tool** in the sidebar
2. **Roles** tab:
   - Create new roles
   - Assign permissions to roles
   - Edit existing role permissions
3. **Permissions** tab:
   - View all available permissions
   - Create custom permissions if needed

### User Access Levels
- **Admin Users**: See all users
- **Upay Admins**: See only upay-cashier users
- **Telcell Admins**: See only telcell-cashier users
- **OASL Permission Users**: See only oasl-agent users

## Loan Management

### Working with Loans

#### Viewing Loans
1. **All Loans**: Click **Loans** in sidebar (requires `view-loans` or `view-full-history` permission)
2. **Specific Loan Types**:
   - **Solar Loans**: Click **Solar Loans** card or **Arpi Solar Loans** in sidebar
   - **Vehicle Loans**: Click **Vehicle Loans** card or **Vehicle Loans** in sidebar
   - **Mortgage Loans**: Click **Mortgage Loans** card or **Mortgage Loans** in sidebar

#### Filtering Loans
Use the filter panel to narrow down results:
1. **Date Filters**:
   - Loan Sign Date: Filter by when loan was signed
   - Application Date: Filter by application submission date
   - Withdrawal Date: Filter by when loan was withdrawn
2. **Type Filters**:
   - Loan Type: OCL, OVL, OASL, OIDL, PL, OIQL, etc.
   - Top-up Loans: Show only top-up loans
3. **Status Filters**:
   - Loan Status: Active, completed, expired, etc.
   - OASL Status: For solar loans specifically

#### Searching Loans
- **Contract Number**: Enter contract number in search box
- **Citizen Information**: Search by passport number or names
- **Vehicle Information**: Search by vehicle number or VIN (for vehicle loans)

#### Loan Actions
Select one or more loans and use available actions:

**Document Actions**:
1. **Download Documents**:
   - Select loans → Choose "Download Documents" action
   - Documents will be packaged and downloaded
2. **Send Documents**:
   - Select loans → Choose "Send Documents" action
   - Documents will be sent to customer via configured method
3. **Regenerate Documents**:
   - Select loans → Choose "Regenerate Documents" action
   - System will recreate loan documents

**Solar Loan Specific Actions**:
1. **Create Package**: Create document package for selected solar loans
2. **Download KFW Loans**: Export KFW-specific loan data
3. **Edit Solar Panel Address**: Update installation address

**Management Actions**:
1. **Reassign Agent**: Change assigned agent for loans
2. **Regenerate Schedule**: Recreate payment schedules (OASL)
3. **Regenerate Withdraw Check**: Recreate withdrawal documentation

### Loan Details View
Click on any loan to see detailed information:
- **Basic Information**: Contract number, citizen details, loan type
- **Financial Details**: Amount, interest rate, terms
- **Status Information**: Current status, dates, agent assignment
- **Related Records**: Documents, transactions, mortgage details
- **Actions**: Available actions for this specific loan

### Working with Withdrawn/Withdrawable Loans

#### Withdrawable Loans (Cash Operations)
1. Click **Unpaid Loans** card or **Withdrawable Loans** in sidebar
2. These are loans ready for cash withdrawal
3. Use filters to find specific loans:
   - Withdraw Date Filter
   - Loan Type Filter
   - Company Filter
4. Process withdrawals as needed

#### Withdrawn Loans (History)
1. Click **Paid Loans** card or **Withdrawn Loans** in sidebar
2. View completed loan withdrawals
3. Use same filtering options as withdrawable loans
4. Export data using available actions

## Transaction Management

### BNPL Transactions

#### Viewing Transactions
1. Click **Transactions** in sidebar (requires `view-bnpl-transactions` permission)
2. View transaction list with:
   - Vendor information
   - Contract numbers
   - Amounts (in Dram)
   - Merchant details
   - Transaction dates

#### Filtering Transactions
- **Vendor Filter**: Filter by specific vendors
- **Date Filter**: Filter by transaction date
- **Amount Range**: Filter by transaction amounts

#### Exporting Transactions
1. Select transactions to export
2. Choose **Export Transactions** action
3. Specify filename and format (Excel, CSV, etc.)
4. Download generated file

### Purchase Requests

#### Creating Purchase Requests
1. Click **Purchase Requests** in sidebar
2. Click **Create Purchase Request**
3. Fill in required information:
   - Select Vendor from dropdown
   - Enter Amount
   - Add Description
   - System automatically assigns user ID
4. Save request

#### Managing Purchase Requests
1. View all requests with status information
2. Use filters:
   - Purchase Request Date Filter
   - Status Filter
   - Vendor Filter
3. Send SMS notifications:
   - Select requests → Choose "Send Purchase Request by SMS"

#### QR Code Generation
- Purchase requests automatically generate QR codes for payment
- QR codes are visible in the detail view
- Customers can scan QR codes to complete payments

## Merchant and QR Code Management

### Managing Merchants

#### Creating a New Merchant
1. Click **Merchants** in sidebar (requires `view-merchant` permission)
2. Click **Create Merchant** button
3. Fill in required information:
   - **Vendor**: Select from dropdown
   - **Merchant Name**: Internal name
   - **Merchant Title**: Display title (required)
   - **Address**: Physical address (required)
   - **Tax Information**: Armsoft Mi Tax Payer ID and Account Number
   - **Contact**: Phone numbers
   - **Type**: Hospital or Vehicle Import
4. Save merchant

#### Managing Merchant Agents
1. Go to merchant detail view
2. In **Agents** section, add or remove agents
3. Agents can be assigned to handle merchant operations

### QR Code Management

#### Creating QR Codes
1. Click **QR Codes** in sidebar (requires `view-qr-codes` permission)
2. Click **Create QR Code** button
3. Select **QR Type**:
   - **Discount Type**: For percentage-based discounts
   - **Owner Type**: For specific business owners
4. Fill in details based on type:

**For Discount QR Codes**:
- Select Loan Type
- Set Discount Percentage (within allowed range)
- Enter QR Name

**For Owner QR Codes**:
- Enter LP Name
- HVHH (8 digits)
- Address
- Phone Number
- Optional notes

#### Using QR Codes
- QR codes are automatically generated with images and links
- QR links can be copied and shared
- QR codes integrate with loan types for specific offers

## Document Management

### Loan Documents

#### Viewing Loan Documents
1. Go to any loan detail view
2. Click **Loan Documents** tab
3. View all documents associated with the loan

#### Document Actions
**Individual Loan Documents**:
1. **Download**: Get specific document files
2. **Regenerate**: Recreate documents if needed
3. **Send**: Email documents to customers

**Bulk Document Operations**:
1. Select multiple loans from loan list
2. Use bulk actions:
   - **Download Documents**: Package multiple loan documents
   - **Send Documents**: Send documents to multiple customers
   - **Regenerate Documents**: Recreate documents for multiple loans

#### Document History
1. Click **Loan Document History** in sidebar
2. View complete history of document operations
3. Track when documents were created, sent, or regenerated

### Transaction Documents
1. Click **Transaction Documents** in sidebar
2. View documents related to transactions
3. Similar management options as loan documents

### Package Management
1. Click **Packages** in sidebar
2. View document packages
3. Use package actions:
   - **Create Package**: Bundle documents together
   - **Change Package State**: Update package status
   - **Download Package Documents**: Get packaged documents

## Reporting and Analytics

### Dashboard Metrics

#### Loans Partition Metric (Admin Only)
- View loan distribution across different types
- Analyze loan portfolio composition
- Monitor loan performance metrics

#### Home Cards Analytics
Each dashboard card shows quick statistics:
- **Loan counts** by type and status
- **Quick navigation** to detailed views
- **Role-based visibility** for relevant data

### Data Export and Reporting

#### Loan Reports
1. **Download Loans**: Export loan data
   - Select loans → Choose "Download Loans" action
   - Specify format and filters
2. **Download KFW Loans**: Export solar loan data for KFW reporting
3. **Download QR Loans**: Export QR-based loan information

#### Transaction Reports
1. **Export Transactions**:
   - Go to Transactions → Select records
   - Choose "Export Transactions" action
   - Specify filename and format
2. **Download Loan Applications**: Export application data

#### Referral Reports
1. **Download Referral Codes**: Export referral program data
   - Go to Referral Codes → Select records
   - Use download action

### Using Filters for Reporting
Combine multiple filters to create specific reports:
1. **Date Range**: Set start and end dates
2. **Loan Type**: Filter by specific loan products
3. **Status**: Filter by loan status
4. **Agent/Company**: Filter by responsible parties
5. **Export**: Use download actions to get filtered data

## System Administration

### Using Administrative Tools

#### Bank Report Tool
1. Click **Bank Report** in sidebar (requires `bank-report` permission)
2. Generate bank reports for regulatory compliance
3. Export reports in required formats
4. Schedule regular report generation

#### ACRA Monitoring Tool
1. Click **ACRA Monitoring** in sidebar (requires `acra-monitoring` permission)
2. Monitor credit bureau data synchronization
3. View ACRA request status and responses
4. Handle data discrepancies

#### Deviated Schedules Tool
1. Click **Deviated Schedules** in sidebar (requires `correct-devated-schedule` permission)
2. Identify schedule deviations
3. Correct payment schedules
4. Apply schedule adjustments

#### Settings Tool
1. Click **Settings** in sidebar (Admin or `gc-customer-admin` role)
2. Configure system parameters
3. Update business rules
4. Manage system configurations

#### Payment Invoice Tool
1. Click **Payment Invoice** in sidebar (requires `view-payment-invoice` permission)
2. Generate payment invoices
3. Track invoice status
4. Manage payment processing

### Configuration Management

#### Rules and Rule Groups
1. **Rules**: Click **Rules** in sidebar
   - View business rules
   - Edit rule parameters
   - Monitor rule execution
2. **Rule Groups**: Click **Rule Groups** in sidebar
   - Organize rules into logical groups
   - Manage rule hierarchies

#### System Configuration
1. **Skyline Config**: Click **Skyline Config** in sidebar
   - System-wide configuration settings
   - Integration parameters
   - Performance settings

### Blacklist Management
1. **Blacklist**: Click **Blacklist** in sidebar
   - View blacklisted customers
   - Add/remove blacklist entries
   - Manage blacklist reasons
2. **Blacklist Reasons**: Click **Blacklist Reasons** in sidebar
   - Categorize blacklist reasons
   - Manage reason codes
## Troubleshooting

### Common Issues and Solutions

#### Access Issues
**Problem**: Cannot see certain resources or actions
**Solutions**:
1. Check user roles and permissions
2. Verify the user has the required permission for the resource
3. Contact admin to assign appropriate roles
4. Check if the resource requires specific conditions (e.g., admin-only features)

**Problem**: "Unauthorized" error when accessing Nova
**Solutions**:
1. Ensure user has `view-nova` permission
2. Check if user account is active
3. Verify authentication credentials
4. Contact system administrator

#### Performance Issues
**Problem**: Slow loading of loan lists or large datasets
**Solutions**:
1. Use filters to narrow down results
2. Adjust "Per Page" filter to show fewer records
3. Use specific date ranges instead of viewing all records
4. Clear browser cache and cookies

**Problem**: Timeout errors during bulk operations
**Solutions**:
1. Process smaller batches of records
2. Use more specific filters before bulk operations
3. Perform operations during off-peak hours
4. Contact system administrator if issues persist

#### Data Issues
**Problem**: Missing or incorrect loan information
**Solutions**:
1. Check if user has permission to view all loan details
2. Verify loan status and type filters
3. Use search function with contract number
4. Check related records (citizen, vehicle, etc.)

**Problem**: Documents not generating or downloading
**Solutions**:
1. Check document generation permissions
2. Verify loan has all required information
3. Try regenerating documents
4. Check AWS S3 connectivity (for admins)

#### Filter and Search Issues
**Problem**: Filters not working as expected
**Solutions**:
1. Clear all filters and reapply
2. Use "Reset All Filters" option
3. Check date format compatibility
4. Verify filter values are valid

**Problem**: Search not returning expected results
**Solutions**:
1. Check search terms for typos
2. Try partial matches instead of exact matches
3. Use different search fields (contract number vs. citizen name)
4. Clear search and try again

### Getting Help
1. **Documentation**: Refer to this manual and the technical documentation
2. **System Administrator**: Contact your system admin for permission issues
3. **Technical Support**: Contact technical support for system errors
4. **Training**: Request additional training for complex operations

## Role-Specific Workflows

### Administrator Workflows

#### Daily Tasks
1. **System Monitoring**:
   - Check dashboard metrics
   - Review system performance
   - Monitor user activity
2. **User Management**:
   - Review new user requests
   - Update user permissions
   - Deactivate inactive users
3. **Data Quality**:
   - Review data integrity
   - Check for anomalies
   - Validate reports

#### Weekly Tasks
1. **Permission Audit**:
   - Review user roles and permissions
   - Update access rights as needed
   - Remove unnecessary permissions
2. **System Maintenance**:
   - Review system logs
   - Check integration status
   - Update configurations

#### Monthly Tasks
1. **Reporting**:
   - Generate comprehensive reports
   - Analyze system usage
   - Review performance metrics
2. **Backup and Security**:
   - Verify backup procedures
   - Review security settings
   - Update system documentation

### Loan Officer Workflows

#### Processing New Loans
1. **Review Applications**:
   - Access loan applications through appropriate resource
   - Verify customer information
   - Check documentation completeness
2. **Loan Approval Process**:
   - Use approval actions for specific loan types
   - Generate required documents
   - Send documents to customers
3. **Follow-up**:
   - Track loan status
   - Monitor payment schedules
   - Handle customer inquiries

#### Managing Existing Loans
1. **Status Updates**:
   - Update loan statuses as needed
   - Process payments and adjustments
   - Handle loan modifications
2. **Document Management**:
   - Regenerate documents when needed
   - Send updated documents to customers
   - Maintain document history
3. **Customer Communication**:
   - Use SMS actions for notifications
   - Email documents and updates
   - Handle customer service requests

### Cashier Workflows

#### Daily Cash Operations
1. **Withdrawable Loans**:
   - Review loans ready for withdrawal
   - Process cash withdrawals
   - Update loan statuses
2. **Transaction Processing**:
   - Monitor BNPL transactions
   - Process payments
   - Handle transaction disputes
3. **QR Code Operations**:
   - Manage QR code payments
   - Process purchase requests
   - Handle payment confirmations

#### End-of-Day Procedures
1. **Reconciliation**:
   - Export transaction data
   - Reconcile cash operations
   - Generate daily reports
2. **Documentation**:
   - Update transaction records
   - File required documentation
   - Prepare reports for management

### Agent Workflows

#### Loan Management
1. **Assigned Loans**:
   - View loans assigned to you
   - Update loan progress
   - Communicate with customers
2. **Document Handling**:
   - Generate required documents
   - Collect customer signatures
   - Upload completed documents
3. **Customer Service**:
   - Handle customer inquiries
   - Process loan modifications
   - Coordinate with other departments

#### Reporting
1. **Daily Reports**:
   - Update loan statuses
   - Report on customer interactions
   - Submit required documentation
2. **Performance Tracking**:
   - Monitor loan processing times
   - Track customer satisfaction
   - Report issues to supervisors

## Best Practices

### Data Management
1. **Regular Backups**: Ensure data is regularly backed up
2. **Data Validation**: Always validate data before processing
3. **Documentation**: Keep detailed records of all operations
4. **Security**: Follow security protocols for sensitive data

### User Management
1. **Principle of Least Privilege**: Give users only necessary permissions
2. **Regular Reviews**: Periodically review user access rights
3. **Training**: Ensure users are properly trained on their roles
4. **Monitoring**: Monitor user activity for security

### System Performance
1. **Efficient Filtering**: Use filters to improve performance
2. **Batch Processing**: Process large operations in smaller batches
3. **Regular Maintenance**: Perform regular system maintenance
4. **Monitoring**: Monitor system performance metrics

### Security
1. **Strong Passwords**: Enforce strong password policies
2. **Regular Updates**: Keep system updated with security patches
3. **Access Logging**: Monitor and log all access attempts
4. **Data Protection**: Protect sensitive customer data

## Conclusion

This usage manual provides comprehensive guidance for using the Global Credit Nova administration system. The system's role-based design ensures that users see only the features relevant to their responsibilities while maintaining security and data integrity.

For optimal results:
- Follow the step-by-step procedures outlined in this manual
- Use appropriate filters and search functions to manage large datasets efficiently
- Leverage bulk actions for processing multiple records
- Maintain regular communication with system administrators for updates and support

Regular use of these procedures will ensure efficient operation of the loan management system and provide excellent service to customers while maintaining compliance with business rules and regulations.

## Additional System Resources and Menus

### Customer and Citizen Management

#### Managing Citizens
1. Click **Citizens** in sidebar (requires appropriate customer data permissions)
2. **Creating/Editing Citizens**:
   - Personal Information: Name, surname, passport details
   - Contact Details: Phone numbers, email, addresses
   - Identification: SSN, tax ID numbers
   - Demographics: Age, gender, nationality
   - Document verification status
3. **Citizen Documents**:
   - Access via **Citizen Documents** menu
   - Upload and manage customer documents
   - Track document verification status
   - Handle document expiration alerts
4. **Citizen Phone Management**:
   - Access via **Citizen Phone** menu
   - Manage multiple phone numbers per customer
   - Set primary contact preferences
   - Track phone verification status

#### Citizen Lock Management
1. Click **Citizen Lock** in sidebar (admin access required)
2. **Locking Accounts**:
   - Search for citizen by passport or name
   - Apply account locks with reasons
   - Set lock duration and conditions
   - Document lock justification
3. **Unlocking Accounts**:
   - Review lock reasons and conditions
   - Verify unlock authorization
   - Update lock status
   - Notify relevant parties

### Vehicle and Transportation Management

#### Vehicle Information Management
1. Click **Vehicles** in sidebar (requires vehicle operations permissions)
2. **Vehicle Registration**:
   - VIN and license plate information
   - Make, model, year details
   - Technical specifications
   - Insurance information
   - Registration documentation
3. **Vehicle Brands and Types**:
   - Access **Vehicle Brands** for brand management
   - Use **Vehicle Types** for categorization
   - Configure vehicle specifications
   - Manage import company relationships

#### Vehicle Import Operations
1. Click **Vehicle Import Companies** in sidebar
2. **Import Company Management**:
   - Company registration details
   - Import licenses and permits
   - Contact information
   - Service areas and capabilities
3. **Import Loan Processing**:
   - Link vehicles to import companies
   - Process import financing
   - Handle customs documentation
   - Track import status

### Evaluation and Assessment

#### Evaluation Company Management
1. Click **Evaluation Companies** in sidebar (requires evaluation operations permissions)
2. **Company Setup**:
   - Company registration and certification
   - Service areas and specializations
   - Contact information and availability
   - Quality ratings and performance metrics
3. **Evaluation Company Users**:
   - Access via **Evaluation Company Users** menu
   - Manage evaluator accounts
   - Set permissions and access levels
   - Track evaluator performance

#### Property Evaluation Process
1. **Requesting Evaluations**:
   - Select appropriate evaluation company
   - Specify property type and location
   - Set evaluation timeline
   - Provide property documentation
2. **Managing Evaluation Results**:
   - Review evaluation reports
   - Validate evaluation accuracy
   - Handle evaluation disputes
   - Update property values

### Geographic and Reference Data

#### Geographic Management
1. **Regions**: Click **Regions** in sidebar
   - Manage geographic regions
   - Set regional parameters
   - Configure service areas
2. **Districts**: Click **Districts** in sidebar
   - District-level geographic data
   - Local administrative boundaries
   - Service coverage mapping

#### Reference Data Management
1. **Education Levels**: Manage education categories
2. **Family Status**: Configure family status options
3. **Gender**: Manage gender categories
4. **Income Sources**: Define income source types
5. **Job Titles**: Maintain job title database
6. **Loan Purposes**: Configure loan purpose categories
7. **Marital Status**: Manage marital status options
8. **Nationalities**: Maintain nationality database
9. **Occupations**: Manage occupation categories
10. **Relationships**: Define relationship types
11. **Residence Types**: Configure residence categories
12. **Social Status**: Manage social status categories

### Banking and Financial Management

#### Banking Operations
1. Click **Banks** in sidebar (requires banking operations permissions)
2. **Bank Management**:
   - Bank registration and details
   - Banking relationships
   - Account management
   - Transaction processing
3. **Bank Branches**:
   - Access via **Bank Branches** menu
   - Branch location management
   - Service capabilities
   - Contact information

#### Currency Management
1. Click **Currency** in sidebar (requires financial configuration permissions)
2. **Currency Operations**:
   - Exchange rate management
   - Currency conversion rules
   - Multi-currency loan support
   - Rate update procedures

### Collateral Management

#### Collateral Operations
1. Click **Collateral** in sidebar (requires collateral operations permissions)
2. **Collateral Registration**:
   - Asset identification and valuation
   - Legal documentation
   - Insurance requirements
   - Custody arrangements
3. **Collateral Types**:
   - Access via **Collateral Types** menu
   - Define collateral categories
   - Set valuation rules
   - Configure acceptance criteria

### Company and Organization Management

#### Company Operations
1. Click **Companies** in sidebar (requires company management permissions)
2. **Company Registration**:
   - Business registration details
   - Contact information
   - Verification status
   - Business relationships
3. **Company Users**:
   - Access via **Company Users** menu
   - Manage company representative accounts
   - Set access permissions
   - Track user activities

### Agent and Vendor Management

#### Agent Operations
1. Click **Agents** in sidebar (requires agent management permissions)
2. **Agent Management**:
   - Agent registration and certification
   - Performance tracking
   - Commission management
   - Territory assignments
3. **Agent Performance**:
   - Track loan origination
   - Monitor customer satisfaction
   - Manage agent incentives
   - Handle agent disputes

#### Vendor Management
1. Click **Vendors** in sidebar (requires vendor management permissions)
2. **Vendor Operations**:
   - Vendor registration and approval
   - Service agreements
   - Performance monitoring
   - Payment processing
3. **Vendor Relationships**:
   - Link vendors to merchants
   - Manage vendor categories
   - Track vendor performance
   - Handle vendor disputes

### Advanced Loan Management

#### Loan Explorer
1. Click **Loan Explorer** in sidebar (requires advanced permissions)
2. **Advanced Analysis**:
   - Complex loan queries
   - Performance analytics
   - Risk assessment tools
   - Portfolio analysis
3. **Data Exploration**:
   - Custom report generation
   - Trend analysis
   - Comparative studies
   - Predictive modeling

#### Loan History and Updates
1. **Loan History**: Access comprehensive loan history
2. **Loan Update History**: Track all loan modifications
3. **Historical Analysis**:
   - Review loan lifecycle
   - Track status changes
   - Audit trail maintenance
   - Compliance reporting

### QR Code and Digital Operations

#### QR Loan Management
1. Click **QR Loans** in sidebar (requires QR loan permissions)
2. **QR Loan Processing**:
   - QR code generation
   - Digital loan applications
   - Mobile payment integration
   - Real-time processing
3. **QR Owner Management**:
   - Access via **QR Owners** menu
   - Manage QR code ownership
   - Track QR usage
   - Handle QR disputes

### Specialized Loan Programs

#### Telcell Loan Operations
1. Click **Telcell Loans** in sidebar (requires Telcell permissions)
2. **Telcell-Specific Processing**:
   - Telcell customer verification
   - Mobile payment integration
   - Telcell billing integration
   - Customer service coordination

### System Configuration and Maintenance

#### Loan Types and Categories
1. **Loan Types**: Click **Loan Types** in sidebar
   - Configure loan products
   - Set interest rates and terms
   - Define eligibility criteria
   - Manage documentation requirements
2. **Loan Categories**: Organize loans into categories
   - Product grouping
   - Reporting categories
   - Risk classifications
   - Regulatory categories

#### System Rules and Configuration
1. **Affected Rules**: Track rule impacts and changes
2. **Pipe Types**: Configure system pipeline types
3. **Velox Product Categories**: Manage Velox integration categories

### Merchant Operations

#### Merchant Agent Management
1. Click **Merchant Agents** in sidebar
2. **Agent Assignment**:
   - Assign agents to merchants
   - Manage agent territories
   - Track agent performance
   - Handle agent rotations

#### Merchant Blacklist
1. Click **Merchant Blacklist** in sidebar
2. **Blacklist Management**:
   - Add merchants to blacklist
   - Document blacklist reasons
   - Review blacklist status
   - Handle blacklist appeals

### Real Estate Operations

#### Real Estate Seller Management
1. Click **Real Estate Sellers** in sidebar
2. **Seller Operations**:
   - Seller registration and verification
   - Property portfolio management
   - Transaction history
   - Performance tracking

#### Mortgage Loan Applications
1. Click **Mortgage Loan Applications** in sidebar (requires `view-reml-loan-applications`)
2. **Application Processing**:
   - Review mortgage applications
   - Property evaluation coordination
   - Documentation verification
   - Approval workflow management