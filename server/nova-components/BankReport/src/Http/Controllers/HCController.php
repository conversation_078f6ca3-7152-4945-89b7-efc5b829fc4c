<?php

namespace Globalcredit\BankReport\Http\Controllers;

use App\Exceptions\HcBankException;
use App\Exceptions\InternalErrorException;
use App\Helpers\ArrayHelper;
use App\Http\Controllers\Controller;
use App\Interfaces\IHcBankService;
use App\Interfaces\IRedisService;
use Exception;
use Laravel\Nova\Http\Requests\NovaRequest;
use Log;

class HCController extends Controller
{
    protected $redis_service;
    protected $hc_bank_service;

    public function __construct(IHcBankService $hc_bank_service, IRedisService $redis_service)
    {
        $this->hc_bank_service = $hc_bank_service;
        $this->redis_service = $redis_service;
    }

    public function saveHC(NovaRequest $request)
    {
        try {
            $json = $request->all();
            $bank_reports = $json['bankReports'];
            $bank_name = $json['bankName'];
            $failed_bank_reports = [];

            $this->hc_bank_service->setHCCredentials($bank_name);

            foreach ($bank_reports as $bank_report) {
                if ($bank_report['contract_number'] == null || $bank_report['payment_type'] == null) {
                    continue;
                }
                try {
                    $aggregated_input = $bank_report['date'].$bank_report['contract_number'].$bank_report['payment_type'].$bank_report['amount'];
                    $cached = $this->redis_service->get($aggregated_input);

                    if ($cached) {
                        Log::info('Skipping following repayment: ', ['bank_report' => ArrayHelper::pick($bank_report, ['date', 'contract_number', 'payment_type', 'amount'])]);
                        continue;
                    }

                    $transaction_id = $this->hc_bank_service->generateTransactionId();
                    $payment_id = $this->hc_bank_service->repay($bank_report['contract_number'], $bank_report['amount'], $transaction_id, $bank_report['date'], $bank_report['payment_type']);
                    $this->hc_bank_service->confirmRepay($bank_report['contract_number'], $bank_report['amount'], $transaction_id, $bank_report['date'], $payment_id, $bank_report['payment_type']);

                    $this->redis_service->set($aggregated_input, true, constants('THREE_DAYS_IN_MINUTES'));
                } catch (HcBankException $e) {
                    array_push($failed_bank_reports, $bank_report);
                    Log::error('HcBankException', ['Unable to save bank report: ' => $bank_report]);
                }
            }

            $is_success = empty($failed_bank_reports);

            return response()->json(['success' => $is_success, 'failed_bank_reports' => $failed_bank_reports]);
        } catch (Exception $e) {
            Log::error('Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }
}
