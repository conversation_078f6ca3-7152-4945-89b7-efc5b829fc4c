<?php

namespace Globalcredit\BankReport\Http\Controllers;

use App\Exceptions\InternalErrorException;
use App\Exceptions\InvalidNovaActionException;
use App\Http\Controllers\Controller;
use App\Services\BankReportParserService;
use Laravel\Nova\Http\Requests\NovaRequest;
use Log;
use Throwable;

class ParseController extends Controller
{
    public function __construct()
    {
    }

    public function parse(NovaRequest $request)
    {
        try {
            $file = $request->file('file');
            $parsed_results = (new BankReportParserService($file))->parse();

            Log::info('Parsed file', ['name' => $file->getClientOriginalName()]);

            return response()->json($parsed_results);
        } catch (InvalidNovaActionException $e) {
            Log::error('InvalidNovaActionException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (Throwable $e) {
            Log::error('Exception', ['error' => $e->getMessage()]);
            throw new InternalErrorException();
        }
    }
}
