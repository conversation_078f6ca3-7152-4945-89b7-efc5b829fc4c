package reports.loansContextReport.loanContextRepository;

import java.util.Map;

public interface ILoanContextRepository {

    public Map<Integer, Map<String, Object>> getLoanSecuritiesByParameters(Map<String, Object> parameters);

    public Map<Integer, Map<String, Object>> getLoanSecuritiesContextByParameters(Map<String, Object> parameters);

    public Map<Integer, Map<String, Object>> getLoanCitizenInfoByParameters(Map<String, Object> parameters);
}
