<?php

namespace App\Api\V2\Controllers;

use App\Api\V2\Requests\AllowPinChangeRequest;
use App\Api\V2\Requests\LoginRequest;
use App\Api\V2\Requests\PasswordRequest;
use App\Exceptions\BlacklistedException;
use App\Exceptions\InternalErrorException;
use App\Exceptions\InvalidSuuidException;
use App\Exceptions\LoginFailureException;
use App\Exceptions\Pallaton\WrongPasswordException;
use App\Exceptions\UnprocessableApplicationException;
use App\Helpers\NumberHelper;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Log;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Throwable;
use Tymon\JWTAuth\Exceptions\JWTException;
use <PERSON><PERSON>\JWTAuth\JWTAuth;

class LoginController extends Controller
{
    public function mobileLogin(LoginRequest $request, JWTAuth $JWTAuth): JsonResponse
    {
        $username = $request->username;
        $password = $request->password;

        $credentials = $this->composeCredentials($username, $password);

        return $this->auth($credentials);
    }

    private function composeCredentials(string $username, string $password)
    {
        $credentials = ['password' => $password];

        if (is_numeric($username)) {
            $credentials = array_merge($credentials, [
                'phone_number' => NumberHelper::phoneMask($username),
            ]);
        } else {
            $credentials = array_merge($credentials, [
                'email' => strtolower($username),
            ]);
        }

        return $credentials;
    }

    public function storePin(AllowPinChangeRequest $request)
    {
        $credentials = $this->composeCredentials($request->username, $request->password);

        if (!auth()->validate($credentials)) {
            throw new WrongPasswordException();
        }

        return response()->json(['success' => 'true']);
    }

    private function auth(array $credentials): JsonResponse
    {
        try {
            $token = Auth::guard('api')->attempt($credentials);

            return $this->authResponse($token);
        } catch (JWTException $e) {
            throw new InternalErrorException();
        }
    }

    private function authResponse(string $token): JsonResponse
    {
        if (!$token) {
            throw new LoginFailureException();
        }

        return response()
            ->json([
                'status' => 'ok',
                'token' => $token,
                'expires_in' => Auth::guard('api')->factory()->getTTL() * 60,
            ]);
    }

    public function registerUser(PasswordRequest $request)
    {
        try {
            $security_service = resolve('App\Interfaces\ISecurityService');
            $loan_security = $security_service->resolveLoanSecurity();

            $payload = [
                'email' => $loan_security->email,
                'phone_number' => $loan_security->phone_number,
                'password' => $request->get('password'),
            ];

            $auth_service = resolve('App\Services\Pallaton\AuthService');
            $auth_service->registerUser($payload, $loan_security);

            return response()
                ->json([
                    'status' => 'ok',
                ]);
        } catch (InvalidSuuidException | BlacklistedException | LoginFailureException | UnprocessableApplicationException $e) {
            Log::warning('Register user, '.get_class($e), ['error' => $e->getMessage()]);
            throw $e;
        } catch (HttpException $e) {
            Log::error('Register user, HttpException', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw $e;
        } catch (Throwable $e) {
            Log::error('Register user, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }
}
