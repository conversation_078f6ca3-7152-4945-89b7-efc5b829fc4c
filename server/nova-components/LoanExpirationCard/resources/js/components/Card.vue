<template>
  <card class="flex flex-col items-center justify-center expiration-date-card">
    <div class="px-3 py-3">
      <div
        class="expiration-days-section block mx-auto button-block text-center"
      >
        <h4 class="text-center text-80 font-light">
          {{ __('Change Expiration Days') }}
        </h4>
        <calendar class="calendar"></calendar>
        <vue-datepicker
          :minDate="MIN_DATE"
          :placeholder="__('Date range')"
          @reset="handleDateReset"
          @update="handleDateChange"
          :format="DATE_FORMAT"
          ref="dateRef"
          class="date"
        />
        <input
          type="number"
          min="1"
          v-model="days"
          :placeholder="__('Days')"
          class="days rounded py-3 px-4"
        />
      </div>
    </div>
    <div class="px-3 py-3 submit-button">
      <button
        :disabled="disabled"
        class="btn btn-default btn-primary"
        v-on:click="handleSubmit()"
      >
        {{ __('Submit') }}
      </button>
      <p v-if="hasError" class="text-xs mt-2 text-danger">
        {{ firstError.code || firstError }}
      </p>
    </div>
  </card>
</template>

<script>
import { Errors } from 'laravel-nova';

const DATE_FORMAT = 'DD/MM/YYYY';
const MIN_DATE = '01/01/2021';

export default {
  name: 'App',
  props: ['card'],

  data() {
    return {
      disabled: false,
      dates: {},
      days: '',
      changeExpDaysErrors: new Errors(),
      DATE_FORMAT,
      MIN_DATE,
      logo: '../../assets/images/calendar.svg',
    };
  },
  watch: {
    days() {
      this.disabled = false;
    },
  },
  methods: {
    async handleSubmit() {
      this.disabled = true;
      this.changeExpDaysErrors = new Errors();

      try {
        await Nova.request().post(
          `/nova-vendor/loan-expiration-card/loan/expiration-date`,
          {
            dates: this.dates,
            days: this.days,
          }
        );

        this.dates = {};
        this.days = '';
        this.$refs.dateRef.reset();
        this.disabled = false;
        this.$toasted.show(this.__('The action ran successfully!'), {
          type: 'success',
        });
      } catch (error) {
        this.changeExpDaysErrors = new Errors(error.response.data.errors);
      }
    },

    handleDateChange(payload) {
      this.dates = { ...payload };
      this.disabled = false;
    },

    handleDateReset() {
      this.dates = {};
      this.disabled = false;
    },
  },

  computed: {
    errorClasses() {
      return this.hasError ? [this.errorClass] : [];
    },
    hasError() {
      return (
        this.changeExpDaysErrors.has('dates') ||
        this.changeExpDaysErrors.has('days')
      );
    },
    firstError() {
      if (this.hasError) {
        return (
          this.changeExpDaysErrors.first('dates') ||
          this.changeExpDaysErrors.first('days')
        );
      }
    },
  },
};
</script>
