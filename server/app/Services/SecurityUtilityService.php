<?php

namespace App\Services;

use App\Exceptions\CreditLine\DuplicateCredentialException;
use App\Exceptions\CreditLine\DuplicateUserException;
use App\Exceptions\CRM\CitizenNotFoundException;
use App\Exceptions\CRM\CitizenUnblockedException;
use App\Exceptions\Pallaton\DuplicateUserException as ProfileExistsException;
use App\Exceptions\UnprocessableApplicationException;
use App\Models\Blacklist;
use App\Models\BlacklistReason;
use App\Models\CitizenLock;
use App\Models\CreditLine\Vendor;
use App\Models\Loan;
use App\Models\LoanSecurity;
use App\Models\Pallaton\Profile;
use App\Models\PredefinedRealEstate;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class SecurityUtilityService
{
    public static function isCitizenBlocked($ssn, $phone_number, $vin, $credit_card, $loan_type_id)
    {
        return Blacklist::isRecordExist(
            $ssn,
            $phone_number,
            $vin,
            $credit_card,
            $loan_type_id
        );
    }

    public static function isCitizenLocked($ssn, $phone_number)
    {
        return CitizenLock::isRecordExist(
            $ssn,
            $phone_number
        );
    }

    /**
     * Get active blacklist reason titles for a citizen.
     *
     * @return string[]
     */
    public static function getActiveBlacklistReasons(string $ssn, string $phone_number): array
    {
        return Blacklist::with('block_reason')
            ->forCitizen($ssn, $phone_number)
            ->active()
            ->get()
            ->pluck('block_reason.title')
            ->unique()
            ->all();
    }

    /**
     * Returns the first matched strict reason title that should strictly block registration, or null if none.
     */
    public static function getStrictRegistrationBlockReason(string $ssn, string $phone_number, array $strict_reasons = null): ?string
    {
        $strict_reasons = $strict_reasons ?? [
            constants('BLACKLIST_REASONS.RECOGNITION_NOT_SUCCESSFULL'),
            constants('BLACKLIST_REASONS.SMS_VERIFICATION_ATTEMPTS'),
        ];

        $active_reasons = self::getActiveBlacklistReasons($ssn, $phone_number);
        foreach ($active_reasons as $reason) {
            if (in_array($reason, $strict_reasons, true)) {
                return $reason;
            }
        }
        // If we have active reasons but none match strict list, log it for traceability
        if (!empty($active_reasons)) {
            Log::info('getStrictRegistrationBlockReason, Citizen blacklisted but has non-strict blacklist reasons for registration', [
                'ssn' => $ssn,
                'phone_number' => $phone_number,
                'active_reasons' => $active_reasons,
                'strict_reasons' => $strict_reasons,
            ]);
        }

        return null;
    }

    /**
     * Enforce registration strict block: log and throw with a specific code when needed.
     */
    public static function ensureRegistrationNotStrictlyBlocked($ssn, $phone_number): void
    {
        $strict_reason = self::getStrictRegistrationBlockReason($ssn, $phone_number);
        if (is_null($strict_reason)) {
            return; // allow registration to proceed
        }

        // Map SMS_VERIFICATION_ATTEMPTS to a dedicated error code for UI-specific handling
        if ($strict_reason == constants('BLACKLIST_REASONS.SMS_VERIFICATION_ATTEMPTS')) {
            $error_code = config('error_codes.SMS_VERIFICATION_ATTEMPTS');
        }

        Log::info('ensureRegistrationNotStrictlyBlocked, Citizen Blocked for registration', [
            'ssn' => $ssn,
            'phone_number' => $phone_number,
            'reason' => $strict_reason,
        ]);

        throw new UnprocessableApplicationException('', $error_code ?? null);
    }

    public static function isLockable($loan_type_id)
    {
        return !self::isOwlLoan($loan_type_id);
    }

    public static function isBlockable($loan_type_id)
    {
        return $loan_type_id != constants('LOAN_TYPES.VLX');
    }

    public static function isUserExist($ssn, $phone_number)
    {
        if (User::isCitizenUserExist($ssn)) {
            Log::info('User with parameters Exist', ['ssn' => $ssn]);

            throw new DuplicateUserException();
        }
        if (User::isPhoneExist($phone_number)) {
            Log::info('User with parameters Exist', ['phone_number' => $phone_number]);

            throw new DuplicateCredentialException();
        }
    }

    public static function unblockCitizenBySsn($ssn, $loan_type_id, $origin = null, $unblock_reason_id = null)
    {
        $citizen = Blacklist::where('social_card_number', $ssn)->where('loan_type_id', $loan_type_id)->latest('created_at')->first();

        if (!$citizen) {
            throw new CitizenNotFoundException();
        }

        if ($citizen->isUnblocked()) {
            throw new CitizenUnblockedException();
        }

        if (is_null($unblock_reason_id)) {
            // If we are not getting appropriate unblock_reason_id, then we needed to set it as UNKNOWN
            $unblock_reason = BlacklistReason::unblockReasons()
                ->where('title', constants('BLACKLIST_REASONS.UNKNOWN'))
                ->first();
        }

        $citizen->expiration = Carbon::now()->subDay();
        $citizen->expiration_origin = $origin;
        $citizen->unblock_reason_id = $unblock_reason->id;
        $citizen->save();

        Log::info('unblockCitizenBySsn', ['ssn' => $ssn, 'loan_type_id' => $loan_type_id, 'origin' => $origin, 'unblock_reason_id' => $unblock_reason->id]);
    }

    public static function resolveLoanType($suuid)
    {
        $loan_security = LoanSecurity::where('suuid', $suuid)->first();

        return $loan_security ? $loan_security->loan_type_id : null;
    }

    public static function isOwlLoan($loan_type_id)
    {
        return in_array(
            $loan_type_id,
            [
                constants('LOAN_TYPES.OIDL'),
                constants('LOAN_TYPES.OTCL'),
                constants('LOAN_TYPES.OEPL'),
                constants('LOAN_TYPES.OUPL'),
                constants('LOAN_TYPES.OFSL'),
            ]
        );
    }

    public static function isTelcellVehicle($loan_type_id, $referrer_source)
    {
        return $loan_type_id == constants('LOAN_TYPES.OVL') && strtoupper($referrer_source) == constants('REFERRER_SOURCES.TELCELL');
    }

    public static function isOVPL($loan_type_id, $loan_subtype_id)
    {
        return $loan_type_id == constants('LOAN_TYPES.OVL') && $loan_subtype_id == constants('LOAN_SUBTYPES.OVPL');
    }

    public static function isOVTL($loan_type_id, $loan_subtype_id)
    {
        return $loan_type_id == constants('LOAN_TYPES.OVL') && $loan_subtype_id == constants('LOAN_SUBTYPES.OVTL');
    }

    public static function isOVIL($loan_type_id, $loan_subtype_id)
    {
        return $loan_type_id == constants('LOAN_TYPES.OVL') && $loan_subtype_id == constants('LOAN_SUBTYPES.OVIL');
    }

    public static function isREML($loan_type_id)
    {
        return $loan_type_id == constants('LOAN_TYPES.REML');
    }

    public static function isOBL($loan_type_id)
    {
        return $loan_type_id == constants('LOAN_TYPES.OBL');
    }

    public static function getREMLSubtypeId($re_token, $re_type)
    {
        if (isset($re_token) || (strtoupper($re_type) === constants('REAL_ESTATE_MARKET_TYPES.PRIMARY'))
        ) {
            return constants('LOAN_SUBTYPES.PREML');
        }

        return constants('LOAN_SUBTYPES.SREML');
    }

    public static function isPREML($re_token, $re_type = null)
    {
        return isset($re_token) || (strtoupper($re_type) === constants('REAL_ESTATE_MARKET_TYPES.PRIMARY'));
    }

    public static function getWalletKey($loan_type_id)
    {
        if ($loan_type_id == constants('LOAN_TYPES.OIDL')) {
            return 'idram';
        }

        if ($loan_type_id == constants('LOAN_TYPES.OTCL')) {
            return 'telcell';
        }

        if ($loan_type_id == constants('LOAN_TYPES.OEPL')) {
            return 'easypay';
        }

        if ($loan_type_id == constants('LOAN_TYPES.OUPL')) {
            return 'upay';
        }

        if ($loan_type_id == constants('LOAN_TYPES.OFSL')) {
            return 'fastshift';
        }

        return '';
    }

    public static function isProfileExists($ssn, $phone_number, $email)
    {
        if (Profile::isCitizenProfileExists($ssn, $phone_number, $email)) {
            Log::info('Profile with parameters exists', ['ssn' => $ssn, 'phone_number' => $phone_number, 'email' => $email]);

            throw new ProfileExistsException();
        }
    }

    public static function getDeveloperCompany($token)
    {
        $real_estate = PredefinedRealEstate::whereToken($token)
            ->whereIn('status', [PredefinedRealEstate::FREE, PredefinedRealEstate::RESERVED])
            ->whereDisabled(false)
            ->first();

        if ($real_estate) {
            return $real_estate->developer_company_id;
        }

        return null;
    }

    public static function isBanali($source): bool
    {
        return strtoupper($source) === constants('REFERRER_SOURCES.BANALI');
    }

    public static function getExistingBNPLLoanSecurity($value)
    {
        return LoanSecurity::whereSsn($value)
            ->where('loan_type_id', constants('LOAN_TYPES.BNPL'))
            ->whereHas('loan', function ($q) {
                $q->whereStatus(Loan::CONFIRMED);
                $q->whereLoanTypeId(constants('LOAN_TYPES.BNPL'));
            })->latest()->first();
    }

    public static function getVendor()
    {
        $client_identifier = request()->header('Client-Identifier');

        return Vendor::getVendor($client_identifier);
    }

    public static function isPayLaterVendor($vendor_type): bool
    {
        return $vendor_type == constants('VENDOR_TYPES.PAY_LATER');
    }

    public static function isInternalBNPLVendor($vendor_type): bool
    {
        return $vendor_type == constants('VENDOR_TYPES.INTERNAL_BNPL');
    }

    public static function isArmedVendor($vendor_type): bool
    {
        return $vendor_type == constants('VENDOR_TYPES.ARMED');
    }

    public static function isBNPLVendor($vendor_type): bool
    {
        return $vendor_type == constants('VENDOR_TYPES.BNPL');
    }

    public static function getExistingOblLoanSecurity($value)
    {
        return LoanSecurity::whereSuuid($value)
            ->where('loan_type_id', constants('LOAN_TYPES.OBL'))
            ->whereHas('loan', function ($q) {
                $q->whereStatus(Loan::CONFIRMED);
            })->first();
    }
}
