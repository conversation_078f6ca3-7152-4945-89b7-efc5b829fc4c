<?php

namespace Globalcredit\ActionButton;

use <PERSON><PERSON>\Nova\Fields\Field;

class ActionButton extends Field
{
    /**
     * The field's component.
     *
     * @var string
     */
    public $component = 'action-button';

    public $label = null;

    public $indexName = null;

    public $indexAlign = 'right';

    public $visible = true;

    public function resolve($resource, $attribute = null)
    {
        parent::resolve($resource, $attribute);

        $this->withMeta([
            'label' => $this->label,
            'indexName' => $this->indexName,
            'indexAlign' => $this->indexAlign,
            'visible' => $this->visible,
        ]);
    }

    public function visible($visible)
    {
        $this->visible = $visible;

        return $this;
    }
}
