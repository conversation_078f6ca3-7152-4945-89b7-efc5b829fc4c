<template>
  <div class="overflow-hidden mt-8">
    <formulate-form
      class="form"
      @submit="handleCheckLoan"
      @failed-validation="handleValidationFailed"
      :form-errors="formError"
      v-model="formPayload"
      ref="formRef"
    >
      <vue-tabs
        active-tab-color="#4099de"
        active-text-color="white"
        centered
        ref="tabsRef"
      >
        <v-tab :title="__('Seller Info')" v-if="!isPREML">
          <seller-info :seller="seller" :isFieldDisabled="isFieldDisabled" />
        </v-tab>

        <v-tab :title="__('Mortgage Subject Info')">
          <mortgage-subject-info
            :mortgageSubject="mortgageSubject"
            :realEstateAddress="field.realEstateAddress"
            :isFieldDisabled="isFieldDisabled"
          />
        </v-tab>

        <v-tab :title="__('Attach Documents')">
          <div class="upload-documents py-6 border-b border-40">
            <label class="inline-block">{{
              `${__('Ownership Certificate')}`
            }}</label>
            <media-view
              :resourceId="resourceId"
              :processing="allowProcessing"
              fileType="image/*"
              documentType="ownership_certificate"
              @mediaUploaded="setIsOwnershipCertUploaded"
              :preventParentSubmit="true"
            />
            <p
              v-if="allowUploadOwnershipCert"
              class="text-xs mt-2 text-danger text-center"
            >
              {{ __('Required Field') }}
            </p>
          </div>
          <div class="upload-documents py-6 border-b border-40">
            <label class="inline-block">{{
              `${__('Evaluation Report')}`
            }}</label>
            <media-view
              :resourceId="resourceId"
              :processing="allowProcessing"
              fileType="application/pdf"
              documentType="evaluation_report"
              @mediaUploaded="setIsEvaluationReportUploaded"
              :preventParentSubmit="true"
            />
            <p
              v-if="allowUploadEvaluationReport"
              class="text-xs mt-2 text-danger text-center"
            >
              {{ __('Required Field') }}
            </p>
          </div>
          <div
            class="upload-documents py-6 border-b border-40"
            v-if="!isECREMLAdmin"
          >
            <label class="inline-block">{{
              `${__('Unified Cadastral Ref')}`
            }}</label>
            <media-view
              :resourceId="resourceId"
              :processing="isGCREMLSubAdmin"
              fileType="image/*,application/pdf"
              documentType="unified_cadastral_ref"
              @mediaUploaded="setIsUnifiedCadastralRefUploaded"
              :preventParentSubmit="true"
            />
            <p
              v-if="allowUploadCadastralRef"
              class="text-xs mt-2 text-danger text-center"
            >
              {{ __('Required Field') }}
            </p>
          </div>
          <div class="upload-documents py-6 border-b border-40">
            <label class="inline-block">{{ __('Other Documents') }}</label>
            <media-view
              :resourceId="resourceId"
              :processing="!isGCREMLViewer"
              fileType="image/*,application/pdf"
              documentType="media"
              :preventParentSubmit="true"
            />
          </div>
        </v-tab>

        <v-tab :title="__('Other')" v-if="isGCREMLAdmin">
          <div class="row flex flex-wrap other-tab">
            <formulate-input
              type="number"
              name="sremlMortgageContractQuantity"
              :label="__('Quantity')"
              :placeholder="__('Quantity')"
              :help="__('Mortgage Contract Examples Quantity')"
              v-model="field.sremlMortgageContractQuantity"
            />
            <formulate-input
              type="textarea"
              name="sremlMortgageContractNotes"
              :wrapper-class="['additional-notes-info']"
              :label="__('Additional Notes and Info')"
              :placeholder="__('Additional Notes and Info')"
              :help="__('Mortgage Contract Helper')"
              v-model="field.sremlMortgageContractNotes"
            />
          </div>
        </v-tab>

        <div class="other-notes">
          <formulate-input
            type="textarea"
            :name="notesOwner"
            :label="__('Other Notes')"
            :placeholder="__('Other Notes')"
            v-model="notes"
          />
        </div>
      </vue-tabs>

      <FormulateErrors />

      <div class="pr-6 py-3 flex justify-end" v-if="canCheck">
        <div class="reml-requirments-container">
          <input
            type="checkbox"
            name="reml-requirments"
            id="reml-requirments"
            v-model="requirementsChecked"
          />
          <label
            for="reml-requirments"
            class="inline-block text-80 pt-2 leading-tight"
          >
            <a :href="REAL_ESTATE_REQUIREMENTS_PDF_URL" target="_blank">{{
              __('Meets Lending Terms')
            }}</a>
          </label>
        </div>

        <div class="ml-auto">
          <button
            type="submit"
            class="btn btn-default btn-primary inline-flex items-center relative"
            :disabled="isCheckedDisabled || loading"
          >
            {{ __('Checked') }}
          </button>
        </div>
      </div>

      <div
        class="pr-6 py-3 flex mr-0 mb-6 justify-end border-b-2 border-60"
        v-if="canPledge"
      >
        <div>
          <button
            type="submit"
            class="btn btn-default btn-outline inline-flex items-center relative"
            @click.prevent="handleSaveChanges()"
            :disabled="loading"
          >
            {{ __('Save') }}
          </button>
        </div>
        <div class="ml-4">
          <button
            type="submit"
            class="btn btn-default btn-outline inline-flex items-center relative"
            @click.prevent="handleRegenerateDocuments()"
            :disabled="loading"
          >
            {{ __('Regenerate Documents') }}
          </button>
        </div>
      </div>

      <div class="pr-6 py-3 flex justify-end" v-if="canReview">
        <div class="ml-4">
          <button
            type="submit"
            class="btn btn-default btn-primary inline-flex items-center relative"
            @click.prevent="handleReviewLoan()"
            :disabled="loading"
          >
            {{ __('Submit') }}
          </button>
        </div>
        <div class="ml-4">
          <button
            type="submit"
            class="btn btn-default btn-danger inline-flex items-center relative"
            @click.prevent="handleRejectLoan()"
            :disabled="loading"
          >
            {{ __('Reject') }}
          </button>
        </div>
      </div>

      <div class="pr-6 py-3 flex mr-0 justify-end" v-if="canConfirm">
        <button
          type="submit"
          class="btn btn-default btn-primary inline-flex items-center relative"
          @click.prevent="handleConfirmLoan()"
          :disabled="loading"
        >
          {{ __('Provide') }}
        </button>
      </div>

      <div class="pr-6 py-3 flex justify-end" v-if="canPledge">
        <div class="ml-4">
          <button
            type="submit"
            class="btn btn-default btn-primary inline-flex items-center relative"
            @click.prevent="openConfirmModal()"
            :disabled="loading"
          >
            {{ __('Approve Loan') }}
          </button>
        </div>
        <div class="ml-4">
          <button
            type="submit"
            class="btn btn-default btn-danger inline-flex items-center relative"
            @click.prevent="handleRejectLoan()"
            :disabled="loading"
          >
            {{ __('Reject') }}
          </button>
        </div>
      </div>

      <loading v-if="loading" large="true" class="flex m-auto" />
    </formulate-form>
    <confirm-modal
      v-if="showModal"
      @close="hideConfirmModal"
      @confirm="confirmed"
    />
  </div>
</template>

<script>
import errorFocus from '../helpers/errorFocus';
import moment from 'moment/moment';

const REAL_ESTATE_REQUIREMENTS_PDF_URL =
  'http://cashme.am/home_screen_pdfs/real_estate_requirements.pdf';
const DATE_FORMAT = 'YYYY-MM-DD';

export default {
  props: ['resourceName', 'resourceId', 'field'],

  data: () => ({
    loading: false,
    showModal: false,
    isEvaluationReportUploaded: null,
    isOwnershipCertUploaded: null,
    isUnifiedCadastralRefUploaded: null,
    requirementsChecked: false,
    formError: [],
    formPayload: {},
    notes: null,
    seller: {
      firstName: null,
      lastName: null,
      middleName: null,
      phoneNumber: null,
      birthday: null,
      passportNumber: null,
      givenDate: null,
      from: null,
      registrationAddress: null,
      bank: null,
      bankAccount: null,
    },
    mortgageSubject: {
      evaluatedPrice: null,
      evaluatedDate: null,
      subjectType: null,
      subjectAddress: null,
      subjectDescription: null,
      ownershipCertGivenDate: null,
      ownershipCertNumber: null,
      ownershipCertPassword: null,
      unifiedCadastralRefGivenDate: null,
      unifiedCadastralRefNumber: null,
      unifiedCadastralRefPassword: null,
    },
    REAL_ESTATE_REQUIREMENTS_PDF_URL,
  }),

  computed: {
    isCheckedDisabled() {
      return !this.requirementsChecked || this.loading;
    },

    canCheck() {
      return this.isECREMLAdmin && this.isLoanProcessing;
    },

    canReview() {
      return this.isGCREMLSubAdmin && this.isLoanProcessed;
    },

    canPledge() {
      return this.isGCREMLAdmin && this.isLoanReview;
    },

    canConfirm() {
      return this.isGCREMLAdmin && this.isLoanPledged;
    },

    notesOwner() {
      if (this.isECREMLAdmin) {
        return 'ecAdminNotes';
      } else if (this.isGCREMLAdmin) {
        return 'gcAdminNotes';
      } else if (this.isGCREMLSubAdmin) {
        return 'gcSubAdminNotes';
      }
    },

    isFieldDisabled() {
      return this.field.isGCREMLSubAdmin;
    },

    isGCREMLSubAdmin() {
      return this.field.isGCREMLSubAdmin;
    },

    isECREMLAdmin() {
      return this.field.isECREMLAdmin;
    },

    isGCREMLAdmin() {
      return this.field.isGCREMLAdmin;
    },

    isPREML() {
      return this.field.isPREML;
    },

    isGCREMLViewer() {
      return this.field.isGCREMLViewer;
    },

    isLoanProcessing() {
      return (
        this.field.loanStatus === Nova.config.constants.LOAN_STATUS.PROCESSING
      );
    },

    isLoanProcessed() {
      return (
        this.field.loanStatus === Nova.config.constants.LOAN_STATUS.PROCESSED
      );
    },

    isLoanReview() {
      return this.field.loanStatus === Nova.config.constants.LOAN_STATUS.REVIEW;
    },

    isLoanPledged() {
      return (
        this.field.loanStatus === Nova.config.constants.LOAN_STATUS.PLEDGED
      );
    },

    allowProcessing() {
      return (
        (this.isLoanProcessing || (this.isPREML && this.isGCREMLAdmin)) &&
        !this.isGCREMLViewer
      );
    },

    allowUploadEvaluationReport() {
      return (
        !this.isEvaluationReportUploaded &&
        !this.isPREML &&
        !this.isGCREMLViewer
      );
    },

    allowUploadCadastralRef() {
      return !this.isUnifiedCadastralRefUploaded && !this.isGCREMLViewer;
    },

    allowUploadOwnershipCert() {
      return (
        !this.isOwnershipCertUploaded && !this.isPREML && !this.isGCREMLViewer
      );
    },
  },

  mounted() {
    this.$nextTick(() => {
      this.seller = { ...this.seller, ...this.camelizeKeys(this.field.seller) };
      this.seller.phoneNumber =
        this.seller.phoneNumber && this.seller.phoneNumber.slice(-8);

      this.mortgageSubject = {
        ...this.mortgageSubject,
        ...this.camelizeKeys(this.field.mortgageSubject),
      };
      this.mortgageSubject.subjectDescription = this.field.subjectDescription;

      if (this.isPREML) {
        this.setPremlEvaluatedPriceAndDate();
      }

      this.setNotesValue();
    });
  },

  methods: {
    async handleSaveChanges() {
      try {
        if (await this.hasFormError()) {
          return;
        }

        this.formError = [];
        this.loading = true;

        await Nova.request().post(
          `/nova-vendor/evaluation-company-form/loan/${this.resourceId}/save`,
          this.decamelizeKeys(this.formPayload)
        );

        this.finalize(false);
      } catch (error) {
        const message =
          error.response.data.message || this.__('Something went wrong');
        this.$toasted.show(message, { type: 'error' });
      } finally {
        this.loading = false;
      }
    },

    async handleRegenerateDocuments() {
      try {
        this.loading = true;

        await Nova.request().post(
          `/nova-vendor/evaluation-company-form/loan/${this.resourceId}/regenerate-documents`
        );

        this.finalize(false);
        window.location.reload();
      } catch (error) {
        const message =
          error.response.data.message || this.__('Something went wrong');
        this.$toasted.show(message, { type: 'error' });
      } finally {
        this.loading = false;
      }
    },

    async handleCheckLoan() {
      try {
        if (await this.hasFormError()) {
          return;
        }

        this.formError = [];
        this.loading = true;
        this.formPayload.phoneNumber = this.formPayload.phoneNumber.replace(
          /[() ]/g,
          ''
        );

        await Nova.request().post(
          `/nova-vendor/evaluation-company-form/loan/${this.resourceId}/check`,
          this.decamelizeKeys(this.formPayload)
        );

        this.finalize();
      } catch (error) {
        const message =
          error.response.data.message || this.__('Something went wrong');
        this.$toasted.show(message, { type: 'error' });
      } finally {
        this.loading = false;
      }
    },

    async handleReviewLoan() {
      try {
        if (await this.hasFormError()) {
          return;
        }

        this.formError = [];
        this.loading = true;

        await Nova.request().put(
          `/nova-vendor/evaluation-company-form/loan/${this.resourceId}/review`,
          this.decamelizeKeys(this.formPayload)
        );

        this.finalize();
      } catch (error) {
        const message =
          error.response.data.message || this.__('Something went wrong');
        this.$toasted.show(message, { type: 'error' });
      } finally {
        this.loading = false;
      }
    },

    async handleRejectLoan() {
      try {
        this.loading = true;

        await Nova.request().put(
          `/nova-vendor/evaluation-company-form/loan/${this.resourceId}/reject`
        );

        this.finalize();
      } catch (error) {
        const message =
          error.response.data.message || this.__('Something went wrong');
        this.$toasted.show(message, { type: 'error' });
      } finally {
        this.loading = false;
      }
    },

    async handlePledgeLoan() {
      try {
        this.loading = true;
        this.formPayload.phoneNumber =
          this.formPayload.phoneNumber &&
          this.formPayload.phoneNumber.replace(/[() ]/g, '');

        await Nova.request().post(
          `/nova-vendor/evaluation-company-form/loan/${this.resourceId}/pledge`,
          this.decamelizeKeys(this.formPayload)
        );

        this.finalize();
      } catch (error) {
        const message =
          error.response.data.message || this.__('Something went wrong');
        this.$toasted.show(message, { type: 'error' });
      } finally {
        this.loading = false;
      }
    },

    async handleConfirmLoan() {
      try {
        this.loading = true;

        await Nova.request().put(
          `/nova-vendor/evaluation-company-form/loan/${this.resourceId}/confirm`
        );

        this.finalize();
      } catch (error) {
        const message =
          error.response.data.message || this.__('Something went wrong');
        this.$toasted.show(message, { type: 'error' });
      } finally {
        this.loading = false;
      }
    },

    finalize(routeRedirect = true) {
      this.$toasted.show(this.__('The action ran successfully!'), {
        type: 'success',
      });

      routeRedirect && this.$router.push('/resources/mortgage-loans');
    },

    handleValidationFailed() {
      this.$refs.tabsRef.findTabAndActivate(0);
      this.formError = [this.__('Please Fill All Required Fields')];

      errorFocus();
    },

    openConfirmModal() {
      let isFormIncompletelyFilled = Object.values(this.formPayload).some(
        value => value === null
      );

      if (isFormIncompletelyFilled) {
        this.showModal = true;
      } else {
        this.handlePledgeLoan();
      }
    },

    hideConfirmModal() {
      this.showModal = false;
    },

    confirmed() {
      this.handlePledgeLoan();
      this.hideConfirmModal();
    },

    setNotesValue() {
      if (this.isECREMLAdmin) {
        this.notes = this.mortgageSubject.ecAdminNotes;
      } else if (this.isGCREMLAdmin) {
        this.notes = this.mortgageSubject.gcAdminNotes;
      } else if (this.isGCREMLSubAdmin) {
        this.notes = this.mortgageSubject.gcSubAdminNotes;
      }
    },

    setPremlEvaluatedPriceAndDate() {
      if (!this.mortgageSubject.evaluatedPrice) {
        this.mortgageSubject.evaluatedPrice = this.field.realEstatePrice;
      }
      if (!this.mortgageSubject.evaluatedDate) {
        this.mortgageSubject.evaluatedDate = moment(
          this.field.loanSignDate.date
        ).format(DATE_FORMAT);
      }
    },

    setIsEvaluationReportUploaded(uploadedCount) {
      if (uploadedCount && uploadedCount > 0) {
        this.isEvaluationReportUploaded = true;
      } else {
        this.isEvaluationReportUploaded = null;
      }
    },

    setIsUnifiedCadastralRefUploaded(uploadedCount) {
      if (uploadedCount && uploadedCount > 0) {
        this.isUnifiedCadastralRefUploaded = true;
      } else {
        this.isUnifiedCadastralRefUploaded = null;
      }
    },

    setIsOwnershipCertUploaded(uploadedCount) {
      if (uploadedCount && uploadedCount > 0) {
        this.isOwnershipCertUploaded = true;
      } else {
        this.isOwnershipCertUploaded = null;
      }
    },

    async hasFormError() {
      const formHasValidationErrors = await this.$refs.formRef.hasValidationErrors();

      if (this.isGCREMLAdmin && (!formHasValidationErrors || this.isPREML)) {
        return false;
      }

      if (
        this.isECREMLAdmin &&
        this.isEvaluationReportUploaded &&
        this.isOwnershipCertUploaded
      ) {
        return false;
      }

      if (this.isGCREMLSubAdmin && this.isUnifiedCadastralRefUploaded) {
        return false;
      }

      this.formError = [this.__('Please Fill All Required Fields')];
      errorFocus();

      return true;
    },

    moment: function() {
      return moment();
    },

    camelizeKeys(data) {
      return _.reduce(
        data,
        (acc, value, key) => {
          acc[_.camelCase(key)] = value;
          return acc;
        },
        {}
      );
    },

    decamelizeKeys(data) {
      return _.reduce(
        data,
        (acc, value, key) => {
          acc[_.snakeCase(key)] = value;
          return acc;
        },
        {}
      );
    },
  },
};
</script>
