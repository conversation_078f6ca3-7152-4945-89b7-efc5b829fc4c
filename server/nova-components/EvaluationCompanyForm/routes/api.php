<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Tool API Routes
|--------------------------------------------------------------------------
|
| Here is where you may register API routes for your tool. These routes
| are loaded by the ServiceProvider of your tool. You're free to add
| as many additional routes to this file as your tool may require.
|
*/

Route::group(['prefix' => 'loan/{loan_id}', 'namespace' => 'Globalcredit\EvaluationCompanyForm\Http\Controllers'], function () {
    Route::post('save', 'EvaluationCompanyFormController@save');
    Route::post('regenerate-documents', 'EvaluationCompanyFormController@regenerateDocuments');
    Route::post('check', 'EvaluationCompanyFormController@checkLoan');
    Route::put('review', 'EvaluationCompanyFormController@reviewLoan');
    Route::post('pledge', 'EvaluationCompanyFormController@pledgeLoan');
    Route::put('reject', 'EvaluationCompanyFormController@rejectLoan');
    Route::put('confirm', 'EvaluationCompanyFormController@confirmLoan');
});
