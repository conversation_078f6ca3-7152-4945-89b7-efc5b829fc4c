<?php

namespace Globalcredit\ApproveVehicleLoan\Http\Controllers;

use App\Exceptions\InvalidNovaActionException;
use App\Exceptions\LoanNotFoundException;
use App\Exceptions\PledgeExistingLenderException;
use App\Exceptions\PledgeMissingLenderException;
use App\Exceptions\PledgeMultipleLendersException;
use App\Exceptions\PledgeVehicleNotFoundException;
use App\Interfaces\IEkengService;
use App\Interfaces\IVehicleService;
use App\Jobs\SendConfirmationSMS;
use App\Models\HC\HcGCCRDTCODE;
use App\Models\Loan;
use App\Models\LoanDocument;
use App\Services\LoanDocumentServiceOVL;
use App\Strategies\OvlFlow\OvlFlowStrategyContext;
use Carbon\Carbon;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Log;
use Laravel\Nova\Http\Requests\NovaRequest;

class ApproveVehicleLoanController extends Controller
{
    public function getVehicleCondition(NovaRequest $request)
    {
        $loan = Loan::find($request->id);

        $vehicle_condition = $loan->mortgage->vehicle_condition;

        if ($vehicle_condition) {
            $vehicle_condition->is_mileage_acceptable = $this->isMileageAcceptable($loan, $vehicle_condition->mileage);
        }

        return response()->json($vehicle_condition);
    }

    protected function isMileageAcceptable($loan, $mileage)
    {
        if ($loan->isOVIL()) {
            return true;
        }

        $release_date = Carbon::createFromFormat('Y', $loan->vehicle->released);

        $diff_in_years = Carbon::now()->diffInYears($release_date);

        return ($mileage / $diff_in_years) < constants('ACCEPTABLE_MAX_MILEAGE');
    }

    public function storeVehicleCondition(NovaRequest $request)
    {
        $loan = Loan::find($request->loan_id);

        if ($request->mileage !== '') {
            $vehicle_condition = $loan->mortgage->vehicle_condition()->updateOrCreate(
                ['mortgage_id' => $loan->mortgage->id],
                $request->all()
            );
        }

        $rules = [
            'mileage' => 'required|integer',
        ];
        // in the case of OVIL, we do not need Mileage validation
        if (!$loan->isOVIL()) {
            $rules['mileage'] .= '|vehicle_mileage:'.$loan->vehicle->released;
        }

        $request->validate(
            $rules,
            [
                'mileage.required' => lang('nova.exceptions.RequiredField'),
                'vehicle_mileage' => lang('nova.exceptions.InvalidMileageException'),
            ]
        );

        return response()->json($vehicle_condition);
    }

    /**
     * @usage calls after admin final confirmation (Տրամադրել btn)
     */
    public function confirm(NovaRequest $request)
    {
        $loan = Loan::with('vehicle', 'citizen')
            ->find($request->id);
        // We need to make sure the Loan is not Closed in HC, especially OVIL flow cases
        if (HcGCCRDTCODE::isCreditClosed($loan->contract_number)) {
            Log::info('Loan is closed in HC', ['contract_number' => $loan->contract_number]);

            throw new InvalidNovaActionException(__('The Loan Is Closed In HC'));
        }

        $vin = $loan->vehicle->vin;
        $citizen = $loan->citizen;
        $mortgage_citizen = $loan->mortgage->citizen;

        if ($mortgage_citizen) {
            $current_document_number = $mortgage_citizen->getPrimaryDocument()['passport_number'];
        } else {
            $current_document_number = $citizen->getPrimaryDocument()['passport_number'];
        }

        /** @var IEkengService $ekeng_service */
        $aggregator_service = resolve('App\Interfaces\IAggregatorService');
        /** @var IVehicleService $vehicle_service */
        $vehicle_service = resolve('App\Interfaces\IVehicleService');

        $citizen_ekeng = $aggregator_service->getEkengData($current_document_number);

        $current_vehicle_info = $vehicle_service->getPledgedVehicleInfo($citizen_ekeng, $vin);

        if (empty($current_vehicle_info)) {
            throw new PledgeVehicleNotFoundException();
        }

        if ($loan->isOVIL()) {
            // In the case of OVIL, we did not have vehicle details during crediting, so we should fill it later
            $vehicle_info = $vehicle_service->prepareVehicleInfo(['vehicle' => $current_vehicle_info]);

            $loan->vehicle->update($vehicle_info['vehicle']);
        }

        $lenders = $current_vehicle_info->lenders;
        if (empty($lenders)) {
            throw new PledgeMissingLenderException();
        }

        if (count($lenders) > 1) {
            throw new PledgeMultipleLendersException();
        }

        if (trim($lenders[0]->psn) !== constants('VEHICLE_LENDER_PSN')) {
            throw new PledgeExistingLenderException();
        }

        $context = new OvlFlowStrategyContext($loan);
        if ($loan->isOVIL()) {
            $context->confirmOVIL();
        } else {
            $context->confirm();
        }

        return response()->json($loan);
    }

    /**
     * @usage calls after admin rejection(Մերժել btn) or cancel pledge(Չեղարկել գրավադրումը btn)
     */
    public function reject(NovaRequest $request)
    {
        $loan_service = resolve('\App\Services\LoanServiceOVL');
        $loan = Loan::find($request->id);

        $loan_service->rejectLoan($loan, Loan::REJECTED, now());

        SendConfirmationSMS::dispatch($loan);

        return response()->json($loan);
    }

    public function review(NovaRequest $request)
    {
        $loan = Loan::find($request->id);

        $context = new OvlFlowStrategyContext($loan);
        $context->review();

        return response()->json($loan);
    }

    /**
     * @usage calls after agent's confirmation(Հաստատել btn)
     */
    public function process(NovaRequest $request)
    {
        $loan = Loan::find($request->id);

        $context = new OvlFlowStrategyContext($loan);
        $context->process();

        return response()->json($loan);
    }

    /**
     * @usage calls after agent's confirmation from Police File pop-up (Գրավադրել btn)
     */
    public function storePoliceFile(NovaRequest $request)
    {
        $type = LoanDocument::POLICE_FILE;

        $request->validate([
            'police_file' => 'required|mimes:pdf,png,jpg,jpeg',
        ]);

        $loan = Loan::getLoanWithDocuments($request->id);
        if (!$loan) {
            throw new LoanNotFoundException();
        }

        $file = $request->police_file;

        /** @var LoanDocumentServiceOVL $loanDocumentService */
        $loanDocumentService = resolve('\App\Services\LoanDocumentServiceOVL');
        $loanDocumentService->storeExistingDirectoryDocument($loan, $type, $file);

        $context = new OvlFlowStrategyContext($loan);

        $context->pledge();

        return response()->json($loan);
    }
}
