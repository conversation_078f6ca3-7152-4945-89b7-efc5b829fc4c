<?php

namespace App\Nova;

use App\Models\LoanSecurity;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use <PERSON>vel\Nova\Fields\Date;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Http\Requests\ResourceDetailRequest;

class Profile extends Resource
{
    public function __construct($resource)
    {
        parent::__construct($resource);

        $this->recognition_service = resolve('App\Interfaces\IRecognitionService');
    }

    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = 'App\\Models\Pallaton\\Profile';

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public function title()
    {
        return "{$this->first_name} {$this->last_name}";
    }

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'phone_number',
        'email',
        'ssn',
        'document_number',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(Request $request)
    {
        $fields = [
            ID::make()->sortable(),
        ];

        // Avoid registering the heavy Avatar field except on Detail view
        if ($request instanceof ResourceDetailRequest) {
            $fields[] = Text::make(__('Avatar'), function () {
                try {
                    $ssn = $this->ssn;
                    $loan_security = LoanSecurity::getRegistrationInstance($ssn);

                    $ekeng_src = $this->recognition_service->getEkengPhotoFromS3($this->ssn, $loan_security);
                    $camera_src = $this->recognition_service->getCameraPhotoFromS3($this->ssn, $loan_security);

                    $html = "<div class='avatar-row' style='display:flex;gap:10px;align-items:center;flex-wrap:wrap;'>";
                    if ($ekeng_src) {
                        $html .= "<a href='{$ekeng_src}' target='_blank' rel='noopener' title='Open Ekeng photo in new tab'>"
                            ."<img src='{$ekeng_src}' style='width:150px;height:200px;border-radius:5px;cursor:pointer;'>"
                            .'</a>';
                    }
                    if ($camera_src) {
                        $html .= "<a href='{$camera_src}' target='_blank' rel='noopener' title='Open Camera photo in new tab'>"
                            ."<img src='{$camera_src}' style='width:150px;height:200px;border-radius:5px;cursor:pointer;'>"
                            .'</a>';
                    }
                    $html .= '</div>';

                    return $html ?: __('No Avatar');
                } catch (Exception $e) {
                    Log::error('Error loading images', ['error' => $e->getMessage()]);

                    return __('Error loading images');
                }
            })->asHtml()->onlyOnDetail();
        }

        $fields = array_merge($fields, [
            Text::make(__('First Name'), 'first_name')->withMeta([
                'extraAttributes' => [
                    'readonly' => true,
                ],
            ]),

            Text::make(__('Last Name'), 'last_name')->withMeta([
                'extraAttributes' => [
                    'readonly' => true,
                ],
            ]),

            Text::make(__('Middle Name'), 'middle_name')->withMeta([
                'extraAttributes' => [
                    'readonly' => true,
                ],
            ]),

            Text::make(__('Phone Number'), 'phone_number')
                ->creationRules('required', 'phone_number', 'max:10')
                ->rules('required', 'regex:/^\+\d{11}$/'),

            Text::make(__('Email'), 'email')
                ->sortable()
                ->withMeta([
                    'extraAttributes' => [
                        'readonly' => true,
                    ],
                ]),

            Text::make(__('Soc Card'), 'ssn')->withMeta([
                    'extraAttributes' => [
                        'readonly' => true,
                    ],
                ]),

            Text::make(__('Passport'), 'document_number')->withMeta([
                    'extraAttributes' => [
                        'readonly' => true,
                    ],
                ]),

            Date::make(__('Register At'), 'created_at')->withMeta([
                    'extraAttributes' => [
                        'readonly' => true,
                    ],
                ]),
        ]);

        return $fields;
    }

    public static function label()
    {
        return __('Profile');
    }

    /**
     * Get the cards available for the request.
     *
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    public static function availableForNavigation(Request $request): bool
    {
        return $request->user()->isAdmin() || $request->user()->hasPermissionTo('remove-users', config('nova.guard'));
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array
     */
    public function actions(Request $request)
    {
        return [];
    }

    public static function authorizedToCreate(Request $request)
    {
        return false;
    }

    public static function indexQuery(NovaRequest $request, $query)
    {
        return $query;
    }
}
