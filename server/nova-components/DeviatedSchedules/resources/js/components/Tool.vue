<template>
  <div id="deviated-schedules" :disabled="true">
    <heading class="mb-6">{{ __('deviated_schedules') }}</heading>
    <loading :active="loading" spinner="line-scale" />
    <card>
      <div class="deviated-schedules-content">
        <input
          type="text"
          class="contract-number"
          placeholder="CVXX-XXXXX"
          v-model="contractNumber"
          @keyup.enter="getLoanSchedule"
        />

        <button class="deviated-schedules-button" @click="getLoanSchedule">
          {{ __('search') }}
        </button>

        <p v-if="deviated && !deviated.length" class="empty-loan">
          Նման պայմանգրով վարկ չի գտնվել
        </p>
        <div v-if="deviated && deviated.length" class="citizen-table">
          <div class="deviated">
            <p>{{ __('deviated') }}</p>
            <table class="table table-striped">
              <tbody>
                <tr>
                  <td>#</td>
                  <td>{{ __('mtot') }}</td>
                  <td>{{ __('princ') }}</td>
                  <td>{{ __('sfvan') }}</td>
                  <td>{{ __('intr') }}</td>
                  <td>{{ __('date') }}</td>
                </tr>
                <tr v-for="(item, key) in deviated" :key="`deviated-${key}`">
                  <td>{{ key + 1 }}</td>
                  <td>{{ item.mtot | numberFormat }}</td>
                  <td>{{ item.princ | numberFormat }}</td>
                  <td>{{ item.sfvan | numberFormat }}</td>
                  <td>{{ item.Intr | numberFormat }}</td>
                  <td>{{ reformatDate(item.fdate) }}</td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="corrected">
            <p>{{ __('corrected') }}</p>
            <table class="table table-striped">
              <tbody>
                <tr>
                  <td class="order">#</td>
                  <td>{{ __('mtot') }}</td>
                  <td>{{ __('princ') }}</td>
                  <td>{{ __('sfvan') }}</td>
                  <td>{{ __('intr') }}</td>
                  <td>{{ __('date') }}</td>
                </tr>
                <tr v-for="(item, key) in corrected" :key="`deviated-${key}`">
                  <td class="order">{{ key + 1 }}</td>
                  <td>{{ item.mtot | numberFormat }}</td>
                  <td>{{ item.princ | numberFormat }}</td>
                  <td>{{ item.sfvan | numberFormat }}</td>
                  <td>{{ item.Intr | numberFormat }}</td>
                  <td>{{ reformatDate(item.fdate) }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="correct-button" v-if="deviated && deviated.length">
          <button
            class="deviated-schedules-button"
            @click="correctLoanSchedule"
          >
            <span v-if="!scheduleIsCorrected">{{ __('correct') }}</span>
            <span v-else>{{ __('shcedule_corrected') }}</span>
          </button>
        </div>
      </div>
    </card>
  </div>
</template>

<script>
import Loading from 'vue-element-loading';
import moment from 'moment';
import formatNumber from 'format-number';

export default {
  data() {
    return {
      contractNumber: '',
      deviated: null,
      corrected: null,
      loading: false,
      scheduleIsCorrected: false,
    };
  },

  components: {
    Loading,
  },

  methods: {
    async getLoanSchedule() {
      try {
        this.loading = true;

        const response = await Nova.request().get(
          `/nova-vendor/deviated-schedules/loan?contract_number=${this.contractNumber.trim()}`
        );

        this.deviated = this.sortByDate(response.data.deviated);
        this.corrected = this.sortByDate(response.data.corrected);
      } catch (e) {
        if (e && e.response.status == 400) {
          return this.$toasted.show(this.__('not_deviated_loan'), {
            type: 'info',
          });
        }

        this.$toasted.show(this.getMessage(e), { type: 'error' });
      } finally {
        this.loading = false;
      }
    },

    async correctLoanSchedule() {
      try {
        this.loading = true;

        await Nova.request().post(
          `/nova-vendor/deviated-schedules/correct?contract_number=${this.contractNumber.trim()}`
        );

        this.scheduleIsCorrected = true;
        this.$toasted.show(this.__('successfuly_corrected'), {
          type: 'success',
        });
      } catch (e) {
        this.scheduleIsCorrected = false;

        this.$toasted.show(this.getMessage(e), { type: 'error' });
      } finally {
        this.loading = false;
      }
    },

    getMessage(e) {
      const errors = e.response && e.response.data.errors;

      if (!errors) {
        return this.__('something_went_wrong');
      }

      return Object.values(errors)[0];
    },

    sortByDate(arr) {
      return arr
        .sort((a, b) => {
          const dateA = moment(a.fdate, 'YYYY-MM-DD hh:mm:ss').unix();
          const dateB = moment(b.fdate, 'YYYY-MM-DD hh:mm:ss').unix();

          return new Date(dateB) - new Date(dateA);
        })
        .reverse();
    },

    reformatDate(date, format = 'DD-MM-YYYY') {
      return moment(date, 'YYYY-MM-DD hh:mm:ss').format(format);
    },
  },

  filters: {
    numberFormat(value) {
      return formatNumber()(+value);
    },
  },
};
</script>
