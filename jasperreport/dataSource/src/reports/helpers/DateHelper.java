package reports.helpers;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;
import java.time.LocalDate;
import java.time.Period;

public class DateHelper {

    // We select from DB UTC time with ARM_TIME_ZONE, so we need +8
    public static final String ARM_TIME_ZONE = "GMT+8";
    public static final String NORK_DATE = "yyyy-MM-dd'T'HH:mm:ss";
    public static final String DATE_TIME = "dd/MM/yyyy HH:mm:ss";
    public static final String DATE = "dd/MM/yyyy";
    public static final String ACRA_DATE = "dd-MM-yyyy";
    public static final String E_CIVIL_DATE = "yyyy-MM-dd";

    public static final Integer START_DAY_DEVIATION = -1;
    public static final Integer END_DAY_DEVIATION = 0;
    public static final Integer UTC_TIME_DEVIATION = 20;

    public static final int ROUND_UP_MONTHS = 1;
    public static final int MONTHS_IN_YEAR = 12;


    public static Date convertToDate(String date, String format) {
        try {
            return new SimpleDateFormat(format).parse(date);
        } catch (ParseException e) {
            return null;
        }
    }

    public static Date convertToDate(Object date, String format) {
        String dateString = formatDateStringByTimeZone(date, format);
        try {
            return new SimpleDateFormat(format).parse(dateString);
        } catch (ParseException e) {
            return null;
        }
    }

    public static String formatDateString(Object date, String format) {
        try {
            SimpleDateFormat formatter = new SimpleDateFormat(format);
            return formatter.format(date);
        } catch (Exception e) {
            return "";
        }
    }

    public static String formatDateStringByTimeZone(Object date, String format) {
        try {
            SimpleDateFormat formatter = new SimpleDateFormat(format);
            formatter.setTimeZone(TimeZone.getTimeZone(ARM_TIME_ZONE));
            return formatter.format(date);
        } catch (Exception e) {
            return "";
        }
    }

    public static Timestamp modifyInputDate(Date date, Integer dayDeviation, Integer setHour) {
        Calendar c = Calendar.getInstance();
        Date s = date;
        c.setTime(s);
        c.add(Calendar.DATE, dayDeviation);
        c.add(Calendar.HOUR, setHour);
        s = c.getTime();

        return new Timestamp(s.getTime());
    }

    public static Date minEntryDate(Date date, Date newDate) {
        if (newDate == null) {
            return date;
        }
        if (date == null) {
            return newDate;
        }

        return newDate.compareTo(date) < 0 ? newDate : date;
    }

    public static Date maxEntryDate(Date date, Date newDate) {
        if (newDate == null) {
            return date;
        }
        if (date == null) {
            return newDate;
        }

        return newDate.compareTo(date) > 0 ? newDate : date;
    }

    public static int monthsRoundUpDiff(Date strat, Date end) {
        LocalDate startDate = strat.toInstant().atZone(ZoneId.of(DateHelper.ARM_TIME_ZONE)).toLocalDate();
        LocalDate endDate = end.toInstant().atZone(ZoneId.of(DateHelper.ARM_TIME_ZONE)).toLocalDate();

        // calculate difference
        int days = Period.between(startDate, endDate).getDays();
        int months = Period.between(startDate, endDate).getMonths();
        int years = Period.between(startDate, endDate).getYears();

        if (days > 0) {
            months = months + DateHelper.ROUND_UP_MONTHS;
        }

        return months + years * DateHelper.MONTHS_IN_YEAR;
    }

    public static int yearsRoundDownDiff(Date start, Date end) {
        LocalDate startDate = start.toInstant().atZone(ZoneId.of(DateHelper.ARM_TIME_ZONE)).toLocalDate();
        LocalDate endDate = end.toInstant().atZone(ZoneId.of(DateHelper.ARM_TIME_ZONE)).toLocalDate();

        // calculate difference
        return Period.between(startDate, endDate).getYears();
    }
}