<?php

namespace App\Observers;

use App\Helpers\EnvironmentHelper;
use App\Models\QrCode;
use Illuminate\Support\Str;
use SimpleSoftwareIO\QrCode\Facades\QrCode as QrCodeGenerator;

class QRCodeObserver
{
    public function __construct()
    {
        $this->aws_service = resolve('App\Interfaces\IAWSService');
    }

    public function creating(QrCode $qr_code)
    {
        $this->extractOwner($qr_code);

        $file_name = Str::random().'.'.constants('QR_CODE.FORMAT');

        $token = $this->generateToken();
        $url = EnvironmentHelper::generateUrl("/app/qr/$token");

        $qr_image = QrCodeGenerator::format(constants('QR_CODE.FORMAT'))
            ->size($qr_code->qr_image_size)
            ->generate($url);

        $this->aws_service->storeQRCode($file_name, $qr_image);

        // Adding 'token' and 'file_name' into QrCode Model attributes list
        $qr_code->token = $token;
        $qr_code->file_name = $file_name;
    }

    public function created(QrCode $qr_code)
    {
        $owner = $this->getOwner();

        if (isset($owner['owner_name'])) {
            $qr_code->qr_owner()->create(array_merge($owner, ['name' => $owner['owner_name']]));
        }
    }

    public function updated(QrCode $qr_code)
    {
    }

    /**
     * Handle the user "deleted" event.
     *
     * @return void
     */
    public function deleted(QrCode $qr_code)
    {
    }

    /**
     * Handle the user "restored" event.
     *
     * @return void
     */
    public function restored(QrCode $qr_code)
    {
    }

    /**
     * Handle the user "force deleted" event.
     *
     * @return void
     */
    public function forceDeleted(QrCode $qr_code)
    {
    }

    private function extractOwner($qr_code)
    {
        $owner = $this->getOwner();

        foreach ($owner as $key => $value) {
            unset($qr_code[$key]);
        }

        return $qr_code;
    }

    private function getOwner()
    {
        return request()->only([
            'owner_name',
            'hvhh',
            'phone_number',
            'email',
            'address',
            'note',
        ]);
    }

    private function generateToken()
    {
        $token = strtolower(Str::random(constants('QR_CODE.TOKEN_LENGTH')));

        // Call the same function if the code exists already
        if (QrCode::whereToken($token)->exists()) {
            return $this->generateToken();
        }

        // Otherwise, it's valid and can be used
        return $token;
    }
}
