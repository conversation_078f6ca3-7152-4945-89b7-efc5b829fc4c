package reports.helpers;

import static org.apache.commons.math.util.MathUtils.round;

public class MathHelper {

    public static double getRatio(double dividend, double divisor) {
        if (dividend == 0 || divisor == 0) {
            return 0;
        }

        return round(dividend / divisor, 2);
    }

    public static int getRatio(int dividend, int divisor) {
        if (dividend == 0 || divisor == 0) {
            return 0;
        }

        return dividend / divisor;
    }
}
