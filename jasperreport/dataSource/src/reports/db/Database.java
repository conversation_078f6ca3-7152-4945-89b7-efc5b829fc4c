package reports.db;

import reports.ReportLogger;
import reports.ReportProperties;

import java.sql.*;

public class Database implements AutoCloseable {

    private Connection conn;
    private String JDBC_URL = "jdbc:postgresql://%s:%s/%s";

    /**
     * load JDBC driver and open DB connection
     */
    public Database() {
        conn = this.createConnection("");
    }

    public Database(String connectionHost) {
        conn = this.createConnection(connectionHost);
    }

    private Connection createConnection(String connectionHost) {
        loadJDBCDriver();

        try {
            ReportProperties prop = ReportProperties.INSTANCE;

            return DriverManager.getConnection(
                    composeConnectionUrl(prop.getProperty(connectionHost + "DB_NAME"), prop.getProperty(connectionHost + "DB_HOST"), prop.getProperty(connectionHost + "DB_PORT")),
                    prop.getProperty(connectionHost + "DB_USER"),
                    prop.getProperty(connectionHost + "DB_PASS")
            );
        } catch (SQLException e) {
            ReportLogger.logger.error("Database SQLException", e);
        } catch (Exception e) {
        }

        return null;
    }

    private String composeConnectionUrl(String schema, String host, String port) {
        return String.format(JDBC_URL, host, port, schema);
    }

    /**
     * Return DB connection
     *
     * @return
     */
    public Connection getConnection() {
        return conn;
    }

    /**
     * Close DB connection
     */
    public void close() {
        try {
            conn.close();
        } catch (SQLException e) {
        }
    }

    private void loadJDBCDriver() {
        try {
            Class.forName("org.postgresql.Driver");
        } catch (ClassNotFoundException e) {
        }
    }
}