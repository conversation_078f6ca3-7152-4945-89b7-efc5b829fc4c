/*
 * Copyright (C) 2005 - 2019 TIBCO Software Inc. All rights reserved.
 * http://www.jaspersoft.com.
 *
 * Unless you have purchased a commercial license agreement from Jaspersoft,
 * the following license terms apply:
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package reports.loanReport;

import com.jaspersoft.jasperserver.api.metadata.common.service.RepositoryService;
import com.jaspersoft.jasperserver.api.metadata.jasperreports.service.ReportDataSourceService;
import net.sf.jasperreports.engine.JRParameter;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class LoanDataSourceService implements ReportDataSourceService {

    public LoanDataSourceService() {}

    /* (non-Javadoc)
     * @see com.jaspersoft.jasperserver.api.metadata.jasperreports.service.ReportDataSourceService#closeConnection()
     */
    public void closeConnection() {
        // Do nothing
    }

    /* (non-Javadoc)
     * @see com.jaspersoft.jasperserver.api.metadata.jasperreports.service.ReportDataSourceService#setReportParameterValues(java.util.Map)
     */
    public void setReportParameterValues(Map parameterValues) {
        // Composing loan report by contract_number
        this.ds = new LoanDataSource(parameterValues.get("connection_host").toString());
        this.ds.composeLoanReport(parameterValues.get("contract_number").toString());

        parameterValues.put(JRParameter.REPORT_DATA_SOURCE, ds);
    }

    public RepositoryService getRepository() {
        return repository;
    }

    public void setRepository(RepositoryService repository) {
        this.repository = repository;
    }

    public static ReportDataSourceService createDataSourceInstance() {
        return new LoanDataSourceService();
    }

    LoanDataSource ds;
    private RepositoryService repository;
}
