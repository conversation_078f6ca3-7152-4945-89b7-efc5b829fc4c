<?php

namespace App\Models;

use App\Helpers\ObjectMerger;
use App\Schemas\PersonalInfoSchema;
use Carbon\Carbon;
use function Functional\map;
use Illuminate\Database\Eloquent\Model;

class EkengRequest extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'content',
        'identifier',
        'identifier_type',
        'date',
        'is_valid',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'is_valid' => 'boolean',
    ];

    public function setContentAttribute($value)
    {
        $this->attributes['content'] = json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    /**
     * Extract and normalize citizen data from Ekeng API response.
     *
     * @param object $ekeng_data API response containing Ekeng citizen data
     *
     * @return array Normalized citizen data
     */
    public static function extractCitizenFromEkengData($ekeng_data): array
    {
        // Decode the Ekeng response content into an associative array
        $citizen['ekeng'] = json_decode($ekeng_data->content, true);
        // Convert passport data to an object to ensure consistency
        $citizen['ekeng']['passport_data'] = json_decode(json_encode($citizen['ekeng']['passport_data'] ?? []));

        $schema = new PersonalInfoSchema();

        $merger = new ObjectMerger($schema->get());
        $normalized_citizen_data = $merger->merge($citizen);
        if ($normalized_citizen_data['region'] == 'ԵՐԵՎԱՆ') {
            $normalized_citizen_data['city'] = 'ԵՐԵՎԱՆ'; // Normalize "Yerevan" as the city when applicable
        }

        $normalized_citizen_data['passports'] = map($normalized_citizen_data['passports'], function ($passport) {
            // Extract social security card number
            if ($passport['type'] == constants('SOC_CARD')) {
                return $passport;
            }

            // Normalize and collect passport data
            $passport['given_date'] = Carbon::parse($passport['given_date'])->startOfDay();
            $passport['expire_date'] = Carbon::parse($passport['expire_date'])->startOfDay();

            return $passport;
        });

        return [
            'citizen' => $normalized_citizen_data,
            'ekeng_data' => $citizen['ekeng'],
        ];
    }

    public static function getDecodedEkengData($ssn)
    {
        $ekeng_request_content = self::whereIdentifier($ssn)
            ->where('identifier_type', constants('SOC_CARD'))
            ->valid()
            ->latest()
            ->value('content');

        if ($ekeng_request_content) {
            return json_decode($ekeng_request_content);
        }

        return null;
    }

    /**
     * Scope a query to only include valid records.
     */
    public function scopeValid($query)
    {
        return $query->where('is_valid', true);
    }
}
