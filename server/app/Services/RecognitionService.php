<?php

namespace App\Services;

use App\Exceptions\AwsException;
use App\Exceptions\AwsStoreException;
use App\Exceptions\BlacklistedException;
use App\Exceptions\CitizenPhotoException;
use App\Exceptions\CompareFaceException;
use App\Exceptions\LivenessException;
use App\Interfaces\IRecognitionService;
use App\Models\CitizenFaceRecognition;
use App\Models\LoanSecurity;
use Carbon\Carbon;
use Exception;
use Log;

class RecognitionService implements IRecognitionService
{
    private $livenessService;
    private $faceComparisonService;

    public function __construct()
    {
        $this->livenessService = resolve('App\Interfaces\ILivenessService');
        $this->faceComparisonService = resolve('App\Interfaces\IFaceComparisonService');
    }

    public function detectCitizen($image)
    {
        $face_detection_attrs = [
            'status' => CitizenFaceRecognition::LIVENESS_FAIL,
        ];

        try {
            // Getting size and store path
            $image_path = $this->storeImageToS3($image);

            $image_dimensions = $this->getImageSize($image) ?? [];

            $face_detection_attrs = array_merge(
                $face_detection_attrs,
                $image_dimensions,
                [
                    'image_path' => $image_path,
                ]
            );

            // Face recognition
            $compare_attrs = $this->faceComparisonService->compareCitizen($image, $this->getEkengPhoto());
            $face_detection_attrs = array_merge($face_detection_attrs, $compare_attrs);

            // Liveness check
            $liveness_attrs = $this->livenessService->checkLiveness($image, $image_path, $image_dimensions);

            $face_detection_attrs = array_merge(
                $face_detection_attrs,
                $liveness_attrs
            );

            return true;
        } catch (LivenessException $e) {
            $face_detection_attrs = array_merge($face_detection_attrs, $e->getVerifications());

            // Disregard liveness failure during the day
            if (!$this->isLivenessEnabled()) {
                $failed = $this->getFailedRecognitionsCount();
                if ($failed >= constants('FACE_RECOGNITION_ATTEMPTS') - 1) {
                    return true;
                }
            }

            throw $e;
        } catch (CitizenPhotoException $e) {
            $face_detection_attrs = array_merge($face_detection_attrs, $e->getVerifications());
        } catch (AwsException $e) {
            $face_detection_attrs = array_merge($face_detection_attrs, $e->getVerifications());
            throw $e;
        } catch (Exception $e) {
            Log::error('Citizen face comparison, CompareFaceException', ['error' => $e->getMessage()]);
            throw new CompareFaceException();
        } finally {
            $this->storeCitizenFaceRecognition($face_detection_attrs);
            $this->checkFailedRecognitions();
        }
    }

    protected function getEkengPhoto()
    {
        try {
            $aggregator_service = resolve('App\Interfaces\IAggregatorService');
            $security_service = resolve('App\Interfaces\ISecurityService');
            $loan_security = $security_service->resolveLoanSecurity();

            $citizen_photo = $aggregator_service->getEkengPhoto($loan_security->document_number);

            return base64_decode($citizen_photo);
        } catch (Exception $e) {
            Log::error("Can't fetch citizen ekeng photo", ['document_number' => $loan_security->document_number]);

            throw $e;
        }
    }

    protected function storeCitizenFaceRecognition($data = [])
    {
        $security_service = resolve('App\Interfaces\ISecurityService');
        $loan_security = $security_service->resolveLoanSecurity();

        $loan_security->citizen_face_recognitions()->create($data);
    }

    protected function storeImageToS3(string $image)
    {
        try {
            $aws_service = resolve('App\Interfaces\IAWSService');

            $image_path = env('CITIZEN_PHOTO_SUBDIR').'/'.str_random().'.png';

            $aws_service->storeRecognitionMedia($image_path, $image);

            return $image_path;
        } catch (Exception $e) {
            // We do not throw exception as we need to continue the request of the client despite the exception.
            Log::error('AwsStoreException', ['error' => $e->getMessage()]);

            throw new AwsStoreException(['status' => CitizenFaceRecognition::AWS_STORE_FAIL]);
        }
    }

    protected function getFailedRecognitionsCount()
    {
        $security_service = resolve('App\Interfaces\ISecurityService');
        $loan_security = $security_service->resolveLoanSecurity();

        return $loan_security->citizen_face_recognitions()->whereNotIn('status', [
            CitizenFaceRecognition::MATCHED,
            CitizenFaceRecognition::OK,
        ])->where('type', CitizenFaceRecognition::CAMERA_PHOTO)->count();
    }

    protected function getUnmatchedCount()
    {
        $security_service = resolve('App\Interfaces\ISecurityService');
        $loan_security = $security_service->resolveLoanSecurity();

        return $loan_security->citizen_face_recognitions()->whereIn('status', [
            CitizenFaceRecognition::UNMATCHED,
            CitizenFaceRecognition::AWS_FAIL,
        ])->where('type', CitizenFaceRecognition::CAMERA_PHOTO)->count();
    }

    protected function isLivenessEnabled()
    {
        $now = Carbon::now(constants('ARM_TIMEZONE'));
        $start = Carbon::parse(env('LIVENESS_ENABLED_START'), constants('ARM_TIMEZONE'));
        $end = Carbon::parse(env('LIVENESS_ENABLED_END'), constants('ARM_TIMEZONE'));

        return $now->gte($start) && $now->lte($end);
    }

    protected function isRecognitionSuccessfull()
    {
        $unmatched = $this->getUnmatchedCount();

        return $unmatched < constants('FACE_RECOGNITION_ATTEMPTS');
    }

    /**
     * Function to get image dimensions.
     *
     * @param $image
     *
     * @return array
     */
    protected function getImageSize(string $image)
    {
        $size_info = getimagesizefromstring($image);

        return [
            'image_width' => $size_info[0],
            'image_height' => $size_info[1],
        ];
    }

    protected function checkFailedRecognitions()
    {
        $security_service = resolve('App\Interfaces\ISecurityService');

        $failed = $this->getFailedRecognitionsCount();

        if ($failed >= constants('FACE_RECOGNITION_ATTEMPTS')) {
            // We block citizen at night time, or at day time for failing face recognition(disregard liveness)
            if (
                $this->isLivenessEnabled() ||
                (!$this->isLivenessEnabled() && !$this->isRecognitionSuccessfull())
            ) {
                // For unmatched failures, block citizen
                $security_service->blockCitizen(constants('BLACKLIST_REASONS.RECOGNITION_NOT_SUCCESSFULL'));

                throw new BlacklistedException();
            }
        }
    }

    public function hasEkengPhoto()
    {
        $security_service = resolve('App\Interfaces\ISecurityService');
        $loan_security = $security_service->resolveLoanSecurity();

        return $loan_security->citizen_face_recognitions()->ekengPhoto()->exists();
    }

    public function handleEkengPhoto()
    {
        $aggregator_service = resolve('App\Interfaces\IAggregatorService');
        $security_service = resolve('App\Interfaces\ISecurityService');

        $loan_security = $security_service->resolveLoanSecurity();

        $image = $aggregator_service->getEkengPhoto($loan_security->document_number);

        if (is_null($image)) {
            $this->storeEkengPhotoToDb([
                'status' => CitizenFaceRecognition::NO_PHOTO,
                'type' => CitizenFaceRecognition::EKENG_PHOTO,
            ]);

            $security_service->blockCitizen(constants('BLACKLIST_REASONS.NO_EKENG_PHOTO'));

            throw new BlacklistedException();
        }

        $this->storeEkengPhotoToS3($image);
    }

    public function storeEkengPhotoToS3($image)
    {
        try {
            $aws_service = resolve('App\Interfaces\IAWSService');
            $image = base64_decode($image);
            $path = str_random().'.png';

            $aws_service->storeEkengMedia($path, $image);

            $data = [
                'type' => CitizenFaceRecognition::EKENG_PHOTO,
                'status' => CitizenFaceRecognition::OK,
                'image_path' => $path,
            ];
        } catch (AwsException $e) {
            $data = [
                'type' => CitizenFaceRecognition::EKENG_PHOTO,
                'status' => CitizenFaceRecognition::AWS_FAIL,
            ];

            throw new AwsStoreException();
        } catch (Exception $e) {
            $data = [
                'type' => CitizenFaceRecognition::EKENG_PHOTO,
                'status' => CitizenFaceRecognition::EKENG_FAIL,
            ];

            throw $e;
        } finally {
            $this->storeEkengPhotoToDb($data);
        }
    }

    public function storeEkengPhotoToDb(array $data): void
    {
        $security_service = resolve('App\Interfaces\ISecurityService');
        $loan_security = $security_service->resolveLoanSecurity();

        $loan_security->citizen_face_recognitions()->create($data);
    }

    public function getEkengPhotoFromS3($ssn, $loan_security = null, $should_base64 = false)
    {
        $img = null;
        $aws_service = resolve('App\Interfaces\IAWSService');

        if (!$loan_security) {
            $loan_security = LoanSecurity::getRegistrationInstance($ssn);
        }

        if ($loan_security) {
            $ekeng_photo = $loan_security->citizen_face_recognitions()->ekengPhoto()->first();

            if ($ekeng_photo) {
                if ($should_base64) {
                    $img = base64_encode($aws_service->getEkengMedia($ekeng_photo->image_path));
                } else {
                    $img = $ekeng_photo->ekeng_full_image_path;
                }
            }
        }

        return $img;
    }

    public function getCameraPhotoFromS3($ssn, $loan_security = null)
    {
        $img_src = null;

        if (!$loan_security) {
            $loan_security = LoanSecurity::getRegistrationInstance($ssn);
        }

        if ($loan_security) {
            $camera_photo = $loan_security->citizen_face_recognitions()->cameraPhoto()->first();

            if ($camera_photo) {
                $img_src = $camera_photo->full_image_path;
            }
        }

        return $img_src;
    }
}
