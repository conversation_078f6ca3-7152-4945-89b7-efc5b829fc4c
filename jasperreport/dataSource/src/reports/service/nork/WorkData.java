package reports.service.nork;

import org.json.JSONException;
import org.json.JSONObject;
import reports.ReportLogger;
import reports.helpers.DateHelper;

import java.util.Date;

public class WorkData {
    private String workName;
    private String registor_kod;
    private String gorcatu_kod;
    private String hvhh;
    private String region;
    private String address;
    private Date entryDate;
    private Date expiryDate;
    private Double salary;
    private Date pajmanData;
    private Double avum;
    private Double socvjar;
    private String pashton;
    private String workPhone;

    public WorkData() {
    }

    public WorkData(JSONObject workData) {
        try {
            this.workName = workData.getString("WorkName");
            this.registor_kod = workData.getString("Registor_kod");
            this.gorcatu_kod = workData.getString("Gorcatu_kod");
            this.hvhh = workData.getString("Hvhh");
            this.region = workData.getString("Region");
            this.address = workData.getString("Address");
            this.entryDate = getDate(workData.getString("EntryDate"));
            this.expiryDate = getDate(workData.getString("ExpiryDate"));
            this.salary = workData.getDouble("Salary");
            this.pajmanData = getDate(workData.getString("Pajman_data"));
            this.avum = workData.getDouble("Avum");
            this.socvjar = workData.getDouble("Socvjar");
            this.pashton = workData.getString("Pashton");
            this.workPhone = workData.getString("WorkPhone");

        } catch (JSONException e) {
            ReportLogger.logger.error("Work Data JSONException", e);
        }
    }

    public String getWorkName() {
        return workName;
    }

    public Date getEntryDate() {
        return entryDate;
    }

    public void setEntryDate(Date entryDate) {
        this.entryDate = entryDate;
    }

    public Date getExpiryDate() {
        return expiryDate;
    }

    public String getHvhh() {
        return hvhh;
    }

    public Date getPajmanData() {
        return pajmanData;
    }

    public Double getSalary() {
        return salary;
    }

    public String getWorkPhone() {
        return workPhone;
    }

    public String getAddress() {
        return address;
    }

    /**
     * Format NORK String date to Date type
     *
     * @param date
     * @return
     */
    private Date getDate(String date) {
        return DateHelper.convertToDate(date, DateHelper.NORK_DATE);
    }
}
