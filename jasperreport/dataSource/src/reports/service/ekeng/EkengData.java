package reports.service.ekeng;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import reports.ReportLogger;

import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;

public class EkengData {
    protected JSONArray vehicles_info;
    protected List<JSONObject> e_civil = new LinkedList<JSONObject>();
    public static final String E_CIVIL_MARRIAGE = "marriage";
    public static final String E_CIVIL_DIVORCE = "divorce";
    public static final String E_CIVIL_SINGLE = "single";

    public EkengData(JSONObject ekeng) {
        this.setVehicleInfo(ekeng);
        this.setE_civil(ekeng);
    }

    protected void setVehicleInfo(JSONObject ekeng) {
        try {
            this.vehicles_info = ekeng.getJSONArray("vehicle_info");
        } catch (JSONException e) {
        }
    }

    protected void setE_civil(JSONObject ekeng) {
        try {
            JSONObject e_civil = ekeng.getJSONObject("e_civil");

            Iterator iterator = e_civil.keys();
            while (iterator.hasNext()) {
                String key = (String) iterator.next();
                JSONObject j = e_civil.getJSONObject(key);
                this.e_civil.add(j);
            }
        } catch (JSONException e) {
        }
    }

    public int getVehicleCount() {
        try {
            return this.vehicles_info.length();
        } catch (Exception e) {
            ReportLogger.logger.error("Get Vehicle Count Exception", e);
        }

        return 0;
    }

    public List<JSONObject> getE_civil() {
        return e_civil;
    }
}
