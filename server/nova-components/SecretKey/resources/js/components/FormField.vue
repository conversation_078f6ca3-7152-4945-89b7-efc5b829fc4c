<template>
  <default-field :field="field" :errors="errors">
    <template slot="field">
      <div v-if="!this.isUpdating()" class="secret-key-section">
        <input
          :id="field.name"
          disabled
          type="text"
          class="w-full form-control form-input form-input-bordered"
          :class="errorClasses"
          :placeholder="field.name"
          v-model="value"
        />
        <button @click.prevent="generateKey">
          <img :src="this.regenerateIMG()" />
        </button>
      </div>
      <div v-else class="secret-key-section">
        <input
          :id="field.name"
          disabled
          type="text"
          class="w-full form-control form-input form-input-bordered"
          :class="errorClasses"
          :placeholder="field.name"
          v-model="this.newKey"
        />
        <button @click.prevent="generateNewKey">
          <img :src="this.regenerateIMG()" />
        </button>
      </div>
    </template>
  </default-field>
</template>

<script>
import { <PERSON><PERSON>ield, HandlesValidationErrors } from 'laravel-nova';
import randomBytes from 'random-bytes';

export default {
  mixins: [<PERSON><PERSON><PERSON>, HandlesValidationErrors],

  props: ['resourceName', 'resourceId', 'field'],

  data() {
    return {
      newKey: '',
    };
  },

  mounted() {
    !this.isUpdating() && this.generateKey();
  },

  methods: {
    /*
     * Set the initial, internal value for the field.
     */
    setInitialValue() {
      this.value = this.isUpdating() ? null : this.field.value || '';
    },

    /**
     * Fill the given FormData object with the field's internal value.
     */
    fill(formData) {
      formData.append(this.field.attribute, this.value || '');
    },

    /**
     * Update the field's internal value.
     */
    handleChange(value) {
      this.value = value;
    },

    generateKey() {
      const bytes = randomBytes.sync(32);
      const secret = this.toHexString(bytes);

      this.value = secret;
    },

    generateNewKey() {
      const bytes = randomBytes.sync(32);
      const secret = this.toHexString(bytes);

      this.newKey = secret;
      this.value = secret;
    },

    toHexString(byteArray) {
      return Array.from(byteArray, function(byte) {
        return ('0' + (byte & 0xff).toString(16)).slice(-2);
      }).join('');
    },

    isUpdating() {
      return this.$route.name === 'edit';
    },

    regenerateIMG() {
      return 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAFBQUFBVUFpkZFp9h3iHfbmqm5uquf/I18jXyP//////////////////////////////////////////////////wAALCABNAE0BAREA/8QAOQAAAwEBAQAAAAAAAAAAAAAAAAQFAwIBEAADAQABBQEAAwEAAAAAAAAAAQIDBBESFCFSMRMyQjP/2gAIAQEAAD8AdAz03iBOuXozJ7a/bBb6/YxHM+0OTc2ussn8r/sLptfjLInvv/iBE2jDSzbw39hXDsWqKh9KQRdQ+sne1rSlRiVd9OyCUPYYf7scA8dTP60jl/w2ujaJuufZRkA1y666GeEd+iKgAIcz+8igABYrHK31chOcR+I6ADy84v3SOPHx+A8fH4Dx8fgFhl8CnmWHmWHmWHmWO533xLOgB+kI+ZY1ho9IJIDE4aWk0HjbfI/knGcydgLcnVTPYTixjHZnKI4G2W1ZFGNov8Z2HpC+vKmfUE5t022NcbHq+8fIo/llOuArpleZkdK7+mDqn+tnI3lxm/dj6SS6ICM10bRS4vrI3Mq4uVGL4i+wXEgYnLOPyTsAP//Z';
    },
  },
};
</script>
