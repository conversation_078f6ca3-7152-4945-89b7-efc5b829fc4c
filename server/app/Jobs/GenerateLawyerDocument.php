<?php

namespace App\Jobs;

use App\Factory\DocumentServiceFactory;
use App\Helpers\ArrayHelper;
use App\Helpers\NumberHelper;
use App\Helpers\PassportHelper;
use App\Models\CardToCardPayment;
use App\Models\CashPayment;
use App\Models\EasypayWalletPayment;
use App\Models\IdramWalletPayment;
use App\Models\LoanDocument;
use App\Models\ManualTransfer;
use App\Models\ProductProvision;
use App\Models\TelcellWalletPayment;
use App\Models\WalletLoanPayment;
use App\Models\WirePayment;
use Carbon\Carbon;
use DNS1D;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Log;
use PDF;

class GenerateLawyerDocument implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    private $loan;
    private $loanDocumentService;
    private $loan_config_service;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($loan)
    {
        $this->loan = $loan;
        $this->loanDocumentService = DocumentServiceFactory::build($loan->loan_type_id);
        $this->loan_config_service = resolve('App\Services\LoanConfigService');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // We have to generate lawyer documents if loan documents already exist.
        if (!$this->loan->documents->first()) {
            return;
        }

        $type = LoanDocument::LAWYER['name'];

        if ($this->loan->isTopUp()) {
            // We need to store the latest lawyer document in history coz of top-up loan could have it multiple times
            $this->loan->archiveLoanDocuments($type, true);
        }

        $this->loan->documents()
            ->where('document_type', $type)
            ->delete();

        $directory = $this->loanDocumentService->extractDirectoryPath($this->loan);
        $this->generatePdf($type, $directory);
    }

    protected function generatePdf($type, $directory)
    {
        $citizen = $this->loan->citizen;
        $path = $this->loanDocumentService->getDocumentPath($citizen->first_name, $citizen->last_name, $type, $directory, constants('EXTENSION.PDF'));

        $pdf = PDF::loadView(
            $this->loanDocumentService->getPdfTemplatePath($type),
            $this->composePdfData($this->loan)
        );
        $content = $pdf->output();

        $this->loanDocumentService->persistDocument($this->loan, $path, $type, $content, LoanDocument::LAWYER['public']);
    }

    protected function composePdfData($loan)
    {
        $citizen = $loan->citizen;

        ['doc' => $doc, 'soc_card' => $soc_card] = $this->extractDocumentAndSocCard($citizen);

        $loan_security = $loan->loan_security()->first();

        $configs = $this->loan_config_service->getConfigsByLoan($loan);

        $payment = $this->getPayment($loan);

        $date_now = $this->parseDate(Carbon::now());

        $data = array_merge(
            $loan->toArray(),
            $loan_security->toArray(),
            $payment ?? [],
            $configs,
            ArrayHelper::pick($citizen, ['first_name', 'last_name', 'middle_name', 'phone_number', 'email']),
            ArrayHelper::pick($doc, ['passport_number', 'from']),
            [
                'soc_card_number' => $soc_card['passport_number'],
                'address' => PassportHelper::composeAddress($citizen),
                'date_now' => Carbon::parse($date_now)->format('d.m.Y'),
                'verification_code_sent' => $this->parseDate($loan_security->verification_code_sent),
                'verification_code_recv' => $this->parseDate($loan_security->verification_code_recv),
                'sign_date' => $this->parseDate($loan->sign_date),
                'barcode' => DNS1D::getBarcodeHTML($loan->contract_number, 'C39'),
                'interest_rate' => $loan->interest_rate,
                'created_at' => $this->parseDate($loan->created_at),
            ]
        );

        return $data;
    }

    protected function getPayment($loan)
    {
        if ($loan->payment instanceof IdramWalletPayment) {
            return [
                'payment_type' => lang('transfer_methods.idram_wallet'),
                'payment_target' => $loan->payment->wallet_id,
                'payment_desc' => lang('transfer_desc.wallet'),
                'paid' => $this->parseDate($loan->payment->withdrawn),
            ];
        } elseif ($loan->payment instanceof CashPayment) {
            return [
                'payment_type' => lang('transfer_type.cash'),
                'payment_target' => lang('transfer_methods.cash_payment'),
                'payment_desc' => lang('transfer_desc.cash'),
                'paid' => null,
            ];
        } elseif ($loan->payment instanceof CardToCardPayment) {
            return [
                'payment_type' => lang('transfer_methods.card_to_card'),
                'payment_target' => NumberHelper::cardMask($loan->payment->card_number),
                'payment_desc' => lang('transfer_desc.bank_card'),
                'paid' => $this->parseDate($loan->payment->withdrawn),
            ];
        } elseif ($loan->payment instanceof WirePayment) {
            return [
                'payment_type' => lang('transfer_methods.bank_transfer'),
                'payment_target' => $loan->payment->bank_account,
                'payment_desc' => lang('transfer_desc.bank_account'),
                'paid' => null,
            ];
        } elseif ($loan->payment instanceof EasypayWalletPayment) {
            return [
                'payment_type' => lang('transfer_methods.easypay_wallet'),
                'payment_target' => $loan->payment->wallet_id,
                'payment_desc' => lang('transfer_desc.wallet'),
                'paid' => $this->parseDate($loan->payment->withdrawn),
            ];
        } elseif ($loan->payment instanceof TelcellWalletPayment) {
            return [
                'payment_type' => lang('transfer_methods.telcell_wallet'),
                'payment_target' => $loan->payment->wallet_id,
                'payment_desc' => lang('transfer_desc.wallet'),
                'paid' => $this->parseDate($loan->payment->withdrawn),
            ];
        } elseif ($loan->payment instanceof ProductProvision) {
            return [
                'payment_type' => lang('transfer_methods.product_provision'),
                'payment_target' => lang('transfer_methods.product_provision'),
                'payment_desc' => lang('transfer_methods.product_provision'),
                'paid' => $this->parseDate($loan->sign_date),
            ];
        } elseif ($loan->payment instanceof WalletLoanPayment) {
            $key = $this->walletKey();

            return [
                'payment_type' => lang('transfer_methods.'.$key),
                'payment_target' => $loan->payment->wallet_id,
                'payment_desc' => lang('transfer_desc.wallet'),
                'paid' => $this->parseDate($loan->payment->withdrawn),
            ];
        } elseif ($loan->payment instanceof ManualTransfer) {
            return [
                'payment_type' => null,
                'payment_target' => null,
                'payment_desc' => null,
                'paid' => null,
            ];
        }
    }

    private function walletKey()
    {
        if ($this->loan->loan_type_id === constants('LOAN_TYPES.OTCL')) {
            return 'telcell_wallet';
        } elseif ($this->loan->loan_type_id === constants('LOAN_TYPES.OIDL')) {
            return 'idram_wallet';
        } elseif ($this->loan->loan_type_id === constants('LOAN_TYPES.OEPL')) {
            return 'easypay_wallet';
        } elseif ($this->loan->loan_type_id === constants('LOAN_TYPES.OUPL')) {
            return 'upay_wallet';
        } elseif ($this->loan->loan_type_id === constants('LOAN_TYPES.OFSL')) {
            return 'fastshift_wallet';
        }
    }

    public function parseDate($date)
    {
        return Carbon::parse($date)->setTimezone(constants('ARM_TIMEZONE'))->format('Y-m-d, H:i:s');
    }

    protected function extractDocumentAndSocCard($citizen)
    {
        $soc_card = [];

        foreach ($citizen['passports'] as $document) {
            if ($document['type'] == constants('SOC_CARD')) {
                $soc_card = $document;
            }
        }

        return [
            'doc' => $citizen->getPrimaryDocument(),
            'soc_card' => $soc_card,
        ];
    }

    public function failed(Exception $exception)
    {
        Log::error('Generate Lawyer Document failed', [
            'loan id' => $this->loan->id,
            'message' => $exception->getMessage(),
        ]);
    }
}
