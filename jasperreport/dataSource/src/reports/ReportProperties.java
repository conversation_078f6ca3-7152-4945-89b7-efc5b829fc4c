package reports;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.Properties;

public enum ReportProperties {
    INSTANCE;

    private Properties appProps;
    private final String rootPath = Thread.currentThread().getContextClassLoader().getResource("").getPath();

    ReportProperties() {
        appProps = new Properties();
        try {
            appProps.load(new FileInputStream(rootPath + "report.properties"));
        } catch (IOException e) {
        }
    }

    public String getProperty(String propName) {
        return appProps.getProperty(propName);
    }
}