/*
 * Copyright (C) 2005 - 2019 TIBCO Software Inc. All rights reserved.
 * http://www.jaspersoft.com.
 *
 * Unless you have purchased a commercial license agreement from Jaspersoft,
 * the following license terms apply:
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package reports.loanReport;

import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JRField;

import java.sql.*;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import reports.ReportLogger;
import reports.ReportProperties;
import reports.helpers.*;
import reports.db.Database;
import reports.loanReport.loanRepository.LoanRepository;
import reports.exception.ReportEmptyException;
import reports.service.nork.NorkService;

public class LoanDataSource implements JRDataSource {
    private Database db;
    private int report_loan_count = 0;
    private int reportLoanMaxCount = 1;
    private Map<String, Object> report;

    public LoanDataSource(String connectionHost) {
        this.db = new Database(connectionHost);
    }

    /**
     * Compose report data
     *
     * @param contractNumber
     */
    public void composeLoanReport(String contractNumber) {
        try (Connection conn = db.getConnection()) {
            LoanRepository loanRepository = new LoanRepository(conn);

            composeLoanData(loanRepository, contractNumber);
            composeLoanDocs(loanRepository, contractNumber);
            composePayment(loanRepository, contractNumber, (String) this.report.get("payment_type"));
        } catch (ReportEmptyException e) {
            this.setReportEmpty();
        } catch (Exception e) {
            ReportLogger.logger.error("Compose Loan Report Exception", e);
        }
    }

    /**
     * Preparing loan
     * <p>
     * * @param loanRepository
     *
     * @param contractNumber
     */
    private void composeLoanData(LoanRepository loanRepository, String contractNumber) throws ReportEmptyException {
        Map<String, Object> citizenMap = loanRepository.getLoanByContractNumber(contractNumber);

        if (citizenMap.isEmpty()) {
            ReportLogger.logger.info("Compose Loan Data ReportEmptyException");
            throw new ReportEmptyException();
        }

        try {
            NorkService norkService = new NorkService((String) citizenMap.get("work_data"));

            // modify and overwrite some DB values
            citizenMap.put("work_data", norkService.getPreparedWorkData());
        } catch (Exception e) {
            ReportLogger.logger.info("Compose Loan Data Nork Service Exception");
        }

        citizenMap.put("amount", Helper.currencyFormat((Double) citizenMap.get("amount")));
        citizenMap.put("loan_type_id", Helper.loanTypeMap.get(citizenMap.get("loan_type_id")));
        citizenMap.put("payment_type", Helper.paymentTypeMap.getOrDefault(citizenMap.get("payment_type"), ""));
        citizenMap.put("given_date", DateHelper.formatDateString(citizenMap.get("given_date"), DateHelper.DATE));
        citizenMap.put("expire_date", DateHelper.formatDateString(citizenMap.get("expire_date"), DateHelper.DATE));
        citizenMap.put("recording_date", DateHelper.formatDateString(citizenMap.get("recording_date"), DateHelper.DATE));
        citizenMap.put("sign_date", DateHelper.formatDateStringByTimeZone(citizenMap.get("sign_date"), DateHelper.DATE_TIME));
        citizenMap.put("birth_date", DateHelper.formatDateString(citizenMap.get("birth_date"), DateHelper.DATE));
        citizenMap.put("image_path", (String) citizenMap.get("image_path"));

        putToReport(citizenMap);
    }

    /**
     * Preparing loan documents
     *
     * @param loanRepository
     * @param contractNumber
     */
    private void composeLoanDocs(LoanRepository loanRepository, String contractNumber) throws ReportEmptyException {
        List<Map<String, Object>> docList = loanRepository.getDocumentsByContractNumber(contractNumber);

        if (!docList.isEmpty()) {
            Map<String, Object> docsMap = docList
                    .stream()
                    .collect(Collectors.toMap(s -> (String) s.get("document_type"), s -> (String) s.get("path")));

            putToReport(docsMap);
        }
    }

    /**
     * Preparing payment
     *
     * @param loanRepository
     * @param contractNumber
     * @param paymentType
     */
    private void composePayment(LoanRepository loanRepository, String contractNumber, String paymentType) throws ReportEmptyException {
        Map<String, Object> paymentMap = null;

        switch (paymentType) {
            case "Կանխիկ":
                paymentMap = loanRepository.getCashPaymentByContractNumber(contractNumber);
                break;
            case "Քարտային":
                paymentMap = loanRepository.getCardPaymentByContractNumber(contractNumber);
                break;
            case "Իդրամ":
                paymentMap = loanRepository.getIdramPaymentByContractNumber(contractNumber);
                break;
            case "Ապրանքի Տրամադրում":
                paymentMap = loanRepository.getProductProvisionByContractNumber(contractNumber);
        }

        if (paymentMap.isEmpty()) {
            ReportLogger.logger.info("Compose Payment ReportEmptyException");
            throw new ReportEmptyException();
        }

        // modify and overwrite DB values
        if (paymentMap.containsKey("payment_id")) {
            paymentMap.put("payment_id", paymentMap.get("payment_id").toString());
        }
        paymentMap.put("withdrawn", DateHelper.formatDateStringByTimeZone(paymentMap.get("withdrawn"), DateHelper.DATE_TIME));

        putToReport(paymentMap);
    }

    private void putToReport(Map report) {
        if (this.report == null) {
            this.report = report;
        } else {
            this.report.putAll(report);
        }
    }

    private void setReportEmpty() {
        this.reportLoanMaxCount = 0;
    }

    /**
     * Getting count of report_row
     */
    public boolean next() throws JRException {
        return (++report_loan_count <= reportLoanMaxCount);
    }

    /**
     * Getting values from data map by key
     */
    public Object getFieldValue(JRField field) throws JRException {
        try {
            return this.report.get(field.getName());
        } catch (NullPointerException e) {
            return " ";
        }
    }
}