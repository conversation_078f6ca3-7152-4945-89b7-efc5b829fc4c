<template>
  <button class="btn btn-default btn-primary m-2" @click="handleClick">
    {{ filter.name }}
  </button>
</template>

<script>
import defaults from 'lodash/defaults';

export default {
  props: {
    resourceName: {
      type: String,
      required: true,
    },
    filterKey: {
      type: String,
      required: true,
    },
  },

  methods: {
    async handleClick() {
      const FIRST_PAGE = 1;

      // Dispatch an action to reset the filter state in the Vuex store
      await this.$store.dispatch(`${this.resourceName}/resetFilterState`, {
        resourceName: this.resourceName,
      });

      // Call the cleanUpDatePicker function to clean up any date picker-related stuff
      this.cleanUpDatePicker();
      // Remove unnecessary query strings from route
      this.$router.push({
        query: defaults(
          {
            [this.resourceName + '_page']: FIRST_PAGE,
            [this.resourceName + '_filter']: '',
            [this.resourceName + '_per_page']: '',
          },
          this.$route.query
        ),
      });
    },

    cleanUpDatePicker() {
      const selectedEndRangeElement = document.querySelector(
        '.flatpickr-day.selected.endRange'
      );
      const selectedStartRangeElement = document.querySelector(
        '.flatpickr-day.selected.startRange'
      );
      const inRangeElements = document.querySelectorAll(
        '.flatpickr-day.inRange'
      );

      if (selectedEndRangeElement) {
        selectedEndRangeElement.classList.remove('endRange');
        selectedEndRangeElement.classList.remove('selected');
      }

      if (selectedStartRangeElement) {
        selectedStartRangeElement.classList.remove('startRange');
        selectedStartRangeElement.classList.remove('selected');
      }

      inRangeElements.forEach(element => {
        element.classList.remove('inRange');
      });
    },
  },

  computed: {
    filter() {
      return this.$store.getters[`${this.resourceName}/getFilter`](
        this.filterKey
      );
    },
  },
};
</script>
