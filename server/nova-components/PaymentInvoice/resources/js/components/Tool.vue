<template>
  <div>
    <heading class="mb-6">{{ __('Payment Invoice') }}</heading>

    <card class="flex flex-col items-center mt-10" style="min-height: 300px">
      <div class="auth-section">
        <input
          type="text"
          class="input"
          v-model="username"
          :placeholder="__('Username')"
        />
        <input
          type="text"
          class="input"
          v-model="password"
          :placeholder="__('Password')"
        />
      </div>
      <div class="date-picker-section">
        <calendar class="calendar"></calendar>
        <vue-datepicker
          :minDate="MIN_DATE"
          :placeholder="__('Date range')"
          @reset="handleDateReset"
          @update="handleDateChange"
          :format="DATE_FORMAT"
          :monthList="MONTHS_IN_ARM"
          :weekList="WEEKDAYS_IN_ARM"
          :fromText="__('Start From')"
          :toText="__('End To')"
          :resetText="__('Reset')"
          :confirmText="__('Submit')"
          ref="dateRef"
          class="date-picker"
        />
      </div>
      <div class="px-3 py-3 submit-button">
        <button
          :disabled="loading || disabled"
          class="btn btn-default btn-primary"
          @click="handleSubmit()"
        >
          {{ __('Export') }}
        </button>
        <p v-if="hasError" class="text-xs mt-2 text-danger">
          {{ firstError.code || firstError }}
        </p>
        <loading v-if="loading" large="true" class="flex m-auto" />
      </div>
    </card>
  </div>
</template>

<script>
import { Errors } from 'laravel-nova';

const MONTHS_IN_ARM = [
  'Հունվար',
  'Փետրվար',
  'Մարտ',
  'Ապրիլ',
  'Մայիս',
  'Հունիս',
  'Հուլիս',
  'Օգոստոս',
  'Սեպտեմբեր',
  'Հոկտեմբեր',
  'Նոյեմբեր',
  'Դեկտեմբեր',
];
const WEEKDAYS_IN_ARM = ['Կիր', 'Երկ', 'Երք', 'Չոր', 'Հին', 'Ուրբ', 'Շաբ'];
const DATE_FORMAT = 'YYYY-MM-DD';
const MIN_DATE = '01-01-2000';
export default {
  data() {
    return {
      loading: false,
      dates: {},
      username: '',
      password: '',
      errors: new Errors(),
      DATE_FORMAT,
      MIN_DATE,
      MONTHS_IN_ARM,
      WEEKDAYS_IN_ARM,
    };
  },
  computed: {
    disabled() {
      return false;
      return (
        !this.dates.hasOwnProperty('start') ||
        !this.dates.hasOwnProperty('end') ||
        !this.username ||
        !this.password
      );
    },
    firstError() {
      if (this.hasError) {
        return (
          this.errors.first('dates') ||
          this.errors.first('username') ||
          this.errors.first('password')
        );
      }
    },
    hasError() {
      return (
        this.errors.has('dates') ||
        this.errors.has('username') ||
        this.errors.has('password')
      );
    },
  },
  methods: {
    async handleSubmit() {
      this.loading = true;
      this.errors = new Errors();

      try {
        const response = await Nova.request().post(
          `/nova-vendor/payment-invoice/export`,
          {
            dates: this.dates,
            username: this.username,
            password: this.password,
          }
        );

        this.downloadFile(response.data);

        this.$toasted.show(this.__('The action ran successfully!'), {
          type: 'success',
        });
      } catch (error) {
        if (error.response.status == 422) {
          this.errors = new Errors(error.response.data.errors);
        } else {
          const message =
            error.response.data.message || this.__('Something went wrong');
          this.$toasted.show(message, { type: 'error' });
        }
      } finally {
        this.loading = false;
      }
    },

    handleDateChange(payload) {
      this.dates = { ...payload };
    },

    handleDateReset() {
      this.dates = {};
      this.$refs.dateRef.selectStartDate = undefined;
      this.$refs.dateRef.selectEndDate = undefined;
      this.$refs.dateRef.value = '';
    },

    downloadFile(data) {
      let fileLink = document.createElement('a');
      fileLink.href = data.download;
      fileLink.setAttribute('download', data.name);
      document.body.appendChild(fileLink);
      fileLink.click();
    },
  },
};
</script>
