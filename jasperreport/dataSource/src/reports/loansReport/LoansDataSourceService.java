package reports.loansReport;

import com.jaspersoft.jasperserver.api.metadata.common.service.RepositoryService;
import com.jaspersoft.jasperserver.api.metadata.jasperreports.service.ReportDataSourceService;
import net.sf.jasperreports.engine.JRParameter;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class LoansDataSourceService implements ReportDataSourceService {

    private LoansDataSource ds;
    private RepositoryService repository;

    public LoansDataSourceService() {
        this.ds = new LoansDataSource();
    }

    /* (non-Javadoc)
     * @see com.jaspersoft.jasperserver.api.metadata.jasperreports.service.ReportDataSourceService#closeConnection()
     */
    public void closeConnection() {
        // Do nothing
    }

    /* (non-Javadoc)
     * @see com.jaspersoft.jasperserver.api.metadata.jasperreports.service.ReportDataSourceService#setReportParameterValues(java.util.Map)
     */
    public void setReportParameterValues(Map parameterValues) {
        // Composing loan report by contract_number
        this.ds.composeLoansReport(parameterValues);

        parameterValues.put(JRParameter.REPORT_DATA_SOURCE, ds);
    }

    public RepositoryService getRepository() {
        return repository;
    }

    public void setRepository(RepositoryService repository) {
        this.repository = repository;
    }
}
