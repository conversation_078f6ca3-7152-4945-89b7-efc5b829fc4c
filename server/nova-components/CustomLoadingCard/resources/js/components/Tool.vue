<template>
  <card class="relative">
    <div
      v-if="loadingRequests.length > 0 || loading"
      class="rounded-lg flex items-center justify-center absolute pin z-50"
      :class="modeClass"
    >
      <loader class="text-60 default-loader" />
    </div>
    <slot />
  </card>
</template>

<script>
export default {
  props: {
    loading: {
      type: Boolean,
      default: true,
    },

    mode: {
      type: String,
      default: 'light',
      validator: function(value) {
        return ['light', 'dark'].indexOf(value) !== -1;
      },
    },
  },

  data() {
    return {
      loadingRequests: [],
    };
  },

  computed: {
    modeClass() {
      return this.mode == 'light' ? 'bg-white' : 'bg-90';
    },
  },

  mounted() {
    // Fetch and update loadingRequests using Axios interceptor
    Nova.request().interceptors.request.use(config => {
      if (
        config.url &&
        Nova.config.constants.NETWORK_LOADING_RESOURCES.includes(config.url)
      ) {
        this.loadingRequests.push(config);
      }

      return config;
    });

    Nova.request().interceptors.response.use(
      response => {
        this.loadingRequests = this.loadingRequests.filter(
          request => request.url !== response.config.url
        );
        return response;
      },
      error => {
        this.loadingRequests = this.loadingRequests.filter(
          request => request.url !== error.config.url
        );
        throw error;
      }
    );
  },
};
</script>

<style lang="scss">
.default-loader {
  position: absolute;
  top: 110px;
}
</style>
