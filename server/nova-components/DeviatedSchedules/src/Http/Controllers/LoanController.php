<?php

namespace Globalcredit\DeviatedSchedules\Http\Controllers;

use App\Exceptions\InvalidLoanException;
use DB;
use Illuminate\Routing\Controller;
use Laravel\Nova\Http\Requests\NovaRequest;

class LoanController extends Controller
{
    public function getLoanSchedule(NovaRequest $request)
    {
        $request->validate([
            'contract_number' => 'required|string',
        ]);

        $contract_number = $request->contract_number;

        if ($this->isDeviatedLoan($contract_number)) {
            throw new InvalidLoanException();
        }

        $deviated = $this->getDeviatedLoanSchedule($contract_number);
        $corrected = $this->getCorrectedLoanSchedule($contract_number);

        if (!$deviated) {
            throw new InvalidLoanException();
        }

        return response()->json([
            'deviated' => $deviated,
            'corrected' => $corrected,
        ]);
    }

    private function getDeviatedLoanSchedule($contract_number)
    {
        $schedule = DB::connection('hc_sqlsrv')->select('SELECT * from HC_graf(?, GETDATE())', [$contract_number]);

        return $schedule;
    }

    private function getCorrectedLoanSchedule($contract_number)
    {
        $schedule = DB::connection('hc_sqlsrv')->select('SELECT * from HC_graph_correct_50(?)', [$contract_number]);

        return $schedule;
    }

    public function correctLoanSchedule(NovaRequest $request)
    {
        $request->validate([
            'contract_number' => 'required|string',
        ]);

        DB::connection('hc_sqlsrv')->select(DB::raw("SET NOCOUNT ON ;EXEC Fix_All_50p '$request->contract_number'"));

        return response()->json([
            'corrected' => true,
        ]);
    }

    private function isDeviatedLoan($contract_number)
    {
        $loan = DB::connection('hc_sqlsrv')->select("SELECT * from Get_50PercRestructuredLIst() where fDGCODE = '$contract_number'");

        return empty($loan);
    }
}
