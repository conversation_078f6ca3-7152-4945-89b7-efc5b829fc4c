package reports.service.nork;

import org.joda.time.DateMidnight;
import org.joda.time.Months;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.json.JSONTokener;
import reports.ReportLogger;

import java.util.*;

public class NorkService {
    protected List<WorkData> workDataList = null;

    public NorkService(String norkWorkHistory) {
        this.workDataList = this.composeWorkData(norkWorkHistory);
    }

    /**
     * @param norkWorkHistory
     * @return List<WorkData> or null
     */
    private List<WorkData> composeWorkData(String norkWorkHistory) {
        List<WorkData> workDataList = new LinkedList<WorkData>();
        try {
            Object workData = new JSONTokener(norkWorkHistory).nextValue();

            if (workData instanceof JSONArray) {
                for (int i = 0; i < ((JSONArray) workData).length(); i++) {
                    JSONObject wd = (JSONObject) ((JSONArray) workData).get(i);
                    workDataList.add(new WorkData(wd));
                }
            } else if (workData instanceof JSONObject) {
                workDataList.add(new WorkData((JSONObject) workData));
            }

            return workDataList;
        } catch (JSONException e) {
            ReportLogger.logger.error("Compose Work Data JSONException", e);
        }

        return null;
    }

    /**
     * Parse Citizen work history and join by working place and first entry date
     *
     * @return String
     */
    public String getPreparedWorkData() {
        CitizenWorkHistory cw = new CitizenWorkHistory();
        Map<String, WorkData> workData = cw.preparedWorkData(workDataList);

        return cw.workHistoryToString(workData);
    }

    public int getCurrentWorkPlacesCount() {
        int count = 0;
        String hvhh = "";

        for (WorkData workDate : this.workDataList) {
            if (workDate.getExpiryDate() == null && !workDate.getHvhh().equals(hvhh)) {
                hvhh = workDate.getHvhh();
                count++;
            }
        }

        return count;
    }

    public Map<String, WorkData> getWorkPlaces() {
        CitizenWorkHistory cw = new CitizenWorkHistory();
        Map<String, WorkData> workData = cw.preparedWorkData(workDataList);

        Map<String, WorkData> workPlaces = new HashMap<>();

        WorkData current = getCurrentHighestSalaryWork(workData);
        workPlaces.put("current", current);
        workPlaces.put("penult", getPenultWorkPlace(workData, current.getHvhh(), current.getPajmanData()));

        return workPlaces;
    }

    public WorkData getCurrentHighestSalaryWork() {
        CitizenWorkHistory cw = new CitizenWorkHistory();
        Map<String, WorkData> workData = cw.preparedWorkData(workDataList);

        WorkData currentWorkPlace = new WorkData();

        double salary = 0;
        for (WorkData workPlace : workData.values()) {
            if (workPlace.getExpiryDate() == null && workPlace.getSalary() >= salary) {
                currentWorkPlace = workPlace;
                salary = workPlace.getSalary();
            }
        }

        return currentWorkPlace;
    }

    public WorkData getCurrentHighestSalaryWork(Map<String, WorkData> workData) {
        WorkData currentWorkPlace = new WorkData();

        double salary = 0;
        for (WorkData workPlace : workData.values()) {
            if (workPlace.getExpiryDate() == null && workPlace.getSalary() >= salary) {
                currentWorkPlace = workPlace;
                salary = workPlace.getSalary();
            }
        }

        return currentWorkPlace;
    }

    public WorkData getPenultWorkPlace(Map<String, WorkData> workData, String currentWorkPlaceHvhh, Date currentWorkPlacePajmanData) {
        WorkData penultWorkPlace = new WorkData();
        Date penultPajmanData = null;

        // If there aren't current workplace, we just get workplace with the biggest PajmanData as penult
        if (currentWorkPlaceHvhh == null && workData.size() > 0) {
            for (WorkData workPlace : workData.values()) {
                if (penultPajmanData == null || (workPlace.getPajmanData()).compareTo(penultPajmanData) > 0) {
                    penultWorkPlace = workPlace;
                    penultPajmanData = workPlace.getPajmanData();
                }
            }

            return penultWorkPlace;
        }

        // As penult workplace getting one with the biggest PajmanData and existing ExpiryDate, where Hvhh+PajmanData isn't same with current workplace
        for (WorkData workPlace : workData.values()) {
            if (workPlace.getExpiryDate() != null
                    && (workPlace.getHvhh() != currentWorkPlaceHvhh || (workPlace.getPajmanData()).compareTo(currentWorkPlacePajmanData) != 0)
                    && (penultPajmanData == null ||
                    ((workPlace.getPajmanData()).compareTo(penultPajmanData) > 0))
            ) {
                penultPajmanData = workPlace.getPajmanData();
                if (penultPajmanData.compareTo(currentWorkPlacePajmanData) < 0) {
                    penultWorkPlace = workPlace;
                }
            }
        }

        return penultWorkPlace;
    }

    /**
     * @return int
     */
    public int getWorkExperience(Date created_at) {
        int workExperience = 0;
        for (WorkData workDate : this.workDataList) {
            DateMidnight start = new DateMidnight(workDate.getEntryDate());
            DateMidnight end = new DateMidnight(workDate.getExpiryDate() != null
                    ? workDate.getExpiryDate()
                    : created_at
            );

            workExperience += Months.monthsBetween(start, end).getMonths();
        }

        return workExperience;
    }
}
