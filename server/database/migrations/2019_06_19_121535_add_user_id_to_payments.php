<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

class AddUserIdToPayments extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('gc_cash_payments', function ($table) {
            $table->integer('withdraw_operator_id')->nullable();
        });

        Schema::table('easypay_payments', function ($table) {
            $table->integer('withdraw_operator_id')->nullable();
        });

        Schema::table('upay_payments', function ($table) {
            $table->integer('withdraw_operator_id')->nullable();
        });

        Schema::table('idram_payments', function ($table) {
            $table->integer('withdraw_operator_id')->nullable();
        });

        Schema::table('idram_wallet_payments', function ($table) {
            $table->integer('withdraw_operator_id')->nullable();
        });

        Schema::table('wire_payments', function ($table) {
            $table->integer('withdraw_operator_id')->nullable();
        });

        Schema::table('card_to_card_payments', function ($table) {
            $table->integer('withdraw_operator_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('gc_cash_payments', function ($table) {
            $table->dropColumn('withdraw_operator_id');
        });

        Schema::table('easypay_payments', function ($table) {
            $table->dropColumn('withdraw_operator_id');
        });

        Schema::table('upay_payments', function ($table) {
            $table->dropColumn('withdraw_operator_id');
        });

        Schema::table('idram_payments', function ($table) {
            $table->dropColumn('withdraw_operator_id');
        });

        Schema::table('idram_wallet_payments', function ($table) {
            $table->dropColumn('withdraw_operator_id');
        });

        Schema::table('wire_payments', function ($table) {
            $table->dropColumn('withdraw_operator_id');
        });

        Schema::table('card_to_card_payments', function ($table) {
            $table->dropColumn('withdraw_operator_id');
        });
    }
}
