package reports.db;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import reports.ReportLogger;

public class QueryExecutor {

    private static final int STRING = 12;
    private static final int DOUBLE = 8;
    private static final int DATE = 91;
    private static final int TIMESTAMP = 93;
    private static final int INTEGER = 4;
    private static final int BOOLEAN = 16;

    private Connection conn;

    public QueryExecutor(Connection conn) {
        this.conn = conn;
    }

    public ArrayList<Map<String, Object>> executeSelectQueryByClauseMap(String query, Map<Integer, Object> parameters) {
        ResultSet rs = null;
        try (PreparedStatement stmt = conn.prepareStatement(query)) {
            setStatementByType(stmt, parameters);
            rs = stmt.executeQuery();

            ArrayList<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
            while (rs.next()) {
                ResultSetMetaData rsmd = rs.getMetaData();
                Map resultRow = new HashMap<String, Object>();

                for (int i = 1; i <= rsmd.getColumnCount(); ++i) {
                    resultRow.put(rsmd.getColumnName(i), getResultByColumnAndType(rs, rsmd, i));
                }

                result.add(resultRow);
            }

            return result;
        } catch (SQLException e) {
            ReportLogger.logger.error("Execute Select Query By Clause Map SQLException", e);
        } finally {
            closeResultSet(rs);
        }

        return null;
    }

    public Map<Integer, Map<String, Object>> executeSelectQueryByClauseMapKeyByInt(String query, Map<Integer, Object> parameters, String keyByColumn) {
        ResultSet rs = null;
        Map<Integer, Map<String, Object>> result = new HashMap<Integer, Map<String, Object>>();
        try (PreparedStatement stmt = conn.prepareStatement(query)) {
            setStatementByType(stmt, parameters);
            rs = stmt.executeQuery();

            while (rs.next()) {
                ResultSetMetaData rsmd = rs.getMetaData();
                Map resultRow = new HashMap<String, Object>();
                for (int i = 1; i <= rsmd.getColumnCount(); ++i) {
                    resultRow.put(rsmd.getColumnName(i), getResultByColumnAndType(rs, rsmd, i));
                }

                result.put(rs.getInt(keyByColumn), resultRow);
            }
        } catch (SQLException e) {
            ReportLogger.logger.error("Query Executor SQLException", e);
        } catch (Exception e) {
            ReportLogger.logger.error("Query Executor Exception", e);
        } finally {
            closeResultSet(rs);
            return result;
        }
    }

    private void setStatementByType(PreparedStatement stmt, Map<Integer, Object> parameters) {
        parameters.forEach((k, v) -> {
            try {
                if (v instanceof Date) {
                    stmt.setDate(k, (Date) v);
                } else if (v instanceof Integer) {
                    stmt.setInt(k, (Integer) v);
                } else if (v instanceof Double) {
                    stmt.setDouble(k, (Double) v);
                } else if (v instanceof Timestamp) {
                    stmt.setTimestamp(k, (Timestamp) v);
                } else if (v instanceof Boolean) {
                    stmt.setBoolean(k, (Boolean) v);
                } else { // String
                    stmt.setString(k, (String) v);
                }
            } catch (SQLException e) {
                ReportLogger.logger.error("Set Statement By Type SQLException", e);
            }
        });
    }

    /**
     * Selecting values by String clause and put it to Map
     * map key: DB column name, map value: DB column value
     *
     * @param query
     * @param clause
     * @return Map
     */
    public Map<String, Object> executeSelectQueryByClause(String query, String clause) {
        ResultSet rs = null;

        try (PreparedStatement stmt = conn.prepareStatement(query)) {
            stmt.setString(1, clause);
            rs = stmt.executeQuery();
            Map result = new HashMap<String, Object>();

            if (rs.next()) {
                ResultSetMetaData rsmd = rs.getMetaData();
                for (int i = 1; i <= rsmd.getColumnCount(); ++i) {
                    result.put(rsmd.getColumnName(i), getResultByColumnAndType(rs, rsmd, i));
                }
            }

            return result;
        } catch (SQLException e) {
            ReportLogger.logger.error("Execute Select Query By Clause SQLException", e);
        } finally {
            closeResultSet(rs);
        }

        return null;
    }

    /**
     * Selecting values by String clause and put it to List
     * Select result can have multiple rows
     *
     * @param query
     * @param clause
     * @return
     */
    public List<Map<String, Object>> executeMultipleRowSelectQueryByClause(String query, String clause) {
        ResultSet rs = null;

        try (PreparedStatement stmt = conn.prepareStatement(query)) {
            stmt.setString(1, clause);
            rs = stmt.executeQuery();

            List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
            while (rs.next()) {
                ResultSetMetaData rsmd = rs.getMetaData();
                Map resultRow = new HashMap<String, Object>();

                for (int i = 1; i <= rsmd.getColumnCount(); ++i) {
                    resultRow.put(rsmd.getColumnName(i), getResultByColumnAndType(rs, rsmd, i));
                }

                result.add(resultRow);
            }

            return result;
        } catch (SQLException e) {
            ReportLogger.logger.error("Execute Multiple Row Select Query By Clause SQLException", e);
        } finally {
            closeResultSet(rs);
        }

        return null;
    }

    /**
     * Return value form ResultSet by DB column name and type
     *
     * @param rs
     * @param rsmd
     * @param column
     * @return
     */
    private Object getResultByColumnAndType(ResultSet rs, ResultSetMetaData rsmd, int column) {
        try {
            switch (rsmd.getColumnType(column)) {
                case DATE:
                    return rs.getDate(column);
                case TIMESTAMP:
                    return rs.getTimestamp(column);
                case DOUBLE:
                    return rs.getDouble(column);
                case INTEGER:
                    return rs.getInt(column);
                case BOOLEAN:
                    return rs.getBoolean(column);
                default: // STRING
                    return rs.getString(column);
            }
        } catch (SQLException e) {
            ReportLogger.logger.error("Get Result By Column And Type SQLException", e);
        }
        return null;
    }

    /**
     * Close DB ResultSet
     *
     * @param rs
     */
    private void closeResultSet(ResultSet rs) {
        try {
            if (rs != null) {
                rs.close();
            }
        } catch (SQLException e) {
            ReportLogger.logger.error("Close ResultSet SQLException", e);
        }
    }
}
