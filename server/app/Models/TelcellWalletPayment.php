<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TelcellWalletPayment extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'transaction_id',
        'identifier',
        'wallet_id',
        'paid',
        'withdrawn',
        'payment_status',
        'phone_number',
    ];

    protected $dates = [
        'paid',
        'withdrawn',
    ];

    /**
     * Get the payment's loan.
     */
    public function loan()
    {
        return $this->morphToMany(Loan::class, 'paymentable')->withTimestamps();
    }
}
