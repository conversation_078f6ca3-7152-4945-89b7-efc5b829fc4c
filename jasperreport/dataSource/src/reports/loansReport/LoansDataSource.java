package reports.loansReport;

import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JRField;
import reports.ReportLogger;
import reports.helpers.*;
import reports.db.Database;

import java.sql.Connection;

import reports.loansReport.loansRepository.LoansRepository;
import reports.service.acra.AcraService;
import reports.service.nork.NorkService;
import reports.service.nork.WorkData;

import java.util.*;

public class LoansDataSource implements JRDataSource {

    private Database db;
    private Map<Object, Map<String, Object>> report;
    private Object[] reportKeySet;
    private Integer reportLoanCount = -1;
    private Integer reportLoanMaxCount = 0;


    public LoansDataSource() {
        this.db = new Database();
    }

    /**
     * @param parameterValues
     */
    public void composeLoansReport(Map parameterValues) {

        try (Connection conn = db.getConnection()) {
            LoansRepository loansRepository = new LoansRepository(conn);

            composeLoansData(loansRepository, parameterValues);
            composeContextData(loansRepository, parameterValues);
            composeFetchCitizenData(loansRepository, parameterValues);
            composeCitizenInfoData(loansRepository, parameterValues);
        } catch (Exception e) {
            ReportLogger.logger.error("Compose Loans Report Exception", e);
        }
    }

    public void composeLoansData(LoansRepository loansRepository, Map parameterValues) {
        Map<Object, Map<String, Object>> loanList = this.composeLoansDataByPayment(loansRepository, parameterValues);

        modifyOutputValues(loanList);
        putToReport(loanList);
    }

    public void composeContextData(LoansRepository loansRepository, Map parameterValues) {
        Map<Object, Map<String, Object>> loanList = loansRepository.getContextInfo(parameterValues, (int) parameterValues.get("payment_type"));

        putToReport(loanList);
    }

    public void composeFetchCitizenData(LoansRepository loansRepository, Map parameterValues) {
        Map<Object, Map<String, Object>> loanList = loansRepository.getFetchCitizen(parameterValues, (int) parameterValues.get("payment_type"));

        putToReport(loanList);
    }

    public void composeCitizenInfoData(LoansRepository loansRepository, Map parameterValues) {
        Map<Object, Map<String, Object>> loanList = loansRepository.getCitizenInfo(parameterValues, (int) parameterValues.get("payment_type"));

        for (Map.Entry<Object, Map<String, Object>> entry : loanList.entrySet()) {
            Object key = entry.getKey();
            Map value = entry.getValue();

            NorkService norkService = new NorkService((String) value.get("work_data"));
            AcraService acraService = new AcraService((String) value.get("acra_loans"));

            WorkData currentWorkPlace = norkService.getCurrentHighestSalaryWork();
            loanList.get(key).put("work_data", currentWorkPlace.getPajmanData());
            loanList.get(key).put("acra_start_date", acraService.acraHistoryStartDate());
        }

        putToReport(loanList);
    }

    private Map<Object, Map<String, Object>> composeLoansDataByPayment(LoansRepository loansRepository, Map parameterValues) {
        switch ((int) parameterValues.get("payment_type")) {
            case InputPaymentTypes.ALL:
                return loansRepository.getLoansByParameters(parameterValues);
            case InputPaymentTypes.CASH:
                return loansRepository.getCashLoansByParameters(parameterValues);
            case InputPaymentTypes.CARD:
                return loansRepository.getCardToCardLoansByParameters(parameterValues);
            case InputPaymentTypes.IDRAM:
                return loansRepository.getIdramLoansByParameters(parameterValues);
            case InputPaymentTypes.PRODUCT_PROVISION:
                return loansRepository.getProductProvisionLoansByParameters(parameterValues);
        }
        return null;
    }

    private void modifyOutputValues(Map<Object, Map<String, Object>> loanList) {

        for (Map.Entry<Object, Map<String, Object>> entry : loanList.entrySet()) {
            Object key = entry.getKey();
            Map value = entry.getValue();

            loanList.get(key).put("amount", value.get("amount"));
            loanList.get(key).put("salary", Helper.currencyFormat((Double) value.get("salary")));
            loanList.get(key).put("is_offline", Helper.loanSignTypeMap.get(value.get("is_offline")));
            loanList.get(key).put("gender", Helper.genderTypeMap.get(value.get("gender")));
            loanList.get(key).put("payment_type", Helper.paymentTypeMap.getOrDefault(value.get("payment_type"), ""));
            loanList.get(key).put("given_date", DateHelper.formatDateString(value.get("given_date"), DateHelper.DATE));
            loanList.get(key).put("expire_date", DateHelper.formatDateString(value.get("expire_date"), DateHelper.DATE));
            loanList.get(key).put("sign_date", DateHelper.formatDateStringByTimeZone(value.get("sign_date"), DateHelper.DATE_TIME));
            loanList.get(key).put("updated_at", DateHelper.formatDateStringByTimeZone(value.get("updated_at"), DateHelper.DATE_TIME));
            loanList.get(key).put("withdrawn", DateHelper.formatDateStringByTimeZone(value.get("withdrawn"), DateHelper.DATE_TIME));
            loanList.get(key).put("birth_date", DateHelper.formatDateString(value.get("birth_date"), DateHelper.DATE));
        }
    }

    private void putToReport(Map<Object, Map<String, Object>> report) {
        if (this.report == null) {
            this.report = report;
        } else {
            this.report.forEach((key, value) -> report.merge(key, value, (v1, v2) -> {
                v2.putAll(v1);
                return v2;
            }));
        }

        this.reportLoanMaxCount = this.report.size();
        this.reportKeySet = this.report.keySet().toArray();
    }

    /**
     * Getting count of report_row
     */
    public boolean next() throws JRException {
        return (++reportLoanCount < reportLoanMaxCount);
    }

    /**
     * Getting values from data map by key
     */
    public Object getFieldValue(JRField field) throws JRException {
        try {
            return this.report.get(this.reportKeySet[reportLoanCount]).get(field.getName());
        } catch (NullPointerException e) {
            return " ";
        }
    }

}
