package reports.loanReport.loanRepository;

import java.util.List;
import java.util.Map;

public interface ILoanRepository {
    public Map getLoanByContractNumber(String contractNumber);

    public List getDocumentsByContractNumber(String contractNumber);

    public Map getCashPaymentByContractNumber(String contractNumber);

    public Map getCardPaymentByContractNumber(String contractNumber);

    public Map getIdramPaymentByContractNumber(String contractNumber);
}
