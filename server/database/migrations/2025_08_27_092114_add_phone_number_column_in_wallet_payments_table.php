<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPhoneNumberColumnInWalletPaymentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('idram_wallet_payments', function (Blueprint $table) {
            $table->string('phone_number')->nullable();
            $table->dropColumn('withdraw_operator_id');
        });
        Schema::table('telcell_wallet_payments', function (Blueprint $table) {
            $table->string('phone_number')->nullable();
            $table->dropColumn('withdraw_operator_id');
        });
        Schema::table('easypay_wallet_payments', function (Blueprint $table) {
            $table->string('phone_number')->nullable();
            $table->dropColumn('withdraw_operator_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('idram_wallet_payments', function (Blueprint $table) {
            $table->dropColumn('phone_number');
            $table->integer('withdraw_operator_id')->nullable();
        });
        Schema::table('telcell_wallet_payments', function (Blueprint $table) {
            $table->dropColumn('phone_number');
            $table->integer('withdraw_operator_id')->nullable();
        });
        Schema::table('easypay_wallet_payments', function (Blueprint $table) {
            $table->dropColumn('phone_number');
            $table->integer('withdraw_operator_id')->nullable();
        });
    }
}
