<?php

namespace App\Models;

use App\Factory\DocumentServiceFactory;
use App\Services\SecurityUtilityService;
use App\Traits\ModelLocking;
use App\Traits\Transaction;
use Carbon\Carbon;
use function Functional\pluck;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Log;

class Loan extends Model
{
    use ModelLocking;
    use Transaction;

    // Loan current status, until validation step
    const PENDING = 'PENDING';
    // Loan Verified but HC and Payment fail
    const VERIFIED = 'VERIFIED';
    // Loan in system without making actual payment and storing in HC
    const FINISHED = 'FINISHED';
    // Loan is FINISHED before REJECTED, we do not show these loans to moderators,
    // because loan created from rejected device
    const FINISHED_WITHOUT_MODERATION = 'FINISHED_WITHOUT_MODERATION';
    // Loan completely Confirmed (now : without observing HC)
    const CONFIRMED = 'CONFIRMED';
    // Failed to make transfer and there are no fallback payment
    // In this case we don't store loan in HC
    const FAILED = 'FAILED';
    // Loan Verified but Payment fail
    const CONFIRMED_NO_PAY = 'CONFIRMED_NO_PAY';
    // OVL loan Verified, waiting for GC-agent checking
    const PROCESSING = 'PROCESSING';
    // OVL loan Verified, files uploaded, waiting for GC-agent-Admin confirmation
    const REVIEW = 'REVIEW';
    // OVL loan checked, waiting for GC-agent police file upload
    const PROCESSED = 'PROCESSED';
    // Police file uploaded, waiting for GC-agent-Admin confirmation
    const PLEDGED = 'PLEDGED';
    // OVL loan rejected by GC-agent-Admin
    const REJECTED = 'REJECTED';

    // Cashme loan Processed and Disbursed manualy in GC
    const MANUAL_DISBURSED = 'MANUAL_DISBURSED';
    // Loan closed manualy by GC request
    const MANUAL = 'MANUAL';

    // Loan current status, after top-up loan creation
    const PENDING_TOP_UP = 'PENDING_TOP_UP';
    // Loan is stored in HC and disbursed, but some actions needed via Nova yet
    const PARTIAL_PROCESSED = 'PARTIAL_PROCESSED';

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'status' => self::PENDING,
    ];

    protected $appends = ['expire_date', 'payment', 'loan_schedule'];

    protected $casts = ['osm' => 'float'];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'public_id',
        'loan_type_id',
        'payment_id',
        'payment_type',
        'amount',
        'months',
        'monthly_payment',
        'last_month_payment',
        'total',
        'interest_rate',
        'real_interest_rate',
        'apr',
        'next_payment_date',
        'fico_score',
        'existing_fico_score',
        'status',
        'verified_at',
        'confirmed_at',
        'sign_date',
        'contract_number',
        'credit_code',
        'notification_method',
        'failed_payment',
        'dispute_solution_method',
        'withdrawal_fee',
        'is_offline',
        'service_fee_rate',
        'nominal_rate',
        'payment_status',
        'osm',
        'dsti',
        'top_up_amount',
        'prev_loan_type_id',
    ];

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        'sign_date',
        'confirmed_at',
        'next_payment_date',
    ];

    /**
     * Business that belong to the citizen.
     */
    public function business()
    {
        return $this->hasOne('App\Models\Business');
    }

    /**
     * Get agents assigned to loan.
     */
    public function agents()
    {
        return $this->belongsToMany('App\Models\User', 'loan_agent')
            ->withTimestamps()
            ->withPivot('notes');
    }

    /**
     * Get all of the owning models.
     */
    public function payments()
    {
        return $this->hasMany('App\Models\Paymentable');
    }

    public function loan_detail()
    {
        return $this->hasOne('App\Models\LoanDetail');
    }

    // Return payment model Name
    public function getPaymentTypeAttribute()
    {
        return $this->payments()->latest()->first()->paymentable_type ?? null;
    }

    // Return current payment
    public function getPaymentAttribute()
    {
        $paymentable_method = $this->paymentableMethod();
        if ($paymentable_method === null) {
            return null;
        }

        return $this->$paymentable_method()->latest()->first();
    }

    // Return paymentable method
    // E.g. cardToCardPayment, cashPayment ...
    public function paymentableMethod()
    {
        $paymentable_type = $this->payment_type;

        return $paymentable_type !== null ? Paymentable::PAYMENTABLE_TYPES[$paymentable_type] : null;
    }

    public function cardToCardPayment()
    {
        return $this->morphedByMany(CardToCardPayment::class, 'paymentable');
    }

    public function idramWalletPayment()
    {
        return $this->morphedByMany(IdramWalletPayment::class, 'paymentable');
    }

    public function walletLoanPayment()
    {
        return $this->morphedByMany(WalletLoanPayment::class, 'paymentable');
    }

    public function paymentable()
    {
        return $this->morphTo('paymentable', 'payment_type', 'payment_id');
    }

    public function cashPayment()
    {
        return $this->morphedByMany(CashPayment::class, 'paymentable');
    }

    public function wirePayment()
    {
        return $this->morphedByMany(WirePayment::class, 'paymentable');
    }

    public function easypayWalletPayment()
    {
        return $this->morphedByMany(EasypayWalletPayment::class, 'paymentable');
    }

    public function telcellWalletPayment()
    {
        return $this->morphedByMany(TelcellWalletPayment::class, 'paymentable');
    }

    public function productProvision()
    {
        return $this->morphedByMany(ProductProvision::class, 'paymentable');
    }

    public function veloxPayment()
    {
        return $this->morphedByMany(VeloxPayment::class, 'paymentable');
    }

    public function manualTransfer()
    {
        return $this->morphedByMany(ManualTransfer::class, 'paymentable');
    }

    /**
     * Get the citizen that belong to the loan.
     */
    public function citizen()
    {
        return $this->hasOne('App\Models\Citizen');
    }

    /**
     * Get idram details that belong to the loan.
     */
    public function wallet_details()
    {
        return $this->hasOne('App\Models\WalletDetail');
    }

    /**
     * Get loan documents.
     */
    public function documents()
    {
        return $this->hasMany('App\Models\LoanDocument');
    }

    /**
     * Get loan documents.
     */
    public function documents_histories()
    {
        return $this->hasMany('App\Models\LoanDocumentHistory');
    }

    /**
     * Tags that belong to the loans.
     */
    public function tags()
    {
        return $this->belongsToMany('App\Models\Tag');
    }

    /**
     * Get the type of loan.
     */
    public function loan_type()
    {
        return $this->belongsTo('App\Models\LoanType');
    }

    /**
     * Get the type of loan.
     */
    public function prev_loan_type()
    {
        return $this->belongsTo('App\Models\LoanType', 'prev_loan_type_id');
    }

    /**
     * Get loan meta.
     */
    public function loan_security()
    {
        return $this->hasOne('App\Models\LoanSecurity')->latest();
    }

    public function credit_limit_calculations()
    {
        return $this->hasMany('App\Models\CreditLine\CreditLimitCalculation');
    }

    public function top_up_selection()
    {
        return $this->hasOne('App\Models\TopUpSelection');
    }

    /**
     * Get loan schedule.
     * Note: Always get loan schedules via loan_schedule attribute, not directly by relationship.
     */
    public function loan_schedules()
    {
        return $this->hasMany('App\Models\LoanSchedule');
    }

    public function getLoanScheduleAttribute()
    {
        return $this->loan_schedules()->whereDisabled(false)->orderBy('date')->get();
    }

    public function deleteLoanSchedule()
    {
        $loan_schedule_ides = $this->loan_schedule->pluck('id');

        $this->loan_schedules()
            ->whereIn('id', $loan_schedule_ides)
            ->delete();
    }

    public function storeLoanSchedule($schedule)
    {
        $loan_schedule_ides = $this->loan_schedule->pluck('id');
        // We need to disable old schedules, before inserting new group
        $this->loan_schedules()
            ->whereIn('id', $loan_schedule_ides)
            ->update(['disabled' => true]);

        $this->loan_schedules()->createMany($schedule);
    }

    public function vehicle()
    {
        return $this->hasOne('App\Models\Vehicle');
    }

    public function mortgage()
    {
        return $this->hasOne('App\Models\Mortgage');
    }

    public function pipe()
    {
        return $this->hasOne('App\Models\Pipe');
    }

    public function moderator()
    {
        return $this->belongsToMany('App\Models\User', 'loan_moderator')
            ->withTimestamps()
            ->withPivot([
                'notes',
                'confirmed_at',
                'rejected_at',
                'assigned_at',
            ]);
    }

    public function solar_panel()
    {
        return $this->hasOne('App\Models\SolarPanel');
    }

    public function solar_panel_mortgage()
    {
        return $this->hasOne('App\Models\SolarPanelMortgage');
    }

    public function real_estate_mortgage()
    {
        return $this->hasOne('App\Models\RealEstateMortgage');
    }

    public function transactions()
    {
        return $this->hasMany('App\Models\CreditLine\Transaction');
    }

    public function transaction_documents()
    {
        return $this->hasManyThrough('App\Models\TransactionDocument', 'App\Models\CreditLine\Transaction');
    }

    public function loan_types()
    {
        return $this->belongsTo('App\Models\LoanType', 'loan_type_id');
    }

    public function purchase_session()
    {
        return $this->hasMany('App\Models\CreditLine\PurchaseSession');
    }

    public function getDocumentsAttribute()
    {
        if ($this->isTopUp()) {
            return $this->documents()->where('batch', constants('LOAN_DOCUMENT_BATCHES.TOP_UP_LOAN'))->get();
        }

        return $this->documents()->get();
    }

    public function getLoansCredDefaultsAttribute()
    {
        return $this->loan_type->hc_defaults->where('table_name', $this->resolveTable())->reduce(function ($carry, $default) {
            $key = $default['key'];
            $carry[$key] = $default['value'];

            return $carry;
        }, []);
    }

    public function getLoansCredDisbursementDefaultsAttribute()
    {
        $loan_type = $this->isConverted() ? $this->prev_loan_type : $this->loan_type;

        return $loan_type->hc_defaults->where('table_name', 'LOANS_CRED_DISBURSEMENTS')->reduce(function ($carry, $default) {
            $key = $default['key'];
            $carry[$key] = $default['value'];

            return $carry;
        }, []);
    }

    public function getMortgageDefaultsAttribute()
    {
        return $this->loan_type->hc_defaults->where('table_name', 'MORTGAGES')->reduce(function ($carry, $default) {
            $key = $default['key'];
            $carry[$key] = $default['value'];

            return $carry;
        }, []);
    }

    public function getMortOtherDefaultsAttribute()
    {
        return $this->loan_type->hc_defaults->where('table_name', 'MORT_OTHER')->reduce(function ($carry, $default) {
            $key = $default['key'];
            $carry[$key] = $default['value'];

            return $carry;
        }, []);
    }

    public function getMortVehicleDefaultsAttribute()
    {
        return $this->loan_type->hc_defaults->where('table_name', 'MORT_VEHICLE')->reduce(function ($carry, $default) {
            $key = $default['key'];
            $carry[$key] = $default['value'];

            return $carry;
        }, []);
    }

    public function getLoansCredInterestDefaultsAttribute()
    {
        $loan_type = $this->isConverted() ? $this->prev_loan_type : $this->loan_type;

        return $loan_type->hc_defaults->where('table_name', 'LOANS_CRED_INTERESTS')->reduce(function ($carry, $default) {
            $key = $default['key'];
            $carry[$key] = $default['value'];

            return $carry;
        }, []);
    }

    public function getLoansCredLineBreakDefaultsAttribute()
    {
        $loan_type = $this->isConverted() ? $this->prev_loan_type : $this->loan_type;

        return $loan_type->hc_defaults->where('table_name', 'LOANS_CRED_LINE_BREAKS')->reduce(function ($carry, $default) {
            $key = $default['key'];
            $carry[$key] = $default['value'];

            return $carry;
        }, []);
    }

    public function getLastPaymentAttribute()
    {
        return $this->loan_schedule->sortByDesc('date')->first();
    }

    public function getFirstPaymentAttribute()
    {
        return $this->loan_schedule->first();
    }

    public function isWithdrawn()
    {
        return $this->payment != null && $this->payment->withdrawn != null;
    }

    public function isConfirmed()
    {
        return $this->status === Loan::CONFIRMED;
    }

    public function isProcessing()
    {
        return $this->status === Loan::PROCESSING;
    }

    public static function getByPublicId($public_id)
    {
        $loan = Loan::where('public_id', $public_id);

        if ($loan->first() && $loan->first()->isTopUp()) {
            $documents_batch = constants('LOAN_DOCUMENT_BATCHES.TOP_UP_LOAN');
        } else {
            $documents_batch = constants('LOAN_DOCUMENT_BATCHES.LOAN');
        }

        return $loan->with(['documents' => function ($q) use ($documents_batch) {
            $q->where('public', true)
                ->where('batch', $documents_batch);
        }, 'citizen.spouse'])
            ->first();
    }

    public function getLastAssignedAgent()
    {
        return $this->agents()->orderBy('pivot_updated_at', 'desc')->first();
    }

    public static function getDailyTotal($loan_type_id, $night_start, $night_end)
    {
        return Loan::select(DB::raw('count(*) as count, sum(amount) as amount'))
            ->where('confirmed_at', '<=', Carbon::createFromTimeString(
                $night_start,
                constants('ARM_TIMEZONE'))->setTimezone(constants('UTC_TIME')))
            ->where('confirmed_at', '>=', Carbon::createFromTimeString(
                $night_end,
                constants('ARM_TIMEZONE'))->setTimezone(constants('UTC_TIME')))
            ->where('loan_type_id', $loan_type_id)
            ->first();
    }

    public static function getDailyNightTotal($loan_type_id, $night_start, $night_end)
    {
        return Loan::select(DB::raw(' count(*) as count, sum(amount) as amount'))
            ->where('confirmed_at', '>', Carbon::createFromTimeString(
                $night_start,
                constants('ARM_TIMEZONE'))->setTimezone(constants('UTC_TIME')))
            ->orWhere('confirmed_at', '<', Carbon::createFromTimeString(
                $night_end,
                constants('ARM_TIMEZONE'))->setTimezone(constants('UTC_TIME')))
            ->whereDate('confirmed_at', Carbon::today())
            ->where('loan_type_id', $loan_type_id)
            ->first();
    }

    public static function getContractNumbersBySocCard($soc_card)
    {
        return DB::table('loans')
            ->join('citizens', 'loans.id', '=', 'citizens.loan_id')
            ->join('citizen_passports', 'citizens.id', '=', 'citizen_passports.citizen_id')
            ->select('loans.*', 'citizens.id', 'citizens.loan_id', 'citizen_passports.passport_number', 'citizen_passports.type')
            ->where('citizen_passports.type', 'SOC_CARD')
            ->where('citizen_passports.passport_number', $soc_card)
            ->whereIn('loans.status', [Loan::CONFIRMED, Loan::FINISHED, Loan::FINISHED_WITHOUT_MODERATION, Loan::CONFIRMED_NO_PAY])
            ->pluck('contract_number');
    }

    public static function getLoanWithDocuments($id)
    {
        return self::with('documents')->find($id);
    }

    public function scopeExpired($query)
    {
        return $query->where('status', self::PROCESSING)->whereHas('solar_panel', function ($q) {
            $q->whereDate('expiration_date', '<', now());
        });
    }

    public function scopeNotExpired($query)
    {
        return $query->where('status', self::PROCESSING)->whereHas('solar_panel', function ($q) {
            $q->whereDate('expiration_date', '>=', now());
        });
    }

    public function scopeWithCashPayment($query, $include_history = false)
    {
        $composed_query = $query
            ->select('loans.*', 'cp.paid', 'cp.withdrawn', 'cp.withdraw_operator_id')
            ->leftJoin('cash_payments as cp', function ($join) {
                $join->on('loans.payment_id', '=', 'cp.id');
                $join->on('loans.payment_type', '=', DB::raw("'App\Models\CashPayment'"));
            })
            ->where(function ($query) {
                $query->where('payment_type', 'App\Models\CashPayment')
                        ->where('loans.status', Loan::CONFIRMED);
            });

        if ($include_history) {
            // Since loan can have loan history,
            //that's why we need to check whether loan was being CONFIRMED in the past or not,
            //otherwise we will lose PENDING_TOP_UP loans details
            $composed_query->orWhereHas('loan_histories', function ($q) {
                $q->where('payment_type', 'App\Models\CashPayment')
                    ->where('status', Loan::CONFIRMED);
            });
        }

        return $composed_query;
    }

    public function scopeWithTelcellPayment($query)
    {
        return $query
            ->select('loans.*', 'cp.paid', 'cp.withdrawn', 'cp.withdraw_operator_id')
            ->leftJoin('cash_payments as cp', function ($join) {
                $join->on('loans.payment_id', '=', 'cp.id');
                $join->on('loans.payment_type', '=', DB::raw("'App\Models\CashPayment'"));
            })
            ->where(function ($query) {
                $query->where(function ($q) {
                    $this->applyTelcellCashPaymentConditions($q, 'loans');
                    // Since loan can have loan history,
                    //that's why we need to check whether loan was being CONFIRMED in the past or not and its withdrawal type is TELCELL
                    // otherwise we will lose PENDING_TOP_UP loans details
                })->orWhereHas('loan_histories', function ($q) {
                    $q->select('loan_histories.*', 'cp.paid', 'cp.withdrawn', 'cp.withdraw_operator_id')
                        ->leftJoin('cash_payments as cp', 'loan_histories.payment_id', '=', 'cp.id');

                    $this->applyTelcellCashPaymentConditions($q, 'loan_histories');
                });
            });
    }

    protected function applyTelcellCashPaymentConditions($query, $table_name)
    {
        $query->where($table_name.'.payment_type', 'App\Models\CashPayment')
            ->where('cp.type', CashPayment::TELCELL)
            ->where($table_name.'.status', Loan::CONFIRMED);
    }

    public function scopeWithAllWithdrawnPayment($query)
    {
        $this->selectPaymentColumns($query, 'loans');
        $this->joinPaymentTypes($query, Paymentable::PAYMENTABLE_TYPES_WITH_ALIAS, 'loans');

        return $this->allWithdrawnLoans($query);
    }

    public function scopeEligibleForPeriodicTopUp($query)
    {
        return $query
            ->where('loan_type_id', constants('LOAN_TYPES.OCL'))
            ->whereNull('prev_loan_type_id')
            ->where(function ($query) {
                $query->where(function ($q) {
                    $q->where('status', self::CONFIRMED)
                        // Calculate the loan end date by adding 'months' to 'sign_date' to avoid getting old & possibly closed loans
                        ->whereRaw('(sign_date + (months || \' months\')::interval) > ?', [now()])
                        // At least 2 months should be passed of loan signing
                        ->where('sign_date', '<', now()->subMonths(2))
                        ->whereNotNull('payment_type')
                        ->whereNotNull('payment_id');
                })
                ->orWhere(function ($q) {
                    // This covers the cases when the user may have Pending or Verified top-up offer on specified loan
                    $q->where('status', '!=', self::CONFIRMED)
                        ->whereNotNull('top_up_amount');
                });
            })
            ->orderBy('id');
    }

    protected function allWithdrawnLoans($query)
    {
        return $query
            ->where('loans.status', Loan::CONFIRMED)
            ->orWhere(function ($q) {
                $this->applyPaymentWithdrawnConditions($q, 'loans');
            })
            // Since loan can have loan history,
            //that's why we need to check whether loan was being CONFIRMED in the past or not,
            //otherwise we will lose PENDING_TOP_UP loans details
            ->orWhereHas('loan_histories', function ($q) {
                $this->selectPaymentColumns($q, 'loan_histories');
                $this->joinPaymentTypes($q, Paymentable::PAYMENTABLE_TYPES_WITH_ALIAS, 'loan_histories');

                $q->where(function ($q) {
                    $this->applyPaymentWithdrawnConditions($q, 'loan_histories');
                });
            });
    }

    protected function selectPaymentColumns($query, $table_name)
    {
        $query->select($table_name.'.*',
            'cp.paid', 'cp.withdrawn', 'cp.withdraw_operator_id',
            'ctc.paid', 'ctc.withdrawn', 'ctc.withdraw_operator_id',
            'iwp.paid', 'iwp.withdrawn',
            'ewp.paid', 'ewp.withdrawn',
            'twp.paid', 'twp.withdrawn',
            'wp.paid', 'wp.withdrawn', 'wp.withdraw_operator_id',
            'pp.paid', 'pp.withdrawn', 'pp.withdraw_operator_id',
            'wlp.paid', 'wlp.withdrawn', 'wlp.withdraw_operator_id',
            'vp.paid', 'vp.withdrawn'
        );
    }

    protected function joinPaymentTypes($query, $payment_types_with_alias, $table_name)
    {
        foreach ($payment_types_with_alias as $alias => $payment_type) {
            $table = (new $payment_type())->getTable();

            $query->leftJoin($table.' as '.$alias, function ($join) use ($alias, $payment_type, $table_name) {
                $this->joinPaymentType($join, $payment_type, $alias, $table_name);
            });
        }
    }

    protected function joinPaymentType($join, $payment_type, $alias, $table_name)
    {
        $join->on($table_name.'.payment_id', '=', $alias.'.id')
            ->on($table_name.'.payment_type', '=', DB::raw("'".$payment_type."'"));
    }

    protected function applyPaymentWithdrawnConditions($query, $table_name)
    {
        foreach (Paymentable::PAYMENTABLE_TYPES_WITH_ALIAS as $alias => $payment_type) {
            $this->paymentWithdrawnConditions($query, $table_name, $payment_type, $alias);
        }
    }

    protected function paymentWithdrawnConditions($q, $table_name, $payment_type, $alias)
    {
        $q->orWhere(function ($q) use ($table_name, $payment_type, $alias) {
            $q->where($table_name.'.payment_type', $payment_type)
                ->whereNotNull($alias.'.withdrawn');
        });
    }

    public function isVehicle()
    {
        return $this->loan_type->id === constants('LOAN_TYPES.OVL');
    }

    public function isMiasnakan()
    {
        return $this->loan_type->id === constants('LOAN_TYPES.OCL');
    }

    public function isIdramWalletLoan()
    {
        return $this->loan_type->id === constants('LOAN_TYPES.OIDL');
    }

    public function isTelcellWalletLoan()
    {
        return $this->loan_type->id === constants('LOAN_TYPES.OTCL');
    }

    public function isFastshiftWalletLoan()
    {
        return $this->loan_type->id === constants('LOAN_TYPES.OFSL');
    }

    public function isEasypayWalletLoan()
    {
        return $this->loan_type->id === constants('LOAN_TYPES.OEPL');
    }

    public function isUpayWalletLoan()
    {
        return $this->loan_type->id === constants('LOAN_TYPES.OUPL');
    }

    public function updateLoanDocumentsAccess($documents, $is_public)
    {
        return $this->documents()
            ->whereIn('document_type', $documents)
            ->update(['public' => $is_public]);
    }

    public function setPaymentStatus($status)
    {
        return $this->whereId($this->id)->update(['payment_status' => $status]);
    }

    public function setPaymentDetails($payment_id, $payment_type)
    {
        return $this->whereId($this->id)
            ->update([
                'payment_id' => $payment_id,
                'payment_type' => $payment_type,
            ]);
    }

    public function scopeFinished($query)
    {
        return $query->where('status', Loan::FINISHED);
    }

    public function scopeFinishedWithoutModeration($query)
    {
        return $query->where('status', Loan::FINISHED_WITHOUT_MODERATION);
    }

    public function scopeAssigned($query, $user_id)
    {
        return $query->whereHas('moderator', function ($query) use ($user_id) {
            $query->where('user_id', $user_id);
        });
    }

    public function scopeIsNotAssigned($query)
    {
        return $query->doesntHave('moderator');
    }

    public static function getLoanBySSN($ssn)
    {
        return self::bySsn($ssn)
            ->where('loans.created_at', '>=', now()->subDays(constants('FICO_ACTIVE_DAYS')))
            ->where('loans.created_at', '<=', now())->get();
    }

    public static function getLoanBySsnAndLoanType($ssn, $loan_type_id)
    {
        return self::bySsn($ssn)->where('loan_type_id', $loan_type_id)->first();
    }

    public function scopeBySsn($query, $ssn)
    {
        return $query->join('citizens', 'loans.id', '=', 'citizens.loan_id')
            ->join('citizen_passports', 'citizens.id', '=', 'citizen_passports.citizen_id')
            ->select('loans.*', 'citizen_passports.passport_number', 'citizen_passports.type')
            ->where('citizen_passports.passport_number', $ssn)
            ->where('citizen_passports.type', constants('SOC_CARD'))
            ->where('loans.status', Loan::CONFIRMED);
    }

    public function getExpireDateAttribute()
    {
        return Carbon::parse($this->sign_date)->addMonths($this->months)->format(constants('EKENG_DATE_FORMAT'));
    }

    public function loan_histories()
    {
        return $this->hasMany('App\Models\LoanHistory');
    }

    public function confirmed_loan_histories()
    {
        return $this->loan_histories()->where('status', self::CONFIRMED);
    }

    public function processLoanUpdates(array $updatable_attrs = [], $loan_security_id = null)
    {
        $this->startTransaction(function () use ($updatable_attrs, $loan_security_id) {
            // We need to store loan current state into the loan_histories table before updating
            $current_loan_attrs_as_array = $this->toArray();
            $this->loan_histories()->create(array_merge($current_loan_attrs_as_array, [
                'loan_security_id' => $loan_security_id,
            ]));

            $result = array_merge($current_loan_attrs_as_array, $updatable_attrs);

            $this->update($result);
            // loan has been updated, refresh loan model
            $this->fresh();
        });
    }

    public function isConverted()
    {
        return !is_null($this->prev_loan_type_id);
    }

    public static function getLoanByContractNumber($contract_number)
    {
        return self::where('contract_number', $contract_number)->first();
    }

    private function resolveTable()
    {
        if ($this->loan_type_id == constants('LOAN_TYPES.OBL')) {
            return 'LOANS_CRED2';
        }

        return 'LOANS_CRED';
    }

    public function isTopUp(): bool
    {
        return !is_null($this->top_up_amount);
    }

    public function isOVIL(): bool
    {
        $loan_security = $this->loan_security;

        return SecurityUtilityService::isOVIL($loan_security->loan_type_id, $loan_security->loan_subtype_id);
    }

    public function isOVPL(): bool
    {
        $loan_security = $this->loan_security;

        return SecurityUtilityService::isOVPL($loan_security->loan_type_id, $loan_security->loan_subtype_id);
    }

    public function isOVTL(): bool
    {
        $loan_security = $this->loan_security;

        return SecurityUtilityService::isOVTL($loan_security->loan_type_id, $loan_security->loan_subtype_id);
    }

    /**
     * Archive the loan documents for the current loan.
     *
     * @param array|string $archivable_document_names names of documents to archive
     * @param bool         $force_archive             force archiving even if loan already has stored history
     *
     * @return void
     */
    public function archiveLoanDocuments($archivable_document_names = [], bool $force_archive = false)
    {
        $archivable_document_names = is_array($archivable_document_names) ? $archivable_document_names : [$archivable_document_names];

        $latest_loan_history = $this->confirmed_loan_histories()
            ->latest()
            ->first();

        // Check if archiving should be skipped,
        //coz we need to store documents history once for per confirmed_loan_history
        if (is_null($latest_loan_history) ||
            (!$force_archive && LoanDocumentHistory::where('loan_id', $this->id)->where('loan_history_id', $latest_loan_history->id)->exists())
        ) {
            Log::info('Archive loan documents skipped', [
                'loan_id' => $this->id,
                'loan_history_id' => $latest_loan_history->id ?? null,
            ]);

            return;
        }

        // Get archivable document names if not provided
        if (empty($archivable_document_names)) {
            $loan_document_service = DocumentServiceFactory::build($this->loan_type_id);
            $archivable_document_names = pluck($loan_document_service->getDocumentTypes(false, $this), 'name');
        }

        $current_loan_documents_attrs_as_array = $this->documents()
            ->whereIn('document_type', $archivable_document_names)
            ->get()
            ->toArray();
        $this->documents_histories()->createMany($current_loan_documents_attrs_as_array);

        // Update loan history IDs for archived documents
        $this->documents_histories()
            ->whereIn('document_type', $archivable_document_names)
            ->whereNull('loan_history_id')
            ->update(['loan_history_id' => $latest_loan_history->id]);
    }

    public function composeTopUpSerialNumber()
    {
        $loan_top_up_count = $this->confirmed_loan_histories()->whereNotNull('top_up_amount')->count();

        return sprintf('%03d', $loan_top_up_count + 1);
    }

    public function archive_citizen()
    {
        $citizen = $this->citizen()->first();
        $citizen->initial_loan_id = $citizen->loan_id;
        $citizen->loan_id = null;
        $citizen->save();

        return $citizen;
    }

    public function detectOVLType()
    {
        if ($this->isOVIL()) {
            return 'VEHICLE_IMPORT';
        }

        if ($this->isOVTL()) {
            return 'TRADE';
        }

        return 'PLEDGE';
    }
}
