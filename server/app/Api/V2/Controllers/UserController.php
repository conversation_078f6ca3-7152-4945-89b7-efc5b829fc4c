<?php

namespace App\Api\V2\Controllers;

use App\Api\V2\Requests\UpdateEmailRequest;
use App\Exceptions\Pallaton\DuplicateUserException;
use App\Http\Controllers\Controller;
use Auth;
use Exception;
use Illuminate\Http\JsonResponse;
use Log;

class UserController extends Controller
{
    public function me(): JsonResponse
    {
        $recognition_service = resolve('App\Interfaces\IRecognitionService');

        $profile = Auth::guard()->user()->profile;

        if ($profile) {
            $profile = $profile->toArray();

            $ekeng_photo = $recognition_service->getEkengPhotoFromS3($profile['ssn'], null, true);

            if ($ekeng_photo) {
                $profile['uri'] = $ekeng_photo;
            }
        }

        return response()->json($profile);
    }

    public function updateEmail(UpdateEmailRequest $request)
    {
        try {
            $email = $request->get('email');

            $auth_service = resolve('App\Interfaces\Pallaton\IAuthService');
            $auth_service->updateEmail($email);
        } catch (DuplicateUserException $e) {
            Log::warning('Update email, DuplicateUserException', ['message' => $e->getMessage()]);
            throw $e;
        } catch (Exception $e) {
            Log::error('Update email, Exception', ['message' => $e->getMessage()]);
            throw $e;
        }
    }
}
