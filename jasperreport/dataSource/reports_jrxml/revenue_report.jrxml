<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.13.0.final using JasperReports Library version 6.13.0-46ada4d1be8f3c5985fd0b6146f3ed44caed6f05  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Revenue" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="4d563e21-dbf8-4365-8175-365f23c56a2b">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="Local DB"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="214"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="778"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w1" value="611"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w2" value="378"/>
	<parameter name="DATE_PARAM" class="java.sql.Date"/>
	<parameter name="MONTH_BUDGET_CASHME" class="java.lang.Double">
		<defaultValueExpression><![CDATA[200000000.0]]></defaultValueExpression>
	</parameter>
	<parameter name="MONTH_BUDGET_CAR_LOAN" class="java.lang.Double">
		<defaultValueExpression><![CDATA[300000000.0]]></defaultValueExpression>
	</parameter>
	<parameter name="MONTH_BUDGET_CAR_PURCHASE" class="java.lang.Double">
		<defaultValueExpression><![CDATA[300000000.0]]></defaultValueExpression>
	</parameter>
	<parameter name="MONTH_BUDGET_IQOS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2000000.0]]></defaultValueExpression>
	</parameter>
	<parameter name="MONTH_BUDGET_CASHME_QNT" class="java.lang.Integer">
		<defaultValueExpression><![CDATA[400]]></defaultValueExpression>
	</parameter>
	<parameter name="MONTH_BUDGET_CAR_LOAN_QNT" class="java.lang.Integer">
		<defaultValueExpression><![CDATA[80]]></defaultValueExpression>
	</parameter>
	<parameter name="MONTH_BUDGET_CAR_PURCHASE_QNT" class="java.lang.Integer">
		<defaultValueExpression><![CDATA[10]]></defaultValueExpression>
	</parameter>
	<parameter name="MONTH_BUDGET_IQOS_QNT" class="java.lang.Integer">
		<defaultValueExpression><![CDATA[30]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[SELECT loans.loan_type_id,  loans.amount, loans.created_at FROM loans
WHERE  loans.status = 'CONFIRMED'
AND CAST( loans.created_at as date) <= CAST($P{DATE_PARAM} as date)
AND EXTRACT(MONTH from CAST( loans.created_at as date)) = EXTRACT(MONTH from CAST($P{DATE_PARAM} as date))
AND EXTRACT(YEAR from CAST( loans.created_at as date)) = EXTRACT(YEAR from CAST($P{DATE_PARAM} as date))]]>
	</queryString>
	<field name="loan_type_id" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.label" value="name"/>
	</field>
	<field name="amount" class="java.lang.Double">
		<property name="newproperty_1" value="NEW_VALUE"/>
	</field>
	<field name="created_at" class="java.sql.Date"/>
	<variable name="Revenue_Month_OCL" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{loan_type_id}==1?$F{amount}:0]]></variableExpression>
	</variable>
	<variable name="Revenue_Month_OVL" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{loan_type_id}==2?$F{amount}:0]]></variableExpression>
	</variable>
	<variable name="Revenue_Month_COMMON" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{loan_type_id}==7?$F{amount}:0]]></variableExpression>
	</variable>
	<variable name="Revenue_Month_OIQL" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{loan_type_id}==4?$F{amount}:0]]></variableExpression>
	</variable>
	<variable name="Budget_Month_OCL" class="java.lang.Double">
		<variableExpression><![CDATA[$P{MONTH_BUDGET_CASHME}]]></variableExpression>
		<initialValueExpression><![CDATA[0]]></initialValueExpression>
	</variable>
	<variable name="Budget_Month_OVL" class="java.lang.Double">
		<variableExpression><![CDATA[$P{MONTH_BUDGET_CAR_LOAN}]]></variableExpression>
		<initialValueExpression><![CDATA[0]]></initialValueExpression>
	</variable>
	<variable name="Budget_Month_COMMON" class="java.lang.Double">
		<variableExpression><![CDATA[$P{MONTH_BUDGET_CAR_PURCHASE}]]></variableExpression>
		<initialValueExpression><![CDATA[0]]></initialValueExpression>
	</variable>
	<variable name="Budget_Month_OIQL" class="java.lang.Double">
		<variableExpression><![CDATA[$P{MONTH_BUDGET_IQOS}]]></variableExpression>
		<initialValueExpression><![CDATA[0]]></initialValueExpression>
	</variable>
	<variable name="Variation_Month_OIQL" class="java.lang.Double">
		<variableExpression><![CDATA[$P{MONTH_BUDGET_IQOS} - $V{Revenue_Month_OIQL}]]></variableExpression>
		<initialValueExpression><![CDATA[0]]></initialValueExpression>
	</variable>
	<variable name="Variation_Month_OVL" class="java.lang.Double">
		<variableExpression><![CDATA[$P{MONTH_BUDGET_CAR_LOAN} - $V{Revenue_Month_OVL}]]></variableExpression>
		<initialValueExpression><![CDATA[0]]></initialValueExpression>
	</variable>
	<variable name="Variation_Month_OCL" class="java.lang.Double">
		<variableExpression><![CDATA[$P{MONTH_BUDGET_CASHME} - $V{Revenue_Month_OCL}]]></variableExpression>
		<initialValueExpression><![CDATA[0]]></initialValueExpression>
	</variable>
	<variable name="Variation_Month_COMMON" class="java.lang.Double">
		<variableExpression><![CDATA[$P{MONTH_BUDGET_CAR_PURCHASE} - $V{Revenue_Month_COMMON}]]></variableExpression>
		<initialValueExpression><![CDATA[0]]></initialValueExpression>
	</variable>
	<variable name="Variation_Month_OVL_percentage" class="java.lang.Double">
		<variableExpression><![CDATA[($V{Revenue_Month_OVL} / $P{MONTH_BUDGET_CAR_LOAN})*100]]></variableExpression>
		<initialValueExpression><![CDATA[0]]></initialValueExpression>
	</variable>
	<variable name="Variation_Month_OCL_percentage" class="java.lang.Double">
		<variableExpression><![CDATA[($V{Revenue_Month_OCL} / $P{MONTH_BUDGET_CASHME})*100]]></variableExpression>
		<initialValueExpression><![CDATA[0]]></initialValueExpression>
	</variable>
	<variable name="Variation_Month_COMMON_percentage" class="java.lang.Double">
		<variableExpression><![CDATA[($V{Revenue_Month_COMMON} / $P{MONTH_BUDGET_CAR_PURCHASE})*100]]></variableExpression>
		<initialValueExpression><![CDATA[0]]></initialValueExpression>
	</variable>
	<variable name="Variation_Month_OIQL_percentage" class="java.lang.Double">
		<variableExpression><![CDATA[($V{Revenue_Month_OIQL} / $P{MONTH_BUDGET_IQOS})*100]]></variableExpression>
		<initialValueExpression><![CDATA[0]]></initialValueExpression>
	</variable>
	<variable name="Revenue_Month_OCL_qnt" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{loan_type_id}==1?1:0]]></variableExpression>
	</variable>
	<variable name="Revenue_Month_OVL_qnt" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{loan_type_id}==2?1:0]]></variableExpression>
	</variable>
	<variable name="Revenue_Month_COMMON_qnt" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{loan_type_id}==7?1:0]]></variableExpression>
	</variable>
	<variable name="Revenue_Month_OIQL_qnt" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{loan_type_id}==4?1:0]]></variableExpression>
	</variable>
	<variable name="Budget_Month_OCL_qnt" class="java.lang.Integer">
		<variableExpression><![CDATA[$P{MONTH_BUDGET_CASHME_QNT}]]></variableExpression>
	</variable>
	<variable name="Budget_Month_OVL_qnt" class="java.lang.Integer">
		<variableExpression><![CDATA[$P{MONTH_BUDGET_CAR_LOAN_QNT}]]></variableExpression>
	</variable>
	<variable name="Budget_Month_OIQL_qnt" class="java.lang.Integer">
		<variableExpression><![CDATA[$P{MONTH_BUDGET_IQOS_QNT}]]></variableExpression>
	</variable>
	<variable name="Budget_Month_COMMON_qnt" class="java.lang.Integer">
		<variableExpression><![CDATA[$P{MONTH_BUDGET_CAR_PURCHASE_QNT}]]></variableExpression>
	</variable>
	<variable name="Variation_Month_OIQL_qnt" class="java.lang.Integer">
		<variableExpression><![CDATA[$P{MONTH_BUDGET_IQOS_QNT} - $V{Revenue_Month_OIQL_qnt}]]></variableExpression>
		<initialValueExpression><![CDATA[0]]></initialValueExpression>
	</variable>
	<variable name="Variation_Month_COMMON_qnt" class="java.lang.Integer">
		<variableExpression><![CDATA[$P{MONTH_BUDGET_CAR_PURCHASE_QNT} - $V{Revenue_Month_COMMON_qnt}]]></variableExpression>
		<initialValueExpression><![CDATA[0]]></initialValueExpression>
	</variable>
	<variable name="Variation_Month_OVL_qnt" class="java.lang.Integer">
		<variableExpression><![CDATA[$P{MONTH_BUDGET_CAR_LOAN_QNT} - $V{Revenue_Month_OVL_qnt}]]></variableExpression>
		<initialValueExpression><![CDATA[0]]></initialValueExpression>
	</variable>
	<variable name="Variation_Month_OCL_qnt" class="java.lang.Integer">
		<variableExpression><![CDATA[$P{MONTH_BUDGET_CASHME_QNT} - $V{Revenue_Month_OCL_qnt}]]></variableExpression>
		<initialValueExpression><![CDATA[0]]></initialValueExpression>
	</variable>
	<variable name="Variation_Month_OCL_percentage_qnt" class="java.lang.Double">
		<variableExpression><![CDATA[(new Double($V{Revenue_Month_OCL_qnt}) / new Double($P{MONTH_BUDGET_CASHME_QNT}))*100]]></variableExpression>
		<initialValueExpression><![CDATA[0]]></initialValueExpression>
	</variable>
	<variable name="Variation_Month_OVL_percentage_qnt" class="java.lang.Double">
		<variableExpression><![CDATA[(new Double($V{Revenue_Month_OVL_qnt}) / new Double($P{MONTH_BUDGET_CAR_LOAN_QNT}))*100]]></variableExpression>
		<initialValueExpression><![CDATA[0]]></initialValueExpression>
	</variable>
	<variable name="Variation_Month_COMMON_percentage_qnt" class="java.lang.Double">
		<variableExpression><![CDATA[(new Double($V{Revenue_Month_COMMON_qnt}) / new Double($P{MONTH_BUDGET_CAR_PURCHASE_QNT}))*100]]></variableExpression>
		<initialValueExpression><![CDATA[0]]></initialValueExpression>
	</variable>
	<variable name="Variation_Month_OIQL_percentage_qnt" class="java.lang.Double">
		<variableExpression><![CDATA[(new Double($V{Revenue_Month_OIQL_qnt}) /new Double($P{MONTH_BUDGET_IQOS_QNT}))*100]]></variableExpression>
		<initialValueExpression><![CDATA[0]]></initialValueExpression>
	</variable>
	<variable name="Revenue_Day_OCL" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[DATEFORMAT($F{created_at},"dd/MM/yyyy").equals(DATEFORMAT($P{DATE_PARAM},"dd/MM/yyyy")) && $F{loan_type_id}==1 ? $F{amount}:0]]></variableExpression>
	</variable>
	<variable name="Revenue_Day_OVL" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[DATEFORMAT($F{created_at},"dd/MM/yyyy").equals(DATEFORMAT($P{DATE_PARAM},"dd/MM/yyyy")) && $F{loan_type_id}==2 ? $F{amount}:0]]></variableExpression>
	</variable>
	<variable name="Revenue_Day_COMMON" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[DATEFORMAT($F{created_at},"dd/MM/yyyy").equals(DATEFORMAT($P{DATE_PARAM},"dd/MM/yyyy")) && $F{loan_type_id}==7 ? $F{amount}:0]]></variableExpression>
	</variable>
	<variable name="Revenue_Day_OIQL" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[DATEFORMAT($F{created_at},"dd/MM/yyyy").equals(DATEFORMAT($P{DATE_PARAM},"dd/MM/yyyy")) && $F{loan_type_id}==4 ? $F{amount}:0]]></variableExpression>
	</variable>
	<variable name="Revenue_Day_OCL_qnt" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[DATEFORMAT($F{created_at},"dd/MM/yyyy").equals(DATEFORMAT($P{DATE_PARAM},"dd/MM/yyyy")) && $F{loan_type_id}==1 ? 1:0]]></variableExpression>
	</variable>
	<variable name="Revenue_Day_OVL_qnt" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[DATEFORMAT($F{created_at},"dd/MM/yyyy").equals(DATEFORMAT($P{DATE_PARAM},"dd/MM/yyyy")) && $F{loan_type_id}==2 ? 1:0]]></variableExpression>
	</variable>
	<variable name="Revenue_Day_COMMON_qnt" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[DATEFORMAT($F{created_at},"dd/MM/yyyy").equals(DATEFORMAT($P{DATE_PARAM},"dd/MM/yyyy")) && $F{loan_type_id}==7 ? 1:0]]></variableExpression>
	</variable>
	<variable name="Revenue_Day_OIQL_qnt" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[DATEFORMAT($F{created_at},"dd/MM/yyyy").equals(DATEFORMAT($P{DATE_PARAM},"dd/MM/yyyy")) && $F{loan_type_id}==4 ? 1:0]]></variableExpression>
	</variable>
	<variable name="Revenue_Day_Total" class="java.lang.Double">
		<variableExpression><![CDATA[$V{Revenue_Day_OCL} + $V{Revenue_Day_OVL}  + $V{Revenue_Day_COMMON}  + $V{Revenue_Day_OIQL}]]></variableExpression>
	</variable>
	<variable name="Revenue_Month_TOTAL" class="java.lang.Double">
		<variableExpression><![CDATA[$V{Revenue_Month_OCL} + $V{Revenue_Month_OVL} + $V{Revenue_Month_COMMON} + $V{Revenue_Month_OIQL}]]></variableExpression>
	</variable>
	<variable name="Budget_Month_Total" class="java.lang.Double">
		<variableExpression><![CDATA[$V{Budget_Month_OIQL} + $V{Budget_Month_COMMON} + $V{Budget_Month_OCL} + $V{Budget_Month_OVL}]]></variableExpression>
	</variable>
	<variable name="Variation_Month_Total" class="java.lang.Double">
		<variableExpression><![CDATA[$V{Variation_Month_OIQL} + $V{Variation_Month_COMMON} + $V{Variation_Month_OCL} + $V{Variation_Month_OVL}]]></variableExpression>
	</variable>
	<variable name="Variations_Month_Total_precet" class="java.lang.Double">
		<variableExpression><![CDATA[($V{Revenue_Month_TOTAL}/$V{Budget_Month_Total})*100]]></variableExpression>
	</variable>
	<variable name="Revenue_Day_Total_qnt" class="java.lang.Double">
		<variableExpression><![CDATA[$V{Revenue_Day_OCL_qnt} + $V{Revenue_Day_OVL_qnt} + $V{Revenue_Day_COMMON_qnt} + $V{Revenue_Day_OIQL_qnt}]]></variableExpression>
	</variable>
	<variable name="Revenue_Month_Total_qnt" class="java.lang.Integer">
		<variableExpression><![CDATA[$V{Revenue_Month_OIQL_qnt} + $V{Revenue_Month_COMMON_qnt} + $V{Revenue_Month_OVL_qnt} + $V{Revenue_Month_OCL_qnt}]]></variableExpression>
	</variable>
	<variable name="Budget_Month_Total_qnt" class="java.lang.Integer">
		<variableExpression><![CDATA[$V{Budget_Month_COMMON_qnt} + $V{Budget_Month_OIQL_qnt} + $V{Budget_Month_OCL_qnt} + $V{Budget_Month_OVL_qnt}]]></variableExpression>
	</variable>
	<variable name="Variation_Month_Total_qnt" class="java.lang.Integer">
		<variableExpression><![CDATA[$V{Variation_Month_COMMON_qnt} + $V{Variation_Month_OIQL_qnt} + $V{Variation_Month_OVL_qnt} + $V{Variation_Month_OCL_qnt}]]></variableExpression>
	</variable>
	<variable name="Variation_Total_perc" class="java.lang.Double">
		<variableExpression><![CDATA[(new Double($V{Revenue_Month_Total_qnt}) / new Double($V{Budget_Month_Total_qnt}))*100]]></variableExpression>
	</variable>
	<summary>
		<band height="454">
			<textField pattern="#,##0.###">
				<reportElement x="280" y="50" width="100" height="30" uuid="14f4f3fd-d773-4e5b-baeb-ec25a85015f7"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Revenue_Month_OCL}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="280" y="80" width="100" height="30" uuid="91af6ea2-d443-4ba8-ac2e-25b2f7976fa7"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Revenue_Month_OVL}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="280" y="110" width="100" height="30" uuid="dbea8e09-6c12-4b20-a203-5d8b2efac6f1"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Revenue_Month_COMMON}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="280" y="140" width="100" height="30" uuid="384cc6b7-adda-4406-bf27-e8c3f7652664"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Revenue_Month_OIQL}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="80" y="50" width="100" height="30" uuid="7eace163-c9c5-4f6d-91f1-f92e2f00fbec"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA["CashMe"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="80" y="80" width="100" height="30" uuid="a50d251c-df56-41f2-963d-018b1bab7599"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA["Car Loan"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="80" y="110" width="100" height="30" uuid="83cd5464-2c7c-4f8f-8a49-6ac075c53b0f"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA["Car purchase"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="80" y="140" width="100" height="30" uuid="7ede92ed-5324-4026-a941-cfb8b0e396d2"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA["IQOS"]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="380" y="80" width="100" height="30" uuid="1ade4ed8-20ba-4e3e-8053-8c3955045969"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Budget_Month_OVL}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="380" y="50" width="100" height="30" uuid="6ec6d59b-b12b-4f01-a540-0bcbb3441a77"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Budget_Month_OCL}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="380" y="110" width="100" height="30" uuid="00158c5c-bc08-4315-958a-e9f00011d16d"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Budget_Month_COMMON}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="380" y="140" width="100" height="30" uuid="db17e5f4-843d-4221-8c63-1d8cff07dfc7"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Budget_Month_OIQL}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="480" y="140" width="100" height="30" uuid="b63a16df-4518-414b-a78e-ca4cc11d1167"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Variation_Month_OIQL}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="480" y="50" width="100" height="30" uuid="f3828345-c205-4002-a885-6d3fc7163856"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Variation_Month_OCL}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="480" y="80" width="100" height="30" uuid="fe961c67-4204-4b19-9ae0-cf9b84a3c24f"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Variation_Month_OVL}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="480" y="110" width="100" height="30" uuid="90d81df9-23dd-45bf-8ced-b489768488a8"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Variation_Month_COMMON}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###%">
				<reportElement x="580" y="80" width="100" height="30" uuid="52fce0a6-9365-4538-9cd9-60b9478e3216"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Variation_Month_OVL_percentage}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###%">
				<reportElement x="580" y="50" width="100" height="30" uuid="5c24d423-5fdd-47b1-8cc5-7271e82c2410"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Variation_Month_OCL_percentage}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###%">
				<reportElement x="580" y="110" width="100" height="30" uuid="e51b8292-72bb-4eae-b2d7-47a1a8e43bd4"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Variation_Month_COMMON_percentage}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###%">
				<reportElement x="580" y="140" width="100" height="30" uuid="1da021a8-fec6-4d10-a70b-83481fbf2975"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Variation_Month_OIQL_percentage}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="80" y="260" width="100" height="30" uuid="bed4a75b-ba8b-48f2-871f-fb6205ffb116"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA["CashMe"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="80" y="290" width="100" height="30" uuid="68b06cec-5a3e-4be9-893c-0c09ff6b441f"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA["Car Loan"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="80" y="320" width="100" height="30" uuid="24f97322-28e6-4fd4-8290-61066cf69abb"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA["Car purchase"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="80" y="350" width="100" height="30" uuid="7e9746e3-956b-4793-802e-a06cdac778cc"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA["IQOS"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="80" y="230" width="100" height="30" forecolor="#FFFF00" backcolor="#000000" uuid="afb2ed16-6e44-46b6-904a-f5e28b5d93f7"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Type of Product"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="180" y="230" width="100" height="30" forecolor="#FFFF00" backcolor="#000000" uuid="3f1d0638-65d2-4859-b8fd-b71d57e79a24"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Quantity Day"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="280" y="230" width="100" height="30" forecolor="#FFFF00" backcolor="#000000" uuid="b8c0da7f-b924-44aa-8cc4-86083750c0e5"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Quantity Month"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="380" y="230" width="100" height="30" forecolor="#FFFF00" backcolor="#000000" uuid="03c2da2e-c23d-4507-8b4c-f32332a88614"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Month Budget, qnt"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="480" y="230" width="100" height="30" forecolor="#FFFF00" backcolor="#000000" uuid="46effee0-1f7e-44d9-aa08-eef246ad9013"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Variation, qnt"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="580" y="230" width="100" height="30" forecolor="#FFFF00" backcolor="#000000" uuid="84fe1180-6276-4168-a97d-57b8fe37242a"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Variation, %"]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="280" y="260" width="100" height="30" uuid="c91aabd3-47ec-4d3a-9103-f5e2e748a385"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Revenue_Month_OCL_qnt}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="280" y="290" width="100" height="30" uuid="2083e837-4643-452c-af51-a7d408718b38"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Revenue_Month_OVL_qnt}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="280" y="320" width="100" height="30" uuid="38173736-a7b0-4920-b268-89e6e7e53b10"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Revenue_Month_COMMON_qnt}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="280" y="350" width="100" height="30" uuid="87f2d411-2b79-4e34-be2a-84d2604fcecf"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Revenue_Month_OIQL_qnt}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="380" y="260" width="100" height="30" uuid="871e04de-2d06-4442-9967-54a925f5d01a"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Budget_Month_OCL_qnt}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="380" y="290" width="100" height="30" uuid="2f65e252-4bf1-407e-abff-d8f6556aa678"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Budget_Month_OVL_qnt}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="380" y="320" width="100" height="30" uuid="ba7f1365-1aa4-44ea-af86-7cd55453231f"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Budget_Month_OIQL_qnt}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="380" y="350" width="100" height="30" uuid="4c368582-a40b-4cc6-af6c-a055a8cc8e8b"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Budget_Month_COMMON_qnt}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="480" y="260" width="100" height="30" uuid="883847ec-ec93-4f69-9a57-11eaeb99f53c"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Variation_Month_OCL_qnt}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="480" y="290" width="100" height="30" uuid="c1188dae-dc5b-4cc1-a187-e6da97852d21"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Variation_Month_OVL_qnt}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="480" y="350" width="100" height="30" uuid="afbcdf6b-3400-46f1-bf8e-738b3ac87bec"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Variation_Month_COMMON_qnt}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="480" y="320" width="100" height="30" uuid="670150d0-afb1-4ee1-9ec4-c662ae6ebc74"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Variation_Month_OIQL_qnt}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="80" y="20" width="100" height="30" forecolor="#FFFF00" backcolor="#000000" uuid="753f2c9b-7e43-4cae-903e-7fd2c9355094"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Type of Product"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="180" y="20" width="100" height="30" forecolor="#FFFF00" backcolor="#000000" uuid="a81a53ed-3a4f-4a89-ab50-d2d34c719114"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Revenue Day"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="280" y="20" width="100" height="30" forecolor="#FFFF00" backcolor="#000000" uuid="21f1f3dd-7ea1-4d6b-9222-48b1c1283346"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Revenue Month"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="380" y="20" width="100" height="30" forecolor="#FFFF00" backcolor="#000000" uuid="ac3907c5-f9ad-45ef-b8c8-5f57288a039b"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Month Budget"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="480" y="20" width="100" height="30" forecolor="#FFFF00" backcolor="#000000" uuid="f4fd7ade-87cb-4107-b3b7-80aea04a8852"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Variations, amount"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="580" y="20" width="100" height="30" forecolor="#FFFF00" backcolor="#000000" uuid="eb5050b4-67c1-4bdd-8f0d-bdc3b78e8251"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Variations, %"]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###%">
				<reportElement x="580" y="290" width="100" height="30" uuid="09c0d227-cdca-4bb3-937c-0448810c3b79"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Variation_Month_OVL_percentage_qnt}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###%">
				<reportElement x="580" y="260" width="100" height="30" uuid="650c5315-a279-4110-a59d-32c9f544dfff"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Variation_Month_OCL_percentage_qnt}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###%">
				<reportElement x="580" y="320" width="100" height="30" uuid="c5f6470c-c4d9-420a-b8b5-afc370b3a5b7"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Variation_Month_COMMON_percentage_qnt}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###%">
				<reportElement x="580" y="350" width="100" height="30" uuid="09d797e1-3235-48ba-a6e9-04ecbfdfe843"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Variation_Month_OIQL_percentage_qnt}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="180" y="50" width="100" height="30" uuid="935775a1-ed94-44a2-bbf9-5a2391f4cc2c"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Revenue_Day_OCL}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="180" y="80" width="100" height="30" uuid="52b4b7d6-5dd6-4df4-889f-782e2acfca09"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Revenue_Day_OVL}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="180" y="110" width="100" height="30" uuid="21b3c2ca-6e8f-4dff-8a26-814a4d144ffb"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Revenue_Day_COMMON}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="180" y="140" width="100" height="30" uuid="56bb9ae7-4dac-4d07-a14a-f9529c1c1167"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Revenue_Day_OIQL}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="180" y="260" width="100" height="30" uuid="07fa8345-f2ec-43d5-9fc8-c0f26950aeea"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Revenue_Day_OCL_qnt}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="180" y="290" width="100" height="30" uuid="665596cf-19e0-4547-a267-6e6ab6a8e9fc"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Revenue_Day_OVL_qnt}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="180" y="320" width="100" height="30" uuid="6f8e835c-3c26-45cf-ab0f-c427714be6b5"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Revenue_Day_COMMON_qnt}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="180" y="350" width="100" height="30" uuid="55beff06-fd6f-4f3e-b665-3c0f58e5958f"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Revenue_Day_OIQL_qnt}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="80" y="380" width="100" height="30" uuid="e0c7c89c-2904-4724-b323-29c9f45c43a2"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Double"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA["Total"]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="180" y="170" width="100" height="30" uuid="3a28ba0f-0f82-496b-9e2c-37efc8cf0599"/>
				<box>
					<pen lineStyle="Double"/>
					<topPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Revenue_Day_Total}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="280" y="170" width="100" height="30" uuid="9ea4fe59-3c38-4114-a084-340e4c6ac22a"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Double"/>
					<topPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Revenue_Month_TOTAL}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="380" y="170" width="100" height="30" uuid="839b2a41-8e8f-45a1-a1e4-67e3987c0ec7"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Double"/>
					<topPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Budget_Month_Total}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="480" y="170" width="100" height="30" uuid="6206d0c7-1c6b-4ecd-b8d6-65622055eb55"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Double"/>
					<topPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Variation_Month_Total}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###%">
				<reportElement x="580" y="170" width="100" height="30" uuid="f0184a54-f84a-4ac8-9633-fabb663dd299"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Double"/>
					<topPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Variations_Month_Total_precet}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="80" y="170" width="100" height="30" uuid="d45f96a0-f527-4831-96a3-d9993d7cabd4"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Double"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA["Total"]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="180" y="380" width="100" height="30" uuid="713e4f10-434b-42b8-867e-5f83d3dae6ba"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Double"/>
					<topPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Revenue_Day_Total_qnt}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="280" y="380" width="100" height="30" uuid="e78c604a-bb57-4944-82f6-43285494ea9f"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Double"/>
					<topPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Revenue_Month_Total_qnt}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="380" y="380" width="100" height="30" uuid="4c05dd1a-a104-4867-b5a6-23951a1a4a98"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Double"/>
					<topPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Budget_Month_Total_qnt}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###">
				<reportElement x="480" y="380" width="100" height="30" uuid="1741765c-96c3-41eb-bb6e-ad7b53abed05"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Double"/>
					<topPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Variation_Month_Total_qnt}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###%">
				<reportElement x="580" y="380" width="100" height="30" uuid="224d47c6-0b6f-449a-8fb1-b3396b8cf870"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Double"/>
					<topPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Double" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$V{Variation_Total_perc}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
