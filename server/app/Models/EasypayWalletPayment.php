<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class EasypayWalletPayment extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'wallet_id',
        'paid',
        'withdrawn',
        'withdraw_operator_id',
        'payment_status',
        'transaction_id',
        'identifier',
        'phone_number',
    ];

    protected $dates = [
       'paid',
       'withdrawn',
    ];

    /**
     * Get the payment's loan.
     */
    public function loan()
    {
        return $this->morphToMany(Loan::class, 'paymentable')->withTimestamps();
    }
}
