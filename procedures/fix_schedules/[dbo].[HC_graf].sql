USE [appbuffer]
GO
/****** Object:  UserDefinedFunction [dbo].[HC_graf]    Script Date: 10/02/21 6:21:05 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

--DECLARE @loanid AS VARCHAR(150) = 'CV20-VK553',@rdate AS DATETIME=getdate()

Create or ALTER function [dbo].[HC_graf]
(
    @loanid VARCHAR(150),
    @rdate AS DATETIME
)
Returns @graph table
--declare @graph table
(
    fdate datetime,
    princ decimal(18, 2),
    Intr decimal(18, 2),
    sfvan decimal(18, 2),
    mtot decimal(18, 2)
)

--as
begin
    DECLARE @lstf AS INT
    DECLARE @dsi AS VARCHAR(30)
    Declare @AVsvf as decimal(18, 2)
    Declare @checkLastFinc as integer
    Declare @curs as decimal(18, 2)
    Declare @AVint decimal(18, 2)
    SELECT @dsi = CN.fDGISN
    FROM [asvark].dbo.CONTRACTS CN
    WHERE CN.fDGCODE = @loanid


    SELECT @lstf = max(fINC)
    FROM [asvark].[dbo].[AGRSCHEDULE]
    WHERE fAGRISN = @dsi
          AND datediff(d, fDATE, @rdate) >= 0
    set @AVsvf = asvark.[dbo].[asfb_GetRem]('R@', @dsi, @rdate)
    set @AVint = asvark.[dbo].[asfb_GetRem]('R2', @dsi, @rdate)
    set @checkLastFinc =
    (
        SELECT count(*) mx
        FROM [asvark].[dbo].[AGRSCHEDULE]
        WHERE fAGRISN = @dsi
              AND fINC > @lstf
    )


    insert into @graph
    SELECT prin.fDATE,
           prin.fSUM princ,
           isnull(Sint.fSUM, 0) intr,
           isnull(SFval.fSUM, 0) sfvam,
           prin.fSUM + isnull(Sint.fSUM, 0) + isnull(SFval.fSUM, 0) mtot

    FROM [asvark].[dbo].[AGRSCHEDULEVALUES] prin
        CROSS APPLY
    (
        SELECT fDATE
        FROM [asvark].[dbo].[AGRSCHEDULE]
        WHERE fAGRISN = @dsi
              AND fINC = @lstf
              AND fTYPE <> 7
    ) dc
        OUTER APPLY
    (
        SELECT max(fINC) mx
        FROM [asvark].[dbo].[AGRSCHEDULE]
        WHERE fAGRISN = @dsi
              AND datediff(d, fDATE, iif(@checkLastFinc = 0, getdate(), dc.fDATE)) >= 0
              AND fTYPE = 7
    ) sff
        OUTER APPLY
    (
        SELECT sum(fSUM) fSUM
        FROM [asvark].[dbo].[AGRSCHEDULEVALUES]
        WHERE fAGRISN = @dsi
              AND fVALUETYPE = 7
              AND fDATE = prin.fDATE
              AND fINC = sff.mx
    ) SFval
        OUTER APPLY
    (
        SELECT sum(fSUM)
AS          fSUM
        FROM [asvark].[dbo].[AGRSCHEDULEVALUES]
        WHERE fAGRISN = @dsi
              AND fVALUETYPE = 2
              AND fDATE = prin.fDATE
              AND fINC = @lstf
    ) Sint
    WHERE prin.fAGRISN = @dsi
          AND prin.fVALUETYPE = 1
          AND prin.fINC = @lstf
  
    if @AVsvf < 0
        set @curs =
    (
        select sfvan
        from @graph
        where sfvan > 0
              and fdate =
              (
                  select min(fdate) from @graph where sfvan > 0
              )
    )
    Begin

        while @AVsvf < 0
        begin

         
            if (@AVsvf + @curs) < 0
            begin
                update @graph
                set sfvan = 0
                where sfvan > 0
                      and fdate =
                      (
                          select min(fdate) from @graph where sfvan > 0
                      )
            end
            else
            begin
                update @graph
                set sfvan = @AVsvf + @curs
                where sfvan > 0
                      and fdate =
                      (
                          select min(fdate) from @graph where sfvan > 0
                      )
            end
            set @AVsvf = @AVsvf + @curs
            set @curs =
            (
                select sfvan
                from @graph
                where sfvan > 0
                      and fdate =
                      (
                          select min(fdate) from @graph where sfvan > 0
                      )
            )
        end
    end
    if @AVint < 0
        set @curs =
    (
        select Intr
        from @graph
        where Intr > 0
              and fdate =
              (
                  select min(fdate) from @graph where Intr > 0
              )
    )
    Begin

        while @AVint < 0
        begin

            --select @curs,@AVsvf+@curs,@AVsvf
            if (@AVint + @curs) < 0
            begin
                update @graph
                set Intr = 0
                where Intr > 0
                      and fdate =
                      (
                          select min(fdate) from @graph where Intr > 0
                      )
            end
            else
            begin
                update @graph
                set Intr = @AVsvf + @curs
                where Intr > 0
                      and fdate =
                      (
                          select min(fdate) from @graph where Intr > 0
                      )
            end
            set @AVint = @AVint + @curs
            set @curs =
            (
                select Intr
                from @graph
                where Intr > 0
                      and fdate =
                      (
                          select min(fdate) from @graph where Intr > 0
                      )
            )
        end
    end


    update @graph
    set mtot = princ + Intr + sfvan

    
    return
end