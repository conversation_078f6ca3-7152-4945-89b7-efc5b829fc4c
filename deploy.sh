#!/bin/bash

cd globalcredit

git fetch
git checkout origin/$ORIGIN_BRANCH

cd server

cp .env.staging .env

cd ../docker

docker-compose run workspace sh -c '
  echo "Install packages"
  composer install

  echo "Fresh migrate"
  php artisan migrate:fresh --seed

  echo "Run tests"
  ./vendor/bin/phpunit --stop-on-failure

  if [ $? != 0 ]; then
    exit 1
  fi

  echo "Restart jobs"
  PID=`ps -eaf | grep "php artisan queue:work" | grep -v grep | awk "{print $2}"`
  echo "PID: $PID"
  if [ "" !=  "$PID" ]; then
    echo "killing $PID"
    kill -9 $PID
  fi
  php artisan queue:work database --daemon --tries=3 --timeout=0 &
'

# docker failure
if [ $? != 0 ]; then
  exit 1
fi

cd ../web
yarn install
yarn test --watchAll=false
yarn build