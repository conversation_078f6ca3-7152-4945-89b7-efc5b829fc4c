<?php

namespace Globalcredit\ActionButton\Providers;

use Laravel\Nova\Nova;
use Laravel\Nova\Events\ServingNova;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;

class FieldServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Nova::serving(function (ServingNova $event) {
            Nova::script('action-button', __DIR__.'/../../dist/js/field.js');
            Nova::style('action-button', __DIR__.'/../../dist/css/field.css');
        });
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        Route::middleware(['nova'])
             ->prefix('nova-vendor/action-button')
             ->group(__DIR__ . '/../../routes/api.php');
    }
}
