<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.11.0.final using JasperReports Library version 6.11.0-0c4056ccaa4d25a5a8c45672d2f764ea3498bebb  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="idram_daily_report" pageWidth="1795" pageHeight="1842" columnWidth="1755" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="a0414ea1-ffde-4c4b-b813-e5e594c1951b">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="Sample DB"/>
	<property name="ireport.jasperserver.url" value="http://192.168.11.229:8080/jasperserver/"/>
	<property name="ireport.jasperserver.user" value="jasperadmin"/>
	<property name="ireport.jasperserver.report.resource" value="/Reports/Report/daily_report_files/idram_daily_report.jrxml"/>
	<property name="ireport.jasperserver.reportUnit" value="/Reports/Report/daily_report"/>
	<property name="net.sf.jasperreports.export.xls.break.before.row" value="true"/>
	<property name="net.sf.jasperreports.export.xls.break.after.row" value="true"/>
	<property name="net.sf.jasperreports.export.xls.one.page.per.sheet" value="true"/>
	<property name="net.sf.jasperreports.export.xls.ignore.graphics" value="false"/>
	<property name="net.sf.jasperreports.page.break.no.pagination" value="apply"/>
	<propertyExpression name="net.sf.jasperreports.export.xls.sheet.names.sheet1"><![CDATA["Easy Pay"]]></propertyExpression>
	<propertyExpression name="net.sf.jasperreports.export.xls.sheet.names.sheet2"><![CDATA["Easy Pay ըստ գումարի"]]></propertyExpression>
	<propertyExpression name="net.sf.jasperreports.export.xls.sheet.names.sheet3"><![CDATA["Easy Pay ըստ քանակի"]]></propertyExpression>
	<queryString language="SQL">
		<![CDATA[SELECT date_trunc('day', lm.created_at) as day,
count(oepl_all_loans.id) AS oepl_all, sum(oepl_all_loans.amount) as oepl_all_amount,
count(oepl_loans.id) AS oepl_confirmed, sum(oepl_loans.amount) as oepl_confirmed_amount,
count(oepl_pending_loans.id) AS oepl_pending, sum(oepl_pending_loans.amount) as oepl_pending_amount,
count(oepl_failed_loans.id) AS oepl_failed, sum(oepl_failed_loans.amount) as oepl_failed_amount
FROM loan_securities as lm
left outer join loans as oepl_all_loans on oepl_all_loans.id=lm.loan_id and oepl_all_loans.loan_type_id = 10
left outer join loans as oepl_loans on oepl_loans.id=lm.loan_id and oepl_loans.loan_type_id = 10 and oepl_loans.status = 'CONFIRMED'
left outer join loans as oepl_pending_loans on oepl_pending_loans.id=lm.loan_id and oepl_pending_loans.loan_type_id = 10 and oepl_pending_loans.status = 'PENDING'
left outer join loans as oepl_failed_loans on oepl_failed_loans.id=lm.loan_id and oepl_failed_loans.loan_type_id = 10 and oepl_failed_loans.payment_status = 'PAYMENT_FAILED'
WHERE lm.created_at > (current_date - interval '30' day)
AND lm.created_at < (current_date - interval '1' day + time '23:59')
GROUP BY 1
ORDER BY 1;]]>
	</queryString>
	<field name="day" class="java.sql.Date"/>
	<field name="oepl_confirmed_amount" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="pending"/>
	</field>
	<field name="oepl_confirmed" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="pending"/>
	</field>
	<field name="oepl_pending_amount" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="pending"/>
	</field>
	<field name="oepl_pending" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="pending"/>
	</field>
	<field name="oepl_failed_amount" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="pending"/>
	</field>
	<field name="oepl_failed" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="pending"/>
	</field>
	<field name="oepl_all_amount" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="pending"/>
	</field>
	<field name="oepl_all" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="pending"/>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="55" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<textField>
				<reportElement x="0" y="2" width="100" height="30" uuid="d957104a-7eb5-4e5e-a0dd-ef197f50ccc7"/>
				<textElement textAlignment="Center">
					<font size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Easy Pay"]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="61">
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.spreadsheet.SpreadsheetLayout"/>
			<staticText>
				<reportElement x="0" y="0" width="100" height="61" uuid="d5dfd4c3-6261-43d4-95a4-3b3b76c8be02">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="499a5baa-d885-489b-967c-db35bacb9f5f"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Ամսաթիվ]]></text>
			</staticText>
			<staticText>
				<reportElement x="100" y="0" width="100" height="61" uuid="0dcf7b40-f82e-4ae9-9842-c78de2001ec8">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="738cf615-2a2a-4f8e-bbda-912c1a157b69"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Բոլորը]]></text>
			</staticText>
			<staticText>
				<reportElement x="200" y="0" width="100" height="61" uuid="f724cda9-62ec-40fd-9aba-15b4f8c8f179">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="9df6d05a-b2c5-4c68-a9e7-90708453f7f1"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Գումար]]></text>
			</staticText>
			<staticText>
				<reportElement x="300" y="0" width="100" height="61" uuid="553e02a6-04b8-47fd-8c6a-da7a5ee8a9ee">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="3cc75e23-39ca-47d0-b45c-bcb86aeac213"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Հրաժարվել են]]></text>
			</staticText>
			<staticText>
				<reportElement x="400" y="0" width="100" height="61" uuid="edd265f8-0947-4843-aaa9-036b2f54a08c">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="b9a1c8b8-e146-4ec4-b67d-7b3e785eee66"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Հրաժարված Գումար]]></text>
			</staticText>
			<staticText>
				<reportElement x="500" y="0" width="100" height="61" uuid="2356df8f-856f-4eee-a620-97fe5b298c1e">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="f514fb40-5e08-43fa-9e92-7def3f89e49a"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Փոխանցման Ձախողում]]></text>
			</staticText>
			<staticText>
				<reportElement x="600" y="0" width="100" height="61" uuid="48528917-bda5-45c1-a694-d32737ba6220">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="0704b714-d330-4e83-b522-a7a34d5cb1e2"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Փոխանցման Ձախման գումար ]]></text>
			</staticText>
			<staticText>
				<reportElement x="700" y="0" width="100" height="61" uuid="1024f6f9-b6ca-41a9-b79f-8f6a4af61b7f">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="12dc4988-12fd-4bf2-bc66-3c5fa4c2be0f"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Հաստատված]]></text>
			</staticText>
			<staticText>
				<reportElement x="800" y="0" width="100" height="61" uuid="9a6874b2-455b-4502-a92b-3c1511ee1c8c">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="045f9f00-62c0-4c86-9a52-bcc2f8ef93d5"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Հաստատված Գումար]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="64">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.spreadsheet.SpreadsheetLayout"/>
			<textField isBlankWhenNull="true">
				<reportElement isPrintRepeatedValues="false" x="0" y="0" width="100" height="64" uuid="185c2082-92c7-4406-820d-419f367f71a0">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="499a5baa-d885-489b-967c-db35bacb9f5f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center"/>
				<textFieldExpression><![CDATA[$F{day}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="100" y="0" width="100" height="64" uuid="6106f195-4e85-4394-8748-3b53710c18c4">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="738cf615-2a2a-4f8e-bbda-912c1a157b69"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center"/>
				<textFieldExpression><![CDATA[$F{oepl_all}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="200" y="0" width="100" height="64" uuid="93778d9b-389f-433b-8eb0-eed50d606e4f">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="9df6d05a-b2c5-4c68-a9e7-90708453f7f1"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center"/>
				<textFieldExpression><![CDATA[$F{oepl_all_amount}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement x="300" y="0" width="100" height="64" uuid="77aead36-cacf-40c1-956c-7cf3ae0b8a35">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="3cc75e23-39ca-47d0-b45c-bcb86aeac213"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center"/>
				<textFieldExpression><![CDATA[$F{oepl_pending}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="400" y="0" width="100" height="64" uuid="25cd1a70-7cb4-478f-9951-8be9e419c4d0">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="b9a1c8b8-e146-4ec4-b67d-7b3e785eee66"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center"/>
				<textFieldExpression><![CDATA[$F{oepl_pending_amount}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement mode="Transparent" x="500" y="0" width="100" height="64" uuid="305e4cc4-2d6d-4f67-99e5-7b353e98c987">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="f514fb40-5e08-43fa-9e92-7def3f89e49a"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center"/>
				<textFieldExpression><![CDATA[$F{oepl_failed}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement mode="Transparent" x="600" y="0" width="100" height="64" uuid="8f9d3cc4-5575-4bc4-aa77-744b4a0e7149">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="0704b714-d330-4e83-b522-a7a34d5cb1e2"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center"/>
				<textFieldExpression><![CDATA[$F{oepl_failed_amount}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="700" y="0" width="100" height="64" uuid="0f6c3860-1508-4f48-8b1a-724fcf193785">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="12dc4988-12fd-4bf2-bc66-3c5fa4c2be0f"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center"/>
				<textFieldExpression><![CDATA[$F{oepl_confirmed}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="800" y="0" width="100" height="64" uuid="f83a9378-aa3e-4bd9-8bd5-54d912a9bf87">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="045f9f00-62c0-4c86-9a52-bcc2f8ef93d5"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center"/>
				<textFieldExpression><![CDATA[$F{oepl_confirmed_amount}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="1592" splitType="Prevent">
			<break>
				<reportElement x="0" y="10" width="919" height="1" uuid="4ead6bc9-03b9-4f39-a38c-0a30a791ef77"/>
			</break>
			<lineChart>
				<chart evaluationTime="Report">
					<reportElement x="12" y="30" width="1168" height="400" uuid="725191fd-4d8a-4534-80bb-6d219adca7a2">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<chartTitle>
						<titleExpression><![CDATA["Ըստ Գումարի"]]></titleExpression>
					</chartTitle>
					<chartSubtitle/>
					<chartLegend/>
				</chart>
				<categoryDataset>
					<dataset resetType="Report"/>
					<categorySeries>
						<seriesExpression><![CDATA["Հաստատված"]]></seriesExpression>
						<categoryExpression><![CDATA[$F{day}]]></categoryExpression>
						<valueExpression><![CDATA[$F{oepl_confirmed_amount}]]></valueExpression>
					</categorySeries>
					<categorySeries>
						<seriesExpression><![CDATA["Հրաժարվել են"]]></seriesExpression>
						<categoryExpression><![CDATA[$F{day}]]></categoryExpression>
						<valueExpression><![CDATA[$F{oepl_pending_amount}]]></valueExpression>
					</categorySeries>
					<categorySeries>
						<seriesExpression><![CDATA["Ձախողված"]]></seriesExpression>
						<categoryExpression><![CDATA[$F{day}]]></categoryExpression>
						<valueExpression><![CDATA[$F{oepl_failed_amount}]]></valueExpression>
					</categorySeries>
				</categoryDataset>
				<linePlot>
					<plot labelRotation="341.0"/>
					<categoryAxisFormat labelRotation="341.0">
						<axisFormat labelColor="#000000" tickLabelColor="#000000" axisLineColor="#000000"/>
					</categoryAxisFormat>
					<valueAxisFormat>
						<axisFormat labelColor="#000000" tickLabelColor="#000000" axisLineColor="#000000"/>
					</valueAxisFormat>
				</linePlot>
			</lineChart>
			<lineChart>
				<chart evaluationTime="Report">
					<reportElement positionType="Float" x="10" y="450" width="1270" height="400" uuid="87a6c881-227c-4294-893f-e951a376ae34">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<chartTitle>
						<titleExpression><![CDATA["Ըստ Քանակի"]]></titleExpression>
					</chartTitle>
					<chartSubtitle/>
					<chartLegend/>
				</chart>
				<categoryDataset>
					<dataset resetType="Report"/>
					<categorySeries>
						<seriesExpression><![CDATA["Հաստատված"]]></seriesExpression>
						<categoryExpression><![CDATA[$F{day}]]></categoryExpression>
						<valueExpression><![CDATA[$F{oepl_confirmed}]]></valueExpression>
					</categorySeries>
					<categorySeries>
						<seriesExpression><![CDATA["Բոլոորը"]]></seriesExpression>
						<categoryExpression><![CDATA[$F{day}]]></categoryExpression>
						<valueExpression><![CDATA[$F{oepl_all}]]></valueExpression>
					</categorySeries>
					<categorySeries>
						<seriesExpression><![CDATA["Հրաժարվել են"]]></seriesExpression>
						<categoryExpression><![CDATA[$F{day}]]></categoryExpression>
						<valueExpression><![CDATA[$F{oepl_pending}]]></valueExpression>
					</categorySeries>
					<categorySeries>
						<seriesExpression><![CDATA["Ձախողված"]]></seriesExpression>
						<categoryExpression><![CDATA[$F{day}]]></categoryExpression>
						<valueExpression><![CDATA[$F{oepl_failed}]]></valueExpression>
					</categorySeries>
				</categoryDataset>
				<linePlot>
					<plot labelRotation="341.0"/>
					<categoryAxisFormat labelRotation="341.0">
						<axisFormat labelColor="#000000" tickLabelColor="#000000" axisLineColor="#000000"/>
					</categoryAxisFormat>
					<valueAxisFormat>
						<axisFormat labelColor="#000000" tickLabelColor="#000000" axisLineColor="#000000"/>
					</valueAxisFormat>
				</linePlot>
			</lineChart>
			<break>
				<reportElement x="0" y="440" width="920" height="1" uuid="4ac1732a-e2e7-46b3-a266-4ab26c2731c5"/>
			</break>
		</band>
	</summary>
</jasperReport>
