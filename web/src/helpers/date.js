import _ from 'lodash';
import moment from 'moment';

import {
  ARM_TIMEZONE,
  MONTHS_COUNT_IN_YEAR,
  STANDARD_DATETIME_FORMAT,
} from '../constants';

export const composeMonths = (startMonth = 0) => {
  const iteration = MONTHS_COUNT_IN_YEAR - startMonth;

  return _.times(iteration, index => {
    const month = _.padStart(index + startMonth + 1, 2, '0');

    return {
      id: index,
      text: month,
      value: month,
    };
  });
};

export const composeYears = (serverTime = null) => {
  return _.times(21, index => {
    const year = serverTime
      ? moment(serverTime).year() + index
      : moment().year() + index;

    return {
      id: index,
      text: year,
      value: year,
    };
  });
};

export const setArmTimezone = (date, format = '') => {
  if (!format) {
    format = STANDARD_DATETIME_FORMAT;
  }

  return moment(date)
    .utc(date)
    .utcOffset(ARM_TIMEZONE)
    .format(format);
};

export const composeVehicleReleasedYears = () =>
  Array.from({ length: new Date().getFullYear() - 1993 }, (_, index) => ({
    text: 1994 + index,
    value: 1994 + index,
  }));
