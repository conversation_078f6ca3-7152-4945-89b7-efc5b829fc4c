<template>
  <div class="mortgage-subject-container">
    <div class="row flex flex-wrap">
      <formulate-input
        type="numeric"
        name="evaluatedPrice"
        :label="__('Evaluated Market Price')"
        :placeholder="__('Evaluated Market Price')"
        v-model="mortgageSubject.evaluatedPrice"
        validation="required"
        :validation-name="__('Evaluated Market Price')"
        :disabled="isFieldDisabled"
        :currency="AMD_CURRENCY_SYMBOL"
        :precision="PRECISION"
      />

      <formulate-input
        type="date"
        name="evaluatedDate"
        :label="__('Choose date')"
        :help="__('Evaluated Date Helper')"
        validation="required"
        :validation-name="__('Evaluated Date Helper')"
        v-model="mortgageSubject.evaluatedDate"
        :disabled="isFieldDisabled"
      />

      <formulate-input
        type="text"
        name="subjectType"
        :label="__('Mortgage Subject')"
        :placeholder="__('Mortgage Subject')"
        :help="__('Mortgage Subject Helper')"
        v-model="mortgageSubject.subjectType"
        :disabled="isFieldDisabled"
      />

      <formulate-input
        type="text"
        name="subjectAddress"
        :label="__('Mortgage Subject Address')"
        :placeholder="__('Mortgage Subject Address')"
        :help="__('Mortgage Subject Address Helper')"
        v-model="mortgageSubject.subjectAddress"
      />

      <formulate-input
        type="textarea"
        name="subjectDescription"
        :label="__('Description')"
        :placeholder="__('Description')"
        :help="__('Mortgage Subject Description')"
        v-model="mortgageSubject.subjectDescription"
        :disabled="isFieldDisabled"
      />

      <formulate-input
        type="date"
        name="ownershipCertGivenDate"
        :label="__('Choose date')"
        :help="__('Ownership Cert Given Date')"
        v-model="mortgageSubject.ownershipCertGivenDate"
        :disabled="isFieldDisabled"
      />

      <formulate-input
        type="text"
        name="ownershipCertNumber"
        :label="__('Certificate Number')"
        :placeholder="__('Certificate Number')"
        :help="__('Ownership Cert Number')"
        v-model="mortgageSubject.ownershipCertNumber"
        :disabled="isFieldDisabled"
      />

      <formulate-input
        type="text"
        name="ownershipCertPassword"
        :label="__('Certificate Password')"
        :placeholder="__('Certificate Password')"
        :help="__('Ownership Cert Password')"
        v-model="mortgageSubject.ownershipCertPassword"
        :disabled="isFieldDisabled"
      />

      <formulate-input
        type="date"
        name="unifiedCadastralRefGivenDate"
        :label="__('Choose date')"
        :help="__('Unified Cadastral Ref Given Date')"
        v-model="mortgageSubject.unifiedCadastralRefGivenDate"
      />

      <formulate-input
        type="text"
        name="unifiedCadastralRefNumber"
        :label="__('Unified Cadastral Ref Number')"
        :placeholder="__('Unified Cadastral Ref Number')"
        :help="__('Unified Cadastral Ref Number')"
        v-model="mortgageSubject.unifiedCadastralRefNumber"
        v-mask="unifiedCadastralRefNumberMask"
      />

      <formulate-input
        type="text"
        name="unifiedCadastralRefPassword"
        :label="__('Unified Cadastral Ref Password')"
        :placeholder="__('Unified Cadastral Ref Password')"
        :help="__('Unified Cadastral Ref Password Helper')"
        v-model="mortgageSubject.unifiedCadastralRefPassword"
      />

      <formulate-input
        type="text"
        name="realEstateAddress"
        :label="__('Real Estate Address')"
        :placeholder="__('Real Estate Address')"
        :help="__('Filled By Citizen')"
        :value="realEstateAddress"
        :disabled="true"
      />
    </div>
  </div>
</template>

<script>
import { unifiedCadastralRefNumberMask } from '../helpers/masks';

const PRECISION = 2;
const AMD_CURRENCY_SYMBOL = '֏';

export default {
  props: ['mortgageSubject', 'realEstateAddress', 'isFieldDisabled'],

  data() {
    return {
      unifiedCadastralRefNumberMask,
      PRECISION,
      AMD_CURRENCY_SYMBOL,
    };
  },
};
</script>
