package reports.service.acra;

import org.apache.commons.collections4.bidimap.DualHashBidiMap;
import org.json.JSONObject;

import java.util.*;

public class AcraData {
    protected List<JSONObject> loans = new LinkedList<JSONObject>();
    protected List<JSONObject> guarantees = new LinkedList<JSONObject>();
    protected int requestQuantity = 0;

    public static final String ACRA_ACTIVE_LOAN = "գործող";
    public static final String ACRA_REPAID_LOAN = "մարված";

    public static final int DEFAULT_INTEREST_RATE = 24;

    public static DualHashBidiMap<String, Integer> loanClasses;

    static {
        loanClasses = new DualHashBidiMap<>();
        loanClasses.put("", 0);
        loanClasses.put("Ստանդարտ", 1);
        loanClasses.put("Հսկվող", 2);
        loanClasses.put("Ոչ ստանդարտ", 3);
        loanClasses.put("Կասկածելի", 4);
        loanClasses.put("Անհուսալի", 5);
        loanClasses.put("Չհաշվառվող", 6);
    }

    public static List<String> creditScopes = Arrays.asList(
            "կացարանային վարկեր",
            "հիփոթեքային վարկեր",
            "բնակարանների ձեռքբերման վարկեր",
            "բնակարանների կառուցման վարկեր",
            "բնակարանների վերանորոգման վարկեր",
            "կացարանային",
            "հիպոթեկային վարկեր",
            "հիպոթեկային վարկեր (հմձյն. կանոն 2)",
            "անշարժ գույքի ձեռքբերման հիպոթեկային վարկեր",
            "անշարժ գույքի վերանորոգման հիպոթեկային վարկեր",
            "անշարժ գույքի շինարարության հիպոթեկային վարկեր",
            "անշարժ գույքի կառուցապատման հիպոթեկային վարկեր"
    );

    public static List<String> RealEstatePledgeSubjects = Arrays.asList(
            "անշարժ գույք",
            "բնակարան",
            "օֆիսային տարածք"
    );

    public static List<String> nonPledgeSubjects = Arrays.asList(
            "անհայտ",
            "ապրանքներ",
            "երաշխավորություններ",
            "շրջանառու միջոցներ",
            "աշխատավարձ",
            "ոչ ստանդարտ ապահովում",
            "ֆինանսական հոսք",
            "ֆինանսական հոսք և բարի համբավ",
            "այլ",
            "սարքավորումներ",
            "պատրաստի արտադրանք",
            "բլանկային",
            "վարկային երաշխավորություններ",
            "այլ երաշխավորություններ",
            "պատրաստի արտադրանք և ապրանքներ",
            "երաշխավորության պայմանագիր",
            "կենցաղային տեխնիկա",
            "կենցաղային այլ ապրանքներ",
            "գրավ",
            "գրավադրված ապրանք ապառիկի գծով",
            "ապրանքներ մմկ",
            "այլ գրավ",
            "ապրանքներ ակցիային/բկ",
            "ապրանքներ բջջային",
            "հաշվի շրջանառություն կրեդիտ քարտեր",
            "բլանկային կրեդիտ",
            "վարկային երաշխավորություն համատեղ"
    );

    public void setGuarantees(JSONObject loans) {
        this.guarantees.add(loans);
    }

    public List<JSONObject> getGuarantees() {
        return this.guarantees;
    }

    public void setLoans(JSONObject loans) {
        this.loans.add(loans);
    }

    public List<JSONObject> getLoans() {
        return this.loans;
    }

    public int getLoansCount() {
        return this.loans.size();
    }

    public void setRequestQuantity(int requestQuantity) {
        this.requestQuantity = requestQuantity;
    }

    public int getRequestQuantity() {
        return requestQuantity;
    }
}
