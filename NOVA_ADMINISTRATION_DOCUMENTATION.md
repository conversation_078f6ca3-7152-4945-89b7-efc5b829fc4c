# Global Credit Nova Administration Documentation

## Overview
This document provides comprehensive documentation for all existing Nova administration features implemented in the Global Credit system. Nova serves as the primary administrative interface for managing loans, users, transactions, and various business operations.

## Table of Contents
1. [Core Nova Resources](#core-nova-resources)
2. [Custom Nova Tools](#custom-nova-tools)
3. [Dashboard and Metrics](#dashboard-and-metrics)
4. [Actions and Operations](#actions-and-operations)
5. [Filters and Search](#filters-and-search)
6. [Permissions and Authorization](#permissions-and-authorization)
7. [Custom Components](#custom-components)

## Core Nova Resources

### 1. Loan Management Resources

#### Loan Resource (`App\Nova\Loan`)
**Purpose**: Main loan management interface
**Model**: `App\Models\Loan`
**Navigation Permission**: `view-full-history` or `view-loans`

**Key Fields**:
- Contract Number (searchable)
- Citizen Information (first name, last name, passport)
- Loan Type (OCL, OVL, OASL, OIDL, PL, OIQL, etc.)
- Loan Amount and Currency
- Interest Rate and Terms
- Status and Dates
- Top-up Information
- Document Delivery Method (email, in-person, post)

**Relationships**:
- `HasMany`: Loan Documents, Transaction Documents
- `BelongsTo`: Citizen, Loan Type

**Filters**:
- Loan Sign Date Filter
- Loan Type Filter
- Top Up Loan Filter

#### ArpiSolarLoan Resource (`App\Nova\ArpiSolarLoan`)
**Purpose**: Solar panel loan management
**Model**: `App\Models\Loan` (filtered for solar loans)
**Navigation Permission**: `view-oasl-loan`

**Key Fields**:
- Contract Number
- Citizen Information (passport, names, phone)
- Solar Panel Details
- Installation Address
- KFW Information
- Agent Assignment
- Status and Expiration

**Actions**:
- Create Package
- Download Documents
- Download KFW Loans
- Edit Solar Panel Address

**Filters**:
- Loan Sign Date Filter
- OASL Agent Filter
- OASL Status Filter

#### VehicleLoan Resource (`App\Nova\VehicleLoan`)
**Purpose**: Vehicle loan management
**Model**: `App\Models\Loan` (filtered for vehicle loans)
**Navigation Permission**: `view-ovl-loan`

**Key Fields**:
- Contract Number
- Citizen Information
- Vehicle Details (number, VIN, model, mark)
- Mortgage Information
- Security Details
- Agent Assignment

**Filters**:
- Loan Status Filter
- Referrer Source Filter
- Vehicle Checkup Date Filter

#### MortgageLoan Resource (`App\Nova\MortgageLoan`)
**Purpose**: Real estate mortgage loan management
**Navigation Permission**: `view-reml`

**Key Fields**:
- Standard loan fields
- Evaluation Company Information
- Real Estate Details

#### WithdrawnLoan Resource (`App\Nova\WithdrawnLoan`)
**Purpose**: Completed/withdrawn loans management
**Navigation Permission**: `view-loan-history`

**Filters**:
- Withdraw Date Filter
- Withdraw Company Filter
- Loan Type Filter
- Top Up Loan Filter

#### WithdrawableLoan Resource (`App\Nova\WithdrawableLoan`)
**Purpose**: Loans ready for withdrawal
**Navigation Permission**: `view-cash`

### 2. User Management Resources

#### User Resource (`App\Nova\User`)
**Purpose**: System user management
**Model**: `App\Models\User`
**Navigation Permission**: Admin, Upay Admin, Telcell Admin, or `view-oasl-users`

**Key Fields**:
- ID (sortable)
- Avatar (Gravatar)
- First Name, Last Name
- Email (unique, sortable)
- Password (forms only)
- Active Status
- Phone Number
- Role Assignments

**Relationships**:
- `MorphToMany`: Roles and Permissions

**Query Scoping**:
- Admin: All users
- Upay Admin: Only upay-cashier role users
- Telcell Admin: Only telcell-cashier role users
- OASL Permission: Only oasl-agent role users

#### Role Resource (`App\Nova\Role`)
**Purpose**: Role management for permissions
**Integration**: Nova Permission Tool

#### Permission Resource (`App\Nova\Permission`)
**Purpose**: Permission management
**Integration**: Nova Permission Tool

### 3. Transaction and Payment Resources

#### Transaction Resource (`App\Nova\Transaction`)
**Purpose**: BNPL transaction management
**Model**: `App\Models\CreditLine\Transaction`
**Navigation Permission**: Admin or `view-bnpl-transactions`

**Key Fields**:
- Vendor Type and Name
- Contract Number
- Amount (formatted in Dram)
- Merchant Information
- Transaction Date
- Description, Passport, SSN, Phone (detail only)

**Actions**:
- Export Transactions (with filename and format selection)

#### PurchaseRequest Resource (`App\Nova\PurchaseRequest`)
**Purpose**: Credit line purchase request management
**Model**: `App\Models\CreditLine\PurchaseRequest`
**Navigation Permission**: `view-purchase-request` or `view-all-purchase-requests`

**Key Fields**:
- Vendor Selection
- Amount
- Description
- Status
- QR Code Generation
- Creation Date

**Actions**:
- Send Purchase Request by SMS

**Filters**:
- Purchase Request Date Filter
- Purchase Request Status Filter
- Vendor Filter

### 4. Merchant and QR Code Resources

#### Merchant Resource (`App\Nova\Merchant`)
**Purpose**: Merchant management
**Model**: `App\Models\Merchant`
**Navigation Permission**: `view-merchant` or Admin

**Key Fields**:
- Vendor Relationship
- Merchant Name and Title
- Address
- Tax Information (Armsoft Mi Tax Payer ID, Account Number)
- Contact Information (phone numbers)
- Type (Hospital, Vehicle Import)

**Relationships**:
- `BelongsTo`: Vendor
- `BelongsToMany`: Agents

#### QrCode Resource (`App\Nova\QrCode`)
**Purpose**: QR code management for payments
**Model**: `App\Models\QrCode`
**Navigation Permission**: `view-qr-codes`

**Key Fields**:
- QR Type (Discount, Owner-based)
- Loan Type Association
- Discount Percentage (conditional)
- QR Code Image and Link
- Owner Information (conditional)

**Relationships**:
- `HasOne`: QR Owner

### 5. Document Management Resources

#### LoanDocument Resource (`App\Nova\LoanDocument`)
**Purpose**: Loan document management
**Model**: `App\Models\LoanDocument`

#### TransactionDocument Resource (`App\Nova\TransactionDocument`)
**Purpose**: Transaction document management
**Model**: `App\Models\TransactionDocument`

#### LoanDocumentHistory Resource (`App\Nova\LoanDocumentHistory`)
**Purpose**: Document history tracking
**Model**: `App\Models\LoanDocumentHistory`

### 6. Specialized Loan Resources

#### COVID19 Support Resources
- **SupportLoan** (`App\Nova\COVID19\SupportLoan`): COVID-19 support loan management
- **SupportLoanDocument** (`App\Nova\COVID19\SupportLoanDocument`): Related document management
- **SupportBlacklist** (`App\Nova\COVID19\SupportBlacklist`): COVID-19 specific blacklist

#### Stabilization Resources
- **StabilizationLoan** (`App\Nova\Stabilization\StabilizationLoan`): Economic stabilization loans

#### Vehicle-Specific Resources
- **VehicleImportLoan** (`App\Nova\VehicleImportLoan`): Vehicle import financing
- **VehicleClosedLoan** (`App\Nova\VehicleClosedLoan`): Completed vehicle loans
- **VehicleModel** (`App\Nova\VehicleModel`): Vehicle model management
- **VehiclePrice** (`App\Nova\VehiclePrice`): Vehicle pricing information

### 7. Business Logic Resources

#### Blacklist Resource (`App\Nova\Blacklist`)
**Purpose**: Customer blacklist management
**Model**: `App\Models\Blacklist`

#### BlacklistReason Resource (`App\Nova\BlacklistReason`)
**Purpose**: Blacklist reason categorization
**Model**: `App\Models\BlacklistReason`

#### CreditLine Resource (`App\Nova\CreditLine`)
**Purpose**: Credit line management
**Model**: `App\Models\CreditLine`

#### CreditOffer Resource (`App\Nova\CreditOffer`)
**Purpose**: Credit offer management
**Model**: `App\Models\CreditOffer`

#### ReferralCode Resource (`App\Nova\ReferralCode`)
**Purpose**: Referral code system management
**Model**: `App\Models\ReferralCode`

#### ReferralCodeRepayment Resource (`App\Nova\ReferralCodeRepayment`)
**Purpose**: Referral-based repayment tracking
**Model**: `App\Models\ReferralCodeRepayment`

### 8. Configuration Resources

#### Rule Resource (`App\Nova\Rule`)
**Purpose**: Business rule management
**Model**: `App\Models\Rule`

#### RuleGroup Resource (`App\Nova\RuleGroup`)
**Purpose**: Rule grouping and organization
**Model**: `App\Models\RuleGroup`

#### Package Resource (`App\Nova\Package`)
**Purpose**: Document package management
**Model**: `App\Models\Package`

#### SkylineConfig Resource (`App\Nova\SkylineConfig`)
**Purpose**: System configuration management
**Model**: `App\Models\SkylineConfig`

### 9. Support and Scheduling Resources

#### Support Schedule Resources
- **SupportWorkSchedule** (`App\Nova\SupportWorkSchedule`): Work schedule management
- **SupportWorkingHour** (`App\Nova\SupportWorkingHour`): Working hours configuration
- **SupportHoliday** (`App\Nova\SupportHoliday`): Holiday management
- **SupportScheduleException** (`App\Nova\SupportScheduleException`): Schedule exceptions

#### Profile Resource (`App\Nova\Profile`)
**Purpose**: User profile management
**Model**: `App\Models\Pallaton\Profile`

### 10. Media and Marketing Resources

#### MarketingMedia Resource (`App\Nova\MarketingMedia`)
**Purpose**: Marketing material management
**Model**: `App\Models\MarketingMedia`

#### StoryMedia Resource (`App\Nova\StoryMedia`)
**Purpose**: Story content management
**Model**: `App\Models\StoryMedia`

### 11. Additional Business Resources

#### Vendor Resource (`App\Nova\Vendor`)
**Purpose**: Vendor/supplier management
**Model**: `App\Models\Vendor`
**Navigation Permission**: Based on business requirements

#### CitizenLock Resource (`App\Nova\CitizenLock`)
**Purpose**: Citizen account locking management
**Model**: `App\Models\CitizenLock`
**Navigation Permission**: Admin access

#### LoanExplorer Resource (`App\Nova\LoanExplorer`)
**Purpose**: Advanced loan exploration and analysis
**Model**: `App\Models\Loan`
**Navigation Permission**: Advanced user permissions

#### LoanHistory Resource (`App\Nova\LoanHistory`)
**Purpose**: Comprehensive loan history tracking
**Model**: `App\Models\LoanHistory`
**Navigation Permission**: History viewing permissions

#### LoanUpdateHistory Resource (`App\Nova\LoanUpdateHistory`)
**Purpose**: Track all loan modifications and updates
**Model**: `App\Models\LoanUpdateHistory`
**Navigation Permission**: Admin or audit permissions

#### QrLoan Resource (`App\Nova\QrLoan`)
**Purpose**: QR code-based loan management
**Model**: `App\Models\Loan` (filtered for QR loans)
**Navigation Permission**: QR loan access permissions

#### QrOwner Resource (`App\Nova\QrOwner`)
**Purpose**: QR code ownership management
**Model**: `App\Models\QrOwner`
**Navigation Permission**: QR management permissions

#### TelcellLoan Resource (`App\Nova\TelcellLoan`)
**Purpose**: Telcell-specific loan management
**Model**: `App\Models\Loan` (filtered for Telcell loans)
**Navigation Permission**: Telcell operations permissions

### 12. Real Estate and Mortgage Resources

#### RealEstateSeller Resource (`App\Nova\RealEstateSeller`)
**Purpose**: Real estate seller information management
**Model**: `App\Models\RealEstateSeller`
**Navigation Permission**: Real estate operations

#### MortgageLoanApplication Resource (`App\Nova\MortgageLoanApplication`)
**Purpose**: Mortgage loan application processing
**Model**: `App\Models\MortgageLoanApplication`
**Navigation Permission**: `view-reml-loan-applications`

### 13. Merchant and Agent Resources

#### MerchantAgent Resource (`App\Nova\MerchantAgent`)
**Purpose**: Merchant-agent relationship management
**Model**: `App\Models\MerchantAgent`
**Navigation Permission**: Merchant management permissions

#### MerchantBlacklist Resource (`App\Nova\MerchantBlacklist`)
**Purpose**: Merchant blacklist management
**Model**: `App\Models\MerchantBlacklist`
**Navigation Permission**: Merchant administration

### 14. System Configuration Resources

#### AffectedRule Resource (`App\Nova\AffectedRule`)
**Purpose**: Rule impact tracking and management
**Model**: `App\Models\AffectedRule`
**Navigation Permission**: Rule management permissions

#### PipeType Resource (`App\Nova\PipeType`)
**Purpose**: Pipeline type configuration
**Model**: `App\Models\PipeType`
**Navigation Permission**: System configuration

#### VeloxProductCategory Resource (`App\Nova\VeloxProductCategory`)
**Purpose**: Velox product categorization
**Model**: `App\Models\VeloxProductCategory`
**Navigation Permission**: Product management

### 15. Citizen and Customer Resources

#### Citizen Resource (`App\Nova\Citizen`)
**Purpose**: Customer/citizen information management
**Model**: `App\Models\Citizen`
**Navigation Permission**: Customer data access permissions

**Key Fields**:
- Personal Information (Name, Surname, Passport)
- Contact Details (Phone, Email, Address)
- Identification Numbers (SSN, Tax ID)
- Demographic Information
- Document Status

#### CitizenDocument Resource (`App\Nova\CitizenDocument`)
**Purpose**: Customer document management
**Model**: `App\Models\CitizenDocument`
**Navigation Permission**: Document access permissions

#### CitizenPhone Resource (`App\Nova\CitizenPhone`)
**Purpose**: Customer phone number management
**Model**: `App\Models\CitizenPhone`
**Navigation Permission**: Contact management permissions

### 16. Vehicle and Transportation Resources

#### Vehicle Resource (`App\Nova\Vehicle`)
**Purpose**: Vehicle information management
**Model**: `App\Models\Vehicle`
**Navigation Permission**: Vehicle operations permissions

**Key Fields**:
- Vehicle Identification (VIN, License Plate)
- Make, Model, Year
- Technical Specifications
- Insurance Information
- Registration Details

#### VehicleBrand Resource (`App\Nova\VehicleBrand`)
**Purpose**: Vehicle brand management
**Model**: `App\Models\VehicleBrand`
**Navigation Permission**: Vehicle configuration

#### VehicleType Resource (`App\Nova\VehicleType`)
**Purpose**: Vehicle type categorization
**Model**: `App\Models\VehicleType`
**Navigation Permission**: Vehicle configuration

#### VehicleImportCompany Resource (`App\Nova\VehicleImportCompany`)
**Purpose**: Vehicle import company management
**Model**: `App\Models\VehicleImportCompany`
**Navigation Permission**: Import operations

### 17. Evaluation and Assessment Resources

#### EvaluationCompany Resource (`App\Nova\EvaluationCompany`)
**Purpose**: Property evaluation company management
**Model**: `App\Models\EvaluationCompany`
**Navigation Permission**: Evaluation operations

**Key Fields**:
- Company Information
- Contact Details
- Certification Status
- Service Areas
- Evaluation Types

#### EvaluationCompanyUser Resource (`App\Nova\EvaluationCompanyUser`)
**Purpose**: Evaluation company user management
**Model**: `App\Models\EvaluationCompanyUser`
**Navigation Permission**: Evaluation user management

### 18. Loan Type and Category Resources

#### LoanType Resource (`App\Nova\LoanType`)
**Purpose**: Loan product type management
**Model**: `App\Models\LoanType`
**Navigation Permission**: Product configuration

**Key Fields**:
- Loan Type Code (OCL, OVL, OASL, etc.)
- Description and Terms
- Interest Rates
- Eligibility Criteria
- Documentation Requirements

#### LoanCategory Resource (`App\Nova\LoanCategory`)
**Purpose**: Loan categorization management
**Model**: `App\Models\LoanCategory`
**Navigation Permission**: Product configuration

### 19. Company and Organization Resources

#### Company Resource (`App\Nova\Company`)
**Purpose**: Company/organization management
**Model**: `App\Models\Company`
**Navigation Permission**: Company management permissions

**Key Fields**:
- Company Information
- Registration Details
- Contact Information
- Business Type
- Status and Verification

#### CompanyUser Resource (`App\Nova\CompanyUser`)
**Purpose**: Company user relationship management
**Model**: `App\Models\CompanyUser`
**Navigation Permission**: Company user management

### 20. Additional Specialized Resources

#### Agent Resource (`App\Nova\Agent`)
**Purpose**: Agent management and assignment
**Model**: `App\Models\Agent`
**Navigation Permission**: Agent management permissions

#### Bank Resource (`App\Nova\Bank`)
**Purpose**: Banking institution management
**Model**: `App\Models\Bank`
**Navigation Permission**: Banking operations

#### BankBranch Resource (`App\Nova\BankBranch`)
**Purpose**: Bank branch information management
**Model**: `App\Models\BankBranch`
**Navigation Permission**: Banking operations

#### Collateral Resource (`App\Nova\Collateral`)
**Purpose**: Collateral asset management
**Model**: `App\Models\Collateral`
**Navigation Permission**: Collateral operations

#### CollateralType Resource (`App\Nova\CollateralType`)
**Purpose**: Collateral type categorization
**Model**: `App\Models\CollateralType`
**Navigation Permission**: Collateral configuration

#### Currency Resource (`App\Nova\Currency`)
**Purpose**: Currency management and exchange rates
**Model**: `App\Models\Currency`
**Navigation Permission**: Financial configuration

#### District Resource (`App\Nova\District`)
**Purpose**: Geographic district management
**Model**: `App\Models\District`
**Navigation Permission**: Geographic data management

#### EducationLevel Resource (`App\Nova\EducationLevel`)
**Purpose**: Education level categorization
**Model**: `App\Models\EducationLevel`
**Navigation Permission**: Customer data configuration

#### FamilyStatus Resource (`App\Nova\FamilyStatus`)
**Purpose**: Family status categorization
**Model**: `App\Models\FamilyStatus`
**Navigation Permission**: Customer data configuration

#### Gender Resource (`App\Nova\Gender`)
**Purpose**: Gender categorization
**Model**: `App\Models\Gender`
**Navigation Permission**: Customer data configuration

#### IncomeSource Resource (`App\Nova\IncomeSource`)
**Purpose**: Income source categorization
**Model**: `App\Models\IncomeSource`
**Navigation Permission**: Financial assessment

#### JobTitle Resource (`App\Nova\JobTitle`)
**Purpose**: Job title management
**Model**: `App\Models\JobTitle`
**Navigation Permission**: Employment data

#### LoanPurpose Resource (`App\Nova\LoanPurpose`)
**Purpose**: Loan purpose categorization
**Model**: `App\Models\LoanPurpose`
**Navigation Permission**: Loan configuration

#### MaritalStatus Resource (`App\Nova\MaritalStatus`)
**Purpose**: Marital status categorization
**Model**: `App\Models\MaritalStatus`
**Navigation Permission**: Customer data configuration

#### Nationality Resource (`App\Nova\Nationality`)
**Purpose**: Nationality management
**Model**: `App\Models\Nationality`
**Navigation Permission**: Customer data configuration

#### Occupation Resource (`App\Nova\Occupation`)
**Purpose**: Occupation categorization
**Model**: `App\Models\Occupation`
**Navigation Permission**: Employment data

#### Region Resource (`App\Nova\Region`)
**Purpose**: Geographic region management
**Model**: `App\Models\Region`
**Navigation Permission**: Geographic data management

#### Relationship Resource (`App\Nova\Relationship`)
**Purpose**: Relationship type management
**Model**: `App\Models\Relationship`
**Navigation Permission**: Customer relationship data

#### ResidenceType Resource (`App\Nova\ResidenceType`)
**Purpose**: Residence type categorization
**Model**: `App\Models\ResidenceType`
**Navigation Permission**: Customer data configuration

#### SocialStatus Resource (`App\Nova\SocialStatus`)
**Purpose**: Social status categorization
**Model**: `App\Models\SocialStatus`
**Navigation Permission**: Customer data configuration

## Custom Nova Tools

### 1. Permission Management Tool
**Tool**: `Vyuldashev\NovaPermission\NovaPermissionTool`
**Purpose**: Role and permission management
**Access**: Admin role only
**Features**:
- Role creation and management
- Permission assignment
- User role assignment

### 2. Deviated Schedules Tool
**Tool**: `Globalcredit\DeviatedSchedules\DeviatedSchedules`
**Purpose**: Schedule correction management
**Access**: `correct-devated-schedule` permission
**Features**:
- Schedule deviation detection
- Correction workflows

### 3. Bank Report Tool
**Tool**: `Globalcredit\BankReport\BankReport`
**Purpose**: Bank report generation and management
**Access**: `bank-report` permission
**Features**:
- Report generation
- Data export capabilities

### 4. ACRA Monitoring Tool
**Tool**: `Globalcredit\AcraMonitoring\AcraMonitoring`
**Purpose**: ACRA credit bureau monitoring
**Access**: `acra-monitoring` permission
**Features**:
- Credit report monitoring
- Data synchronization

### 5. Settings Tool
**Tool**: `Globalcredit\Settings\Settings`
**Purpose**: System settings management
**Access**: Admin or `gc-customer-admin` role
**Features**:
- System configuration
- Parameter management

### 6. Payment Invoice Tool
**Tool**: `Globalcredit\PaymentInvoice\PaymentInvoice`
**Purpose**: Payment invoice management
**Access**: `view-payment-invoice` permission
**Features**:
- Invoice generation
- Payment tracking
## Dashboard and Metrics

### Dashboard Cards
The Nova dashboard provides quick access to key business areas through custom cards:

#### Home Cards (`Globalcredit\Home\Home`)
- **Unpaid Loans**: Links to withdrawable loans (visible to users with `view-cash` permission)
- **Paid Loans**: Links to withdrawn loans (visible to users with `view-loan-history` permission)
- **All Loans**: Links to all loans (visible to admins and users with `view-loans` permission)
- **Vehicle Loans**: Links to vehicle loans (visible to users with `view-ovl-loan` permission)
- **Vehicle Import Loans**: Links to vehicle import loans (visible to users with `view-ovl-loan` permission)
- **Credit Lines**: Links to credit lines (admin only)
- **Users**: Links to user management (admin, upay admin, telcell admin, or `view-oasl-users` permission)
- **Solar Loans**: Links to solar loans (visible to users with `view-oasl-loan` permission)
- **Stabilization Loans**: Links to stabilization loans (visible to users with `stabilization-loan` permission)
- **Mortgage Loan Applications**: Links to mortgage applications (visible to users with `view-reml-loan-applications` permission)
- **Mortgage Loans**: Links to mortgage loans (visible to users with `view-reml-loan-applications` permission)

#### Metrics Cards
- **LoansPartition** (`App\Nova\Metrics\LoansPartition`): Loan distribution metrics (admin only)

## Actions and Operations

### Loan Actions

#### Document Management Actions
- **DownloadDocuments** (`App\Nova\Actions\DownloadDocuments`): Download loan documents
- **SendDocuments** (`App\Nova\Actions\SendDocuments`): Send documents to customers
- **RegenerateDocuments** (`App\Nova\Actions\RegenerateDocuments`): Regenerate loan documents
- **RegenerateOVILDocuments** (`App\Nova\Actions\RegenerateOVILDocuments`): Regenerate OVIL-specific documents
- **RegenerateWithdrawCheck** (`App\Nova\Actions\RegenerateWithdrawCheck`): Regenerate withdrawal checks

#### Specialized Loan Actions
- **DownloadKFWLoans** (`App\Nova\Actions\DownloadKFWLoans`): Download KFW loan data
- **DownloadQrLoans** (`App\Nova\Actions\DownloadQrLoans`): Download QR-based loan data
- **DownloadLoanApplications** (`App\Nova\Actions\DownloadLoanApplications`): Download loan applications
- **DownloadLoans** (`App\Nova\Actions\DownloadLoans`): Download loan data

#### Loan Management Actions
- **EditEmailAndRegenerateDocuments** (`App\Nova\Actions\EditEmailAndRegenerateDocuments`): Update email and regenerate documents
- **EditSolarPanelAddress** (`App\Nova\Actions\EditSolarPanelAddress`): Update solar panel installation address
- **RegenerateOaslSchedule** (`App\Nova\Actions\RegenerateOaslSchedule`): Regenerate OASL payment schedules
- **ReassignAgent** (`App\Nova\Actions\ReassignAgent`): Reassign loans to different agents

### Package Management Actions
- **CreatePackage** (`App\Nova\Actions\CreatePackage`): Create document packages
- **ChangePackageState** (`App\Nova\Actions\ChangePackageState`): Change package status
- **DownloadPackageDocuments** (`App\Nova\Actions\DownloadPackageDocuments`): Download package documents

### Transaction Actions
- **ExportTransactions** (`App\Nova\Actions\ExportTransactions`): Export transaction data with format selection

### Communication Actions
- **SendPurchaseRequestBySms** (`App\Nova\Actions\SendPurchaseRequestBySms`): Send purchase requests via SMS

### Referral Actions
- **DownloadReferralCodes** (`App\Nova\Actions\DownloadReferralCodes`): Download referral code data

### Stabilization Actions
- **DownloadLoanDocuments** (`App\Nova\Actions\Stabilization\DownloadLoanDocuments`): Download stabilization loan documents

## Filters and Search

### Date Filters
- **LoanSignDateFilter** (`App\Nova\Filters\LoanSignDateFilter`): Filter loans by signing date
- **LoanApplicationDateFilter** (`App\Nova\Filters\LoanApplicationDateFilter`): Filter by application date
- **LoanWithdrawDateFilter** (`App\Nova\Filters\LoanWithdrawDateFilter`): Filter by withdrawal date
- **WithdrawDateFilter** (`App\Nova\Filters\WithdrawDateFilter`): Filter by withdrawal date
- **PurchaseRequestDateFilter** (`App\Nova\Filters\PurchaseRequestDateFilter`): Filter purchase requests by date
- **TransactionPaidDateFilter** (`App\Nova\Filters\TransactionPaidDateFilter`): Filter transactions by payment date
- **VehicleCheckupDateFilter** (`App\Nova\Filters\VehicleCheckupDateFilter`): Filter by vehicle checkup date
- **VehicleImportDateFilter** (`App\Nova\Filters\VehicleImportDateFilter`): Filter by vehicle import date

### Status and Type Filters
- **LoanStatusFilter** (`App\Nova\Filters\LoanStatusFilter`): Filter loans by status
- **LoanTypeFilter** (`App\Nova\Filters\LoanTypeFilter`): Filter by loan type (OCL, OVL, OASL, etc.)
- **OASLStatusFilter** (`App\Nova\Filters\OASLStatusFilter`): Filter OASL loans by status
- **PurchaseRequestStatusFilter** (`App\Nova\Filters\PurchaseRequestStatusFilter`): Filter purchase requests by status
- **OVLTypeFilter** (`App\Nova\Filters\OVLTypeFilter`): Filter OVL loans by subtype

### Business Entity Filters
- **VendorFilter** (`App\Nova\Filters\VendorFilter`): Filter by vendor
- **VendorTypeFilter** (`App\Nova\Filters\VendorTypeFilter`): Filter by vendor type
- **WithdrawCompanyFilter** (`App\Nova\Filters\WithdrawCompanyFilter`): Filter by withdrawal company
- **OASLAgentFilter** (`App\Nova\Filters\OASLAgentFilter`): Filter OASL loans by agent
- **MortgageLoanReferrerSourceFilter** (`App\Nova\Filters\MortgageLoanReferrerSourceFilter`): Filter mortgage loans by referrer
- **ReferrerSourceFilter** (`App\Nova\Filters\ReferrerSourceFilter`): Filter by referrer source

### Special Filters
- **TopUpLoanFilter** (`App\Nova\Filters\TopUpLoanFilter`): Filter top-up loans
- **QRCodeFilter** (`App\Nova\Filters\QRCodeFilter`): Filter QR codes
- **QrTypeFilter** (`App\Nova\Filters\QrTypeFilter`): Filter by QR type
- **TransferTypeFilter** (`App\Nova\Filters\TransferTypeFilter`): Filter by transfer type

### Search Capabilities
Most resources support advanced search including:
- **Contract Number Search**: Available across loan resources
- **Citizen Search**: Search by passport number, names
- **Vehicle Search**: Search by vehicle number, VIN
- **Relationship Search**: Search across related models

## Permissions and Authorization

### Access Control System
Nova uses Laravel's permission system with role-based access control:

#### Admin Roles
- **admin**: Full system access
- **gc-customer-admin**: Customer admin access
- **upay-admin**: Upay system administration
- **telcell-admin**: Telcell system administration

#### Operational Roles
- **upay-cashier**: Upay cashier operations
- **telcell-cashier**: Telcell cashier operations
- **oasl-agent**: Solar loan agent operations

### Key Permissions

#### Loan Management Permissions
- `view-loans`: View loan records
- `view-full-history`: View complete loan history
- `view-loan-history`: View loan history
- `view-cash`: View cash operations
- `view-oasl-loan`: View solar loans
- `view-ovl-loan`: View vehicle loans
- `verify-ovl-loan`: Verify vehicle loans
- `view-reml`: View mortgage loans
- `view-reml-loan-applications`: View mortgage applications
- `stabilization-loan`: Access stabilization loans

#### Transaction Permissions
- `view-bnpl-transactions`: View BNPL transactions
- `view-purchase-request`: View purchase requests
- `view-all-purchase-requests`: View all purchase requests

#### System Permissions
- `view-nova`: Access Nova interface
- `view-merchant`: View merchant data
- `view-qr-codes`: View QR codes
- `view-oasl-users`: View OASL users
- `view-all-details`: View detailed information

#### Tool Permissions
- `correct-devated-schedule`: Access schedule correction tool
- `bank-report`: Access bank report tool
- `acra-monitoring`: Access ACRA monitoring tool
- `view-payment-invoice`: Access payment invoice tool

### Authorization Gates
- **viewNova**: Main gate controlling Nova access
- Resource-level authorization through `availableForNavigation()` methods
- Field-level authorization through `canSee()` callbacks
- Action-level authorization through `canRun()` callbacks
## Custom Components

### Nova Components (nova-components directory)

#### Approval Components
- **ApproveArpiSolarLoan**: Solar loan approval workflow component
- **ApproveVehicleLoan**: Vehicle loan approval workflow component

#### UI Components
- **ActionButton**: Custom action button component
- **CancelButton**: Cancel operation button component
- **CustomLoadingCard**: Custom loading indicator card
- **UploadCard**: File upload card component
- **MediaView**: Media viewing component

#### Form Components
- **EvaluationCompanyForm**: Evaluation company form component
- **SecretKey**: Secret key management component

#### Filter Components
- **NovaDateRangeFilter**: Date range filtering component
- **PerPageFilter**: Items per page filter component
- **ResetAllFilter**: Reset all filters component

#### Monitoring and Reporting
- **LoanExpirationCard**: Loan expiration monitoring card
- **RuleHistory**: Rule execution history component

### Field Types and Customizations

#### Custom Field Types
- **TextCopy**: Copyable text field
- **BooleanTick**: Enhanced boolean display
- **Indicator**: Status indicator field
- **NovaDependencyContainer**: Conditional field container
- **Select2**: Enhanced select field with search

#### Image and Media Fields
- **Image**: Enhanced image field with AWS S3 integration
- **Qrcode**: QR code generation and display field

### Configuration and Localization

#### Localization Support
- Multi-language support through `env('NOVA_LOCALE')`
- Translation keys for all user-facing text
- Armenian timezone support (`constants('ARM_TIMEZONE')`)

#### Custom Styling
- Custom CSS: `/css/admin_custom.css`
- Custom JavaScript: `/js/custom.js`

### Integration Points

#### AWS Integration
- S3 storage for documents and images
- QR code storage and retrieval
- Document generation and storage

#### External Service Integration
- ACRA credit bureau integration
- Bank report processing
- Payment gateway integrations (Upay, Telcell, IDram, Easypay)

#### Database Integration
- Model observers for data integrity
- Relationship management
- Query optimization with eager loading

## Technical Implementation Details

### Nova Configuration
- **Path**: `/nova` (configurable)
- **Guard**: Configurable authentication guard
- **Middleware Stack**:
  - Web middleware
  - Authentication
  - Authorization
  - Permission caching
  - Tool bootstrapping

### Resource Registration
- Automatic resource discovery in `app/Nova` directory
- Dynamic resource loading based on permissions
- Conditional navigation based on user roles

### Performance Optimizations
- Eager loading with `$with` property
- Query scoping for large datasets
- Pagination with configurable per-page options
- Index optimization for searchable fields

### Security Features
- Role-based access control
- Permission-based field visibility
- Action authorization
- CSRF protection
- Input validation and sanitization

## Usage Guidelines

### For Administrators
1. **User Management**: Use the User resource to manage system users and assign roles
2. **Permission Management**: Use the Permission tool to configure access rights
3. **System Monitoring**: Use dashboard cards to monitor key metrics
4. **Report Generation**: Use various download actions for data export

### For Loan Officers
1. **Loan Processing**: Use loan resources to manage applications and approvals
2. **Document Management**: Use document actions to generate and send paperwork
3. **Customer Communication**: Use SMS and email actions for customer contact
4. **Status Tracking**: Use filters to monitor loan statuses and deadlines

### For Cashiers
1. **Transaction Processing**: Use transaction resources to handle payments
2. **Cash Operations**: Use withdrawable/withdrawn loan resources for cash management
3. **QR Code Management**: Use QR resources for payment processing
4. **Purchase Requests**: Use purchase request resources for credit line operations

### For Agents
1. **Loan Assignment**: View assigned loans through filtered resources
2. **Customer Data**: Access customer information based on role permissions
3. **Document Generation**: Generate required documents for loan processing
4. **Status Updates**: Update loan statuses and progress

## Maintenance and Support

### Regular Maintenance Tasks
1. **Permission Auditing**: Regularly review user permissions and roles
2. **Data Cleanup**: Use filters to identify and clean outdated records
3. **Performance Monitoring**: Monitor dashboard metrics for system health
4. **Document Management**: Regularly archive old documents and reports

### Troubleshooting
1. **Access Issues**: Check user roles and permissions
2. **Performance Issues**: Review query optimization and indexing
3. **Integration Issues**: Verify external service connections
4. **Data Issues**: Use Nova's built-in validation and error handling

### Future Enhancements
The Nova administration system is designed to be extensible:
1. **New Resources**: Can be added by creating new resource classes
2. **Custom Actions**: Can be implemented for specific business needs
3. **Additional Tools**: Can be integrated through the tool system
4. **Enhanced Metrics**: Can be added through the metrics system

## Conclusion

The Global Credit Nova administration system provides a comprehensive interface for managing all aspects of the loan and financial services business. With its role-based access control, extensive filtering capabilities, and integrated workflow tools, it serves as the central hub for administrative operations.

The system's modular design allows for easy maintenance and future enhancements while ensuring security and performance. Regular use of the documentation and adherence to the usage guidelines will ensure optimal system performance and user experience.