package reports.helpers;

import java.util.Arrays;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Helper {

    public static Map<Integer, String> loanTypeMap;

    static {
        loanTypeMap = new HashMap<>();
        loanTypeMap.put(1, "Միասնական");
        loanTypeMap.put(2, "Ավտոգրավ");
        loanTypeMap.put(3, "֊");
        loanTypeMap.put(4, "IQOS");
        loanTypeMap.put(5, "Արփի Սոլար");
        loanTypeMap.put(6, "Իդրամ");
        loanTypeMap.put(7, "Ավտո Առոքուվաճառք");
    }
    public static Map<String, String> paymentTypeMap;

    static {
        paymentTypeMap = new HashMap<>();
        paymentTypeMap.put("App\\Models\\CashPayment", "Կանխիկ");
        paymentTypeMap.put("App\\Models\\CardToCardPayment", "Քարտային");
        paymentTypeMap.put("App\\Models\\IdramWalletPayment", "Իդրամ");
        paymentTypeMap.put("App\\Models\\EasypayWalletPayment", "Իզի Փեյ");
        paymentTypeMap.put("App\\Models\\WirePayment", "Բանկային Փոխանցում");
        paymentTypeMap.put("App\\Models\\ProductProvision", "Ապրանքի Տրամադրում");
    }

    public static Map<Integer, String> genderTypeMap;

    static {
        genderTypeMap = new HashMap<>();
        genderTypeMap.put(1, "Արական");
        genderTypeMap.put(2, "Իգական");
    }

    public static Map<String, String> loanSignTypeMap;

    static {
        loanSignTypeMap = new HashMap<>();
        loanSignTypeMap.put("t", "Գրասենյակում");
        loanSignTypeMap.put("f", "Առցանց");
    }

    public static Map<String, String> rejectionReasonsMap;

    static {
        rejectionReasonsMap = new HashMap<>();
        rejectionReasonsMap.put("age", "Տարիք");
        rejectionReasonsMap.put("isDead", "Մահացած");
        rejectionReasonsMap.put("loanClass", "Վարկային դաս");
        rejectionReasonsMap.put("hasOverdueLoans", "Ժամկենտնաց վարկ");
        rejectionReasonsMap.put("acraStatus", "ԱՔՌԱ կարգավիճկ");
        rejectionReasonsMap.put("fico", "Սքոր");
        rejectionReasonsMap.put("amountByLoad", "Բեռ");
        rejectionReasonsMap.put("hasCredit", "Առկա վարկ");
        rejectionReasonsMap.put("dailyLimits", "Օրեկան վարկավորման սահման");
        rejectionReasonsMap.put("weekendLimits", "Շաբաթ,Կիրակի վարկավորման սահման");
        rejectionReasonsMap.put("weekendNightLimits", "Շաբաթ,Կիրակի գիշերային վարկավորման սահման");
        rejectionReasonsMap.put("nightLimits", "Գիշերային վարկավորման սահման");
        rejectionReasonsMap.put("guaranteeClass", "Երաշխավորության Դաս");
        rejectionReasonsMap.put("delayQuantity", "Ուշացումների քանակ");
        rejectionReasonsMap.put("requestQuantity", "Հարցումների քանակ");
        rejectionReasonsMap.put("vehicleAllowedPrice", "Մեքենայի թույլատրելի արժեք");
        rejectionReasonsMap.put("allowedDuration", "Վակի առավելագույն ժամկետ");
        rejectionReasonsMap.put("blocked", "Բլոկավորված Մեքենա");
        rejectionReasonsMap.put("allowedLending", "Գրավադրման թույլատրություն");
        rejectionReasonsMap.put("vehicleVin", "ՎԻՆ");
    }

    public static Map<String, String> maritalStatus;

    static {
        maritalStatus = new HashMap<>();
        maritalStatus.put("single", "Ամուրի");
        maritalStatus.put("marriage", "Ամուսնացած");
        maritalStatus.put("divorce", "Ամուսնալուծված");
    }

    public static ArrayList<List<Integer>> ageRangs = new ArrayList<>();

    static {
        ageRangs.add(Arrays.asList(21, 25));
        ageRangs.add(Arrays.asList(26, 35));
        ageRangs.add(Arrays.asList(36, 45));
        ageRangs.add(Arrays.asList(46, 55));
        ageRangs.add(Arrays.asList(56, 65));
        ageRangs.add(Arrays.asList(66, 99));
    }

    public static ArrayList<List<Double>> monthlyPaymentRangs = new ArrayList<>();

    static {
        monthlyPaymentRangs.add(Arrays.asList(1000.0, 25000.0));
        monthlyPaymentRangs.add(Arrays.asList(25001.0, 50000.0));
        monthlyPaymentRangs.add(Arrays.asList(50001.0, 75000.0));
        monthlyPaymentRangs.add(Arrays.asList(75001.0, 100000.0));
        monthlyPaymentRangs.add(Arrays.asList(100001.0, 125000.0));
        monthlyPaymentRangs.add(Arrays.asList(125001.0, 150000.0));
        monthlyPaymentRangs.add(Arrays.asList(150001.0, 175000.0));
        monthlyPaymentRangs.add(Arrays.asList(175001.0, 200000.0));
        monthlyPaymentRangs.add(Arrays.asList(200000.0, 100000000.0));
    }

    public static String currencyFormat(Double value) {
        return String.format("%,.2f", value);
    }

    public static final int TWELVE_MONTHS = 12;
}
