package reports.service.nork;

import reports.ReportLogger;
import reports.helpers.DateHelper;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CitizenWorkHistory {
    private String workDataTemplate = "\n\r ՀՎՀՀ ։ %s, Աշխատավայր։ %s, Հասցե։ %s, Հեռ․ համար։ %s, Ընդունման ամսաթիվ։ %s, Ազատման ամսաթիվ։ %s, Աշխատավարձ։ %s։";

    public Map<String, WorkData> preparedWorkData(List<WorkData> workDataList) {
        try {
            // create Map by key: hvvh+first_entry_date
            Map<String, WorkData> workHistory = new HashMap<String, WorkData>();
            for (WorkData workDate : workDataList) {
                composeWorkHistory(workHistory, workDate);
            }

            return workHistory;
        } catch (Exception e) {
            ReportLogger.logger.error("Citizen Work History Exception", e);
        }

        return null;
    }

    /**
     * Parse citizen work date and grouping it in Map by key hvhh+first_entry_date,
     * in each group logical detecting EntryDate and ExpiryDate
     *
     * @param workHistory
     * @param wd
     */
    private void composeWorkHistory(Map<String, WorkData> workHistory, WorkData wd) {
        String workPlace = wd.getHvhh() + wd.getPajmanData();
        WorkData workHistoryByPlace = null;

        if (workHistory.containsKey(workPlace)) {
            workHistoryByPlace = updateWorkPlace(workHistory.get(workPlace), wd);
        } else {
            workHistoryByPlace = wd;
        }

        workHistory.put(workPlace, workHistoryByPlace);
    }

    /**
     * Updating citizen work history map
     * Saving min EntryDate and max ExpiryDate
     * if expiry date = "", citizen till working there
     *
     * @param workPlace
     * @param wd
     * @return
     */
    private WorkData updateWorkPlace(WorkData workPlace, WorkData wd) {
        Object expiry = maxExpiryDate(workPlace.getExpiryDate(), wd.getExpiryDate());
        if (expiry == null || ((Date) expiry).compareTo(workPlace.getExpiryDate()) > 0) {
            workPlace = wd;
        }
        workPlace.setEntryDate(DateHelper.minEntryDate((Date) workPlace.getEntryDate(), wd.getEntryDate()));

        return workPlace;
    }

    /**
     * Try to return null value when still working) or last expiry Date
     *
     * @param date
     * @param newDate
     * @return
     */
    private Date maxExpiryDate(Date date, Date newDate) {
        if (date == null || newDate == null) {
            return newDate;
        }

        return newDate.compareTo(date) > 0 ? newDate : date;
    }

    /**
     * Format NORK String date to Date type
     *
     * @param date
     * @return
     */
    private Date getDate(String date) {
        return DateHelper.convertToDate(date, DateHelper.NORK_DATE);
    }

    /**
     * Convert prepared citizen work history to String by template
     *
     * @param workHistory
     * @return String
     */
    public String workHistoryToString(Map<String, WorkData> workHistory) {
        StringBuilder workHistoryStr = new StringBuilder();
        workHistory.forEach((k, v) ->
                workHistoryStr.append(String.format(workDataTemplate,
                        v.getHvhh(),
                        v.getWorkName(),
                        v.getAddress(),
                        v.getWorkPhone(),
                        DateHelper.formatDateString(v.getEntryDate(), DateHelper.DATE),
                        DateHelper.formatDateString(v.getExpiryDate(), DateHelper.DATE),
                        v.getSalary())
                )
        );
        return workHistoryStr.toString();
    }
}
