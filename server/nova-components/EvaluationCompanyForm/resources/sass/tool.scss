@import '../../node_modules/@braid/vue-formulate/themes/snow/snow.scss';

.form {
  .tab-content {
    margin-top: 50px;
  }

  .formulate-input {
    width: calc(25% - 25px);
    margin-right: 25px;

    input {
      max-height: 36px;

      &:focus {
        border: 1px solid black;
      }
    }

    textarea {
      min-height: 36px;
    }

    &.hidden-input {
      visibility: hidden;
    }
  }

  .formulate-form-errors {
    margin: 8px 0 0 0;
  }

  .other-notes {
    margin-top: 20px;

    textarea {
      width: 400px;
      min-height: 100px;
    }
  }

  .other-tab {
    .additional-notes-info {
      width: 500px;

      textarea {
        min-height: 250px;
        width: 500px;
      }
    }

    .additional-notes-info + .formulate-input-help {
      width: 500px;
    }
  }
}

.nav-tabs-navigation {
  ul {
    li a {
      background: #eeeeee;
    }
  }
}

.formulate-input-element {
  input:disabled,
  input[disabled],
  textarea:disabled,
  textarea[disabled] {
    cursor: not-allowed !important;
    resize: none;
  }
}

.reml-requirments-container {
  display: flex;
  justify-content: center;
  align-items: center;

  input {
    width: 22px;
    height: 22px;
    cursor: pointer;
  }

  label {
    padding-top: 0;
    margin-left: 10px;
  }
}

.mortgage-subject-container {
  textarea {
    min-height: 120px !important;
  }
}

.upload-documents {
  label {
    font-weight: bold;
    font-size: 0.9em;
  }

  .text-danger {
    color: #960505;
    font-size: 0.8em;
  }
}

.confirm-modal {
  & > div {
    align-self: center;

    .modal-container {
      padding: 24px 24px 12px 24px;
    }

    h4 {
      font-size: 18px;
    }

    .actions {
      display: flex;
      justify-content: flex-end;
    }
  }
}

.phone-number-input {
  position: relative;

  .prefix {
    position: absolute;
    left: 28px;
    top: 50%;
    transform: translate(-50%, -50%);

    &::after {
      content: '';
      border-right: 1px solid #cecece;
      height: 36px;
      position: absolute;
      top: -8px;
      margin-left: 8px;
    }
  }

  input {
    padding-left: 60px !important;
  }
}
