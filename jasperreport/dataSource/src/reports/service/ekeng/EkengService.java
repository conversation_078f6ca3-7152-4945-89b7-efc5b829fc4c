package reports.service.ekeng;

import org.json.JSONException;
import org.json.JSONObject;
import org.json.JSONTokener;
import reports.ReportLogger;
import reports.helpers.DateHelper;
import reports.service.nork.WorkData;

import java.util.Date;

public class EkengService {
    protected EkengData ekengData = null;

    public EkengService(String ekeng) {
        try {
            Object ekengJson = new JSONTokener(ekeng).nextValue();
            this.ekengData = new EkengData((JSONObject) ekengJson);
        } catch (JSONException e) {
            ReportLogger.logger.error("Compose Work Data JSONException", e);
        }
    }

    public int getVehicleCount() {
        try {
            return this.ekengData.getVehicleCount();
        } catch (Exception e) {
            ReportLogger.logger.error("Get Vehicle Count JSONException", e);
        }

        return 0;
    }

    public String getMaritalStatus() {
        Date date = null;
        String maritalType = EkengData.E_CIVIL_SINGLE;

        try {
            for (JSONObject e_civil : this.ekengData.getE_civil()) {
                Date e_date = DateHelper.convertToDate(e_civil.getString("cert_date"), DateHelper.E_CIVIL_DATE);
                String type = e_civil.getString("type");
                if ((date == null || e_date.compareTo(date) > 0) &&
                        (type.equals(EkengData.E_CIVIL_MARRIAGE) || type.equals(EkengData.E_CIVIL_DIVORCE))
                ) {
                    date = e_date;
                    maritalType = type;
                }
            }

            return maritalType;
        } catch (JSONException e) {
            ReportLogger.logger.error("Get Marital Status JSONException", e);
        }
        return "";
    }
}
