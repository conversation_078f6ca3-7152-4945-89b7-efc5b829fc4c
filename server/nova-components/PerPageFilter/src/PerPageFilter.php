<?php

namespace Globalcredit\PerPageFilter;

use Illuminate\Http\Request;
use Laravel\Nova\Filters\Filter;

class PerPageFilter extends Filter
{
    const OPTIONS = [
        '25' => '25',
        '50' => '50',
        '100' => '100',
        '200' => '200',
    ];

    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'per-page-filter';

    /**
     * Get the displayable name of the resource tool.
     *
     * @return string
     */
    public function name()
    {
        return __('Per Page');
    }

    public function default()
    {
        $options = self::OPTIONS;
        // First value of the option array
        return reset($options);
    }

    /**
     * Apply the filter to the given query.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param mixed                                 $value
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(Request $request, $query, $value)
    {
        return $query;
    }

    /**
     * Get the filter's available options.
     *
     * @return array
     */
    public function options(Request $request)
    {
        return self::OPTIONS;
    }
}
