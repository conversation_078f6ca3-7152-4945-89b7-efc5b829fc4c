package reports.loanReport.loanRepository;

import reports.db.QueryExecutor;

import java.sql.*;
import java.util.List;
import java.util.Map;

public class LoanRepository implements ILoanRepository {

    private QueryExecutor queryExecutor;
    private final String LOAN_QUERY = "select loan_security_metas.content->'nork'->>'WorkData' as work_data,\n" +
            "citizens.first_name, citizens.last_name, citizens.middle_name, citizens.birth_date, citizen_passports.passport_number, citizen_passports.given_date, citizen_passports.expire_date,\n" +
            "concat(citizens.region, ' ', citizens.city, ' ', citizens.street, ' ', citizens.building, ' ', citizens.apartment )  as address, citizens.phone_number, citizens.email, \n" +
            "loan_securities.ssn, loan_securities.loan_type_id,\n" +
            "concat(spouses.first_name, ' ' ,spouses.last_name, ' ', spouses.middle_name) as spouse,\n" +
            "vehicles.vin, vehicles.\"number\" as vehicle_number, vehicles.recording_date, vehicles.owner_cert_id,\n" +
            "concat(vehicle_models.mark, ' ',vehicle_models.model) as vehicle_model,\n" +
            "mortgages.contract_number as mortgage_contract_number, \n" +
            "loans.amount, loans.sign_date, loans.payment_type,\n" +
            "citizen_face_recognitions.image_path \n" +
            "from\n" +
            "loans inner join loan_securities on loans .id = loan_securities .loan_id\n" +
            "inner join loan_security_metas on loan_securities .id = loan_security_metas .loan_security_id\n" +
            "inner join citizens on citizens .loan_id = loans.id\n" +
            "inner join citizen_passports on citizens.id = citizen_passports.citizen_id\n" +
            "left join citizen_face_recognitions on citizen_face_recognitions.loan_security_id = loan_securities.id \n" +
            "left join spouses on citizens.id = spouses.citizen_id \n" +
            "left join vehicles on loans.id =vehicles.loan_id \n" +
            "left join vehicle_models on vehicles.vehicle_model_id = vehicle_models.id \n" +
            "left join mortgages on loans.id = mortgages.loan_id \n" +
            "where \n" +
            "citizen_passports.\"primary\" = true\n" +
            "and loans.contract_number = ?\n" +
            "and loans.status = 'CONFIRMED' \n" +
            // status is null - loans signed before deploying face recognition
            "and (citizen_face_recognitions.status = 'MATCHED' or citizen_face_recognitions.status is null) \n" +
            "and loan_security_metas.step = 'CITIZEN_INFO'";

    private final String LOAN_DOCS_QUERY = "select path, document_type \n" +
            "from \n" +
            "loans inner join loan_documents on loans.id = loan_documents.loan_id \n" +
            "where loans.contract_number = ?";

    private final String CASH_PAYMENT_QUERY = "select concat(users.first_name , ' ', users.last_name) as withdrawn_user, cash_payments.withdrawn, cash_payments.\"type\" as cash_payment_type from loans \n " +
            "inner join cash_payments on loans.payment_id = cash_payments.id \n" +
            "left join users on users.id = cash_payments.withdraw_operator_id \n" +
            "where loans.contract_number = ?";

    private final String CARD_TO_CARD_QUERY = "select card_to_card_payments.card_number as payment_id, withdrawn from loans \n" +
            "inner join card_to_card_payments on loans.payment_id = card_to_card_payments.id \n" +
            "where loans.contract_number = ?";

    private final String IDRAM_QUERY = "select idram_wallet_payments.wallet_id as payment_id, withdrawn from loans \n" +
            "inner join idram_wallet_payments on loans.payment_id = idram_wallet_payments.id \n" +
            "where loans.contract_number = ?";

    private final String PRODUCT_PROVISION_QUERY = "select concat(users.first_name , ' ', users.last_name) as withdrawn_user, product_provisions.withdrawn from loans \n" +
            "inner join product_provisions on loans.payment_id = product_provisions.id \n" +
            "inner join pipes on pipes . loan_id = loans.id\n" +
            "left join users on users.id = pipes.seller_id \n" +
            "where loans.contract_number = ?";

    public LoanRepository(Connection conn) {
        this.queryExecutor = new QueryExecutor(conn);
    }

    /**
     * Get loan from DB
     *
     * @param contractNumber
     * @return
     */
    public Map<String, Object> getLoanByContractNumber(String contractNumber) {
        return this.queryExecutor.executeSelectQueryByClause(LOAN_QUERY, contractNumber);
    }

    public List<Map<String, Object>> getDocumentsByContractNumber(String contractNumber) {
        return this.queryExecutor.executeMultipleRowSelectQueryByClause(LOAN_DOCS_QUERY, contractNumber);
    }

    /**
     * Get cash payment from DB
     *
     * @param contractNumber
     * @return
     */
    public Map<String, Object> getCashPaymentByContractNumber(String contractNumber) {
        return this.queryExecutor.executeSelectQueryByClause(CASH_PAYMENT_QUERY, contractNumber);
    }

    /**
     * Get card payment from DB
     *
     * @param contractNumber
     * @return
     */
    public Map<String, Object> getCardPaymentByContractNumber(String contractNumber) {
        return this.queryExecutor.executeSelectQueryByClause(CARD_TO_CARD_QUERY, contractNumber);
    }

    /**
     * Get idram payment from DB
     *
     * @param contractNumber
     * @return
     */
    public Map<String, Object> getIdramPaymentByContractNumber(String contractNumber) {
        return this.queryExecutor.executeSelectQueryByClause(IDRAM_QUERY, contractNumber);
    }

    /**
     * Get idram payment from DB
     *
     * @param contractNumber
     * @return
     */
    public Map<String, Object> getProductProvisionByContractNumber(String contractNumber) {
        return this.queryExecutor.executeSelectQueryByClause(PRODUCT_PROVISION_QUERY, contractNumber);
    }
}