#
# Copyright (C) 2005 - 2019 TIBCO Software Inc. All rights reserved.
# http://www.jaspersoft.com.
#
# Unless you have purchased a commercial license agreement from Jaspersoft,
# the following license terms apply:
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU Affero General Public License as
# published by the Free Software Foundation, either version 3 of the
# License, or (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
# GNU Affero General Public License for more details.
#
# You should have received a copy of the GNU Affero General Public License
# along with this program. If not, see <http://www.gnu.org/licenses/>.
#

# This file will consolidate all the configurable quartz properties
#
# Other properties which didn't need configuration are kept in js.quartz.base.properties

quartz.delegateClass=org.quartz.impl.jdbcjobstore.PostgreSQLDelegate

quartz.tablePrefix=QRTZ_

#value would be one of the following
#SMART_POLICY
#MISFIRE_INSTRUCTION_FIRE_NOW
#MISFIRE_INSTRUCTION_IGNORE_MISFIRE_POLICY
report.quartz.misfirepolicy.singlesimplejob=SMART_POLICY

#value would be one of the following
#SMART_POLICY
#MISFIRE_INSTRUCTION_FIRE_NOW
#MISFIRE_INSTRUCTION_IGNORE_MISFIRE_POLICY
#MISFIRE_INSTRUCTION_RESCHEDULE_NEXT_WITH_EXISTING_COUNT
#MISFIRE_INSTRUCTION_RESCHEDULE_NEXT_WITH_REMAINING_COUNT
#MISFIRE_INSTRUCTION_RESCHEDULE_NOW_WITH_EXISTING_REPEAT_COUNT
#MISFIRE_INSTRUCTION_RESCHEDULE_NOW_WITH_REMAINING_REPEAT_COUNT
report.quartz.misfirepolicy.repeatingsimplejob=SMART_POLICY


#value would be one of the following
#SMART_POLICY
#MISFIRE_INSTRUCTION_IGNORE_MISFIRE_POLICY
#MISFIRE_INSTRUCTION_FIRE_ONCE_NOW
#MISFIRE_INSTRUCTION_DO_NOTHING
report.quartz.misfirepolicy.calendarjob=SMART_POLICY
quartz.extraSettings=

report.scheduler.web.deployment.uri=http://gayane:8081/jasperserver-pro

report.scheduler.mail.sender.host=smtp.gmail.com
report.scheduler.mail.sender.username=<EMAIL>
report.scheduler.mail.sender.password=qweqweqwe1357
report.scheduler.mail.sender.from=<EMAIL>
report.scheduler.mail.sender.protocol=smtp
report.scheduler.mail.sender.port=587
