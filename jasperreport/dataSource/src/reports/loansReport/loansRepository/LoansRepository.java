package reports.loansReport.loansRepository;

import reports.helpers.DateHelper;
import reports.db.QueryExecutor;
import reports.loansReport.InputPaymentTypes;

import java.sql.Connection;
import java.sql.Timestamp;
import java.util.*;

public class LoansRepository implements ILoansRepository {
    private final int ALL_LOANS = 0;

    private final Integer START_DATE_QUERY_POSITION = 1;
    private final Integer END_DATE_QUERY_POSITION = 2;
    private final Integer LOAN_TYPE_QUERY_POSITION = 3;

    private QueryExecutor queryExecutor;
    private StringBuilder LOAN_CASH_QUERY = new StringBuilder("select \n" +
            "citizens.first_name, citizens.last_name, citizens.middle_name, citizens.birth_date, citizens.gender, citizen_passports.passport_number, citizen_passports.given_date, citizen_passports.expire_date,\n" +
            "concat(citizens.region, ' ', citizens.city, ' ', citizens.street, ' ', citizens.building, ' ', citizens.apartment )  as address, citizens.phone_number, citizens.salary, \n" +
            "loan_securities.ssn, loan_securities.id as loan_securities_id,\n" +
            "loans.amount, loans.sign_date, loans.payment_type, loans.contract_number, loans.is_offline, loans.fico_score, loans.existing_fico_score, loans.updated_at,\n" +
            "cash_payments . withdrawn, cash_payments .type as cash_payment_office , users.first_name as user_first_name, users .last_name as user_last_name\n" +
            "from loans \n" +
            "inner join loan_securities on loans .id = loan_securities .loan_id\n" +
            "inner join citizens on citizens .loan_id = loans.id\n" +
            "inner join citizen_passports on citizens.id = citizen_passports.citizen_id\n" +
            "inner join cash_payments on loans.payment_id =cash_payments.id \n" +
            "left join users on users.id = cash_payments.withdraw_operator_id \n" +
            "where \n" +
            "citizen_passports.\"primary\" = true\n" +
            "and loans.status = 'CONFIRMED' \n" +
            "and loans.payment_type = 'App\\Models\\CashPayment'" +
            "and loans.sign_date >= ? \n" +
            "and loans.sign_date <= ? \n");

    private StringBuilder LOAN_CARD_TO_CARD_QUERY = new StringBuilder("select \n" +
            "citizens.first_name, citizens.last_name, citizens.middle_name, citizens.birth_date, citizens.gender, citizen_passports.passport_number, citizen_passports.given_date, citizen_passports.expire_date,\n" +
            "concat(citizens.region, ' ', citizens.city, ' ', citizens.street, ' ', citizens.building, ' ', citizens.apartment )  as address, citizens.phone_number, citizens.birth_date, citizens.salary, \n" +
            "loan_securities.ssn, loan_securities.id as loan_securities_id,\n" +
            "loans.amount, loans.sign_date, loans.payment_type, loans.contract_number, loans.is_offline, loans.fico_score, loans.existing_fico_score, loans.updated_at,\n" +
            "card_to_card_payments.card_number, card_to_card_payments.withdrawn \n" +
            "from loans \n" +
            "inner join loan_securities on loans .id = loan_securities .loan_id\n" +
            "inner join citizens on citizens .loan_id = loans.id\n" +
            "inner join citizen_passports on citizens.id = citizen_passports.citizen_id\n" +
            "inner join card_to_card_payments on loans.payment_id = card_to_card_payments.id \n" +
            "where \n" +
            "citizen_passports.\"primary\" = true \n" +
            "and loans.status = 'CONFIRMED' \n" +
            "and loans.payment_type = 'App\\Models\\CardToCardPayment' \n" +
            "and loans.sign_date >= ?\n" +
            "and loans.sign_date <= ?\n");

    private StringBuilder LOAN_IDRAM_QUERY = new StringBuilder("select \n" +
            "citizens.first_name, citizens.last_name, citizens.middle_name, citizens.birth_date, citizens.gender, citizen_passports.passport_number, citizen_passports.given_date, citizen_passports.expire_date,\n" +
            "concat(citizens.region, ' ', citizens.city, ' ', citizens.street, ' ', citizens.building, ' ', citizens.apartment )  as address, citizens.phone_number, citizens.birth_date, citizens.salary, \n" +
            "loan_securities.ssn, loan_securities.id as loan_securities_id,\n" +
            "loans.amount, loans.sign_date, loans.payment_type, loans.contract_number, loans.is_offline, loans.fico_score, loans.existing_fico_score, loans.updated_at,\n" +
            "idram_wallet_payments.wallet_id, idram_wallet_payments.withdrawn \n" +
            "from loans \n" +
            "inner join loan_securities on loans .id = loan_securities .loan_id\n" +
            "inner join citizens on citizens .loan_id = loans.id\n" +
            "inner join citizen_passports on citizens.id = citizen_passports.citizen_id\n" +
            "inner join idram_wallet_payments on loans.payment_id =idram_wallet_payments.id \n" +
            "where \n" +
            "citizen_passports.\"primary\" = true\n" +
            "and loans.payment_type = 'App\\Models\\IdramWalletPayment' \n" +
            "and loans.status = 'CONFIRMED' \n" +
            "and loans.sign_date >= ?\n" +
            "and loans.sign_date <= ?\n");

    private StringBuilder PRODUCT_PROVISION_QUERY = new StringBuilder("select \n" +
            "citizens.first_name, citizens.last_name, citizens.middle_name, citizens.birth_date, citizens.gender, citizen_passports.passport_number, citizen_passports.given_date, citizen_passports.expire_date,\n" +
            "concat(citizens.region, ' ', citizens.city, ' ', citizens.street, ' ', citizens.building, ' ', citizens.apartment )  as address, citizens.phone_number, citizens.birth_date, citizens.salary, \n" +
            "loan_securities.ssn, loan_securities.id as loan_securities_id,\n" +
            "loans.amount, loans.sign_date, loans.payment_type, loans.contract_number, loans.is_offline, loans.fico_score, loans.existing_fico_score, loans.updated_at,\n" +
            "users.first_name as user_first_name, users.last_name as user_last_name, product_provisions.withdrawn\n" +
            "from loans \n" +
            "inner join loan_securities on loans .id = loan_securities .loan_id\n" +
            "inner join citizens on citizens .loan_id = loans.id\n" +
            "inner join citizen_passports on citizens.id = citizen_passports.citizen_id\n" +
            "inner join pipes on pipes . loan_id = loans.id\n" +
            "inner join product_provisions on product_provisions.id = loans.payment_id\n" +
            "left join users on users.id = pipes.seller_id \n" +
            "where \n" +
            "citizen_passports.\"primary\" = true\n" +
            "and loans.payment_type = 'App\\Models\\ProductProvision'\n" +
            "and loans.status = 'CONFIRMED' \n" +
            "and loans.sign_date >= ?\n" +
            "and loans.sign_date <= ?\n");

    private String KEY_BY_COLUMN = "loan_securities_id";

    private StringBuilder CONTEXT_INFO_QUERY = new StringBuilder("select loan_securities.id as loan_securities_id, loan_security_metas.\"content\" -> 'input' ->> 'loanLoad' as loan_load from loans \n" +
            "inner join loan_securities on loans.id = loan_securities.loan_id \n" +
            "inner join loan_security_metas on loan_security_metas.loan_security_id = loan_securities.id \n" +
            "where \n" +
            "loans.status = 'CONFIRMED' \n" +
            "and loans.sign_date >= ? \n" +
            "and loans.sign_date <= ? \n" +
            "and loan_security_metas.step = 'CONTEXT_INFO'\n");

    private StringBuilder FETCH_CITIZEN_QUERY = new StringBuilder("select  loan_securities.id as loan_securities_id,\n" +
            "loan_security_metas.\"content\"->'client'->>'isMobile' as is_mobile,\n" +
            "loan_security_metas.\"content\"->'client'->>'isDesktop' as is_desktop,\n" +
            "loan_security_metas.\"content\"->'client'->>'isTablet' is_tablet,\n" +
            "(loan_security_metas.\"content\"->'response'->>'body')::json->>'amount' as max_amount\n" +
            "from loans \n" +
            "inner join loan_securities on loans.id = loan_securities.loan_id \n" +
            "inner join loan_security_metas on loan_security_metas.loan_security_id = loan_securities.id \n" +
            "where \n" +
            "loans.status = 'CONFIRMED' \n" +
            "and loans.sign_date >= ? \n" +
            "and loans.sign_date <= ? \n" +
            "and loan_security_metas.step = 'FETCH_CITIZEN'");

    private StringBuilder CITIZEN_INFO_QUERY = new StringBuilder("select loan_securities.id as loan_securities_id,\n" +
            "loan_security_metas.content->'nork'->>'WorkData' as work_data,\n" +
            "loan_security_metas.\"content\"->'acra'->'PARTICIPIENT'->'Loans'->'Loan' as acra_loans\n" +
            "from loans \n" +
            "inner join loan_securities on loans.id = loan_securities.loan_id \n" +
            "inner join loan_security_metas on loan_security_metas.loan_security_id = loan_securities.id \n" +
            "where \n" +
            "loans.status = 'CONFIRMED' \n" +
            "and loans.sign_date >= ? \n" +
            "and loans.sign_date <= ? \n" +
            "and loan_security_metas.step = 'CITIZEN_INFO'");

    public LoansRepository(Connection conn) {
        this.queryExecutor = new QueryExecutor(conn);
    }

    public Map<Integer, Map<String, Object>> getLoansByParameters(Map<String, Object> parameters) {
        Map<Integer, Map<String, Object>> loans = getCashLoansByParameters(parameters);
        loans.putAll(getCardToCardLoansByParameters(parameters));
        loans.putAll(getIdramLoansByParameters(parameters));
        loans.putAll(getProductProvisionLoansByParameters(parameters));

        return loans;
    }

    public Map<Integer, Map<String, Object>> getCashLoansByParameters(Map<String, Object> parameters) {
        Map<Integer, Object> params = prepareInputValues(parameters);
        prepareQueryLoanType(parameters, LOAN_CASH_QUERY);

        return this.queryExecutor.executeSelectQueryByClauseMapKeyByInt(LOAN_CASH_QUERY.toString(), params, KEY_BY_COLUMN);
    }

    public Map<Integer, Map<String, Object>> getCardToCardLoansByParameters(Map<String, Object> parameters) {
        Map<Integer, Object> params = prepareInputValues(parameters);
        prepareQueryLoanType(parameters, LOAN_CARD_TO_CARD_QUERY);

        return this.queryExecutor.executeSelectQueryByClauseMapKeyByInt(LOAN_CARD_TO_CARD_QUERY.toString(), params, KEY_BY_COLUMN);
    }

    public Map<Integer, Map<String, Object>> getIdramLoansByParameters(Map<String, Object> parameters) {
        Map<Integer, Object> params = prepareInputValues(parameters);
        prepareQueryLoanType(parameters, LOAN_IDRAM_QUERY);

        return this.queryExecutor.executeSelectQueryByClauseMapKeyByInt(LOAN_IDRAM_QUERY.toString(), params, KEY_BY_COLUMN);
    }

    public Map<Integer, Map<String, Object>> getProductProvisionLoansByParameters(Map<String, Object> parameters) {
        Map<Integer, Object> params = prepareInputValues(parameters);
        prepareQueryLoanType(parameters, PRODUCT_PROVISION_QUERY);

        return this.queryExecutor.executeSelectQueryByClauseMapKeyByInt(PRODUCT_PROVISION_QUERY.toString(), params, KEY_BY_COLUMN);
    }

    public Map<Integer, Map<String, Object>> getContextInfo(Map<String, Object> parameters, int payment_type) {
        Map<Integer, Object> params = prepareInputValues(parameters);
        prepareQueryLoanType(parameters, CONTEXT_INFO_QUERY);
        prepareQueryPaymentType(payment_type, CONTEXT_INFO_QUERY);

        return this.queryExecutor.executeSelectQueryByClauseMapKeyByInt(CONTEXT_INFO_QUERY.toString(), params, KEY_BY_COLUMN);
    }

    public Map<Integer, Map<String, Object>> getFetchCitizen(Map<String, Object> parameters, int payment_type) {
        Map<Integer, Object> params = prepareInputValues(parameters);
        prepareQueryLoanType(parameters, FETCH_CITIZEN_QUERY);
        prepareQueryPaymentType(payment_type, FETCH_CITIZEN_QUERY);

        return this.queryExecutor.executeSelectQueryByClauseMapKeyByInt(FETCH_CITIZEN_QUERY.toString(), params, KEY_BY_COLUMN);
    }

    public Map<Integer, Map<String, Object>> getCitizenInfo(Map<String, Object> parameters, int payment_type) {
        Map<Integer, Object> params = prepareInputValues(parameters);
        prepareQueryLoanType(parameters, CITIZEN_INFO_QUERY);
        prepareQueryPaymentType(payment_type, CITIZEN_INFO_QUERY);

        return this.queryExecutor.executeSelectQueryByClauseMapKeyByInt(CITIZEN_INFO_QUERY.toString(), params, KEY_BY_COLUMN);
    }

    /**
     * Preparing query parameters
     */
    private Map<Integer, Object> prepareInputValues(Map<String, Object> parameters) {
        Map<Integer, Object> params = new HashMap<Integer, Object>();
        params.put(START_DATE_QUERY_POSITION, (Timestamp) DateHelper.modifyInputDate((Date) parameters.get("start_date"), DateHelper.START_DAY_DEVIATION, DateHelper.UTC_TIME_DEVIATION));
        params.put(END_DATE_QUERY_POSITION, (Timestamp) DateHelper.modifyInputDate((Date) parameters.get("end_date"), DateHelper.END_DAY_DEVIATION, DateHelper.UTC_TIME_DEVIATION));

        if ((int) parameters.get("loan_type") != ALL_LOANS) {
            params.put(LOAN_TYPE_QUERY_POSITION, parameters.get("loan_type"));
        }

        return params;
    }

    /**
     * Preparing query, add loan_type, if necessary
     */
    private void prepareQueryLoanType(Map<String, Object> parameters, StringBuilder query) {
        if ((int) parameters.get("loan_type") != ALL_LOANS) {
            query.append(" and loans.loan_type_id = ?");
        }
    }

    /**
     * Preparing meta steps query, add payment_type, if necessary
     */
    private void prepareQueryPaymentType(int type, StringBuilder query) {
        String paymentType = "";
        switch (type) {
            case InputPaymentTypes.ALL:
                break;
            case InputPaymentTypes.CASH:
                paymentType = " and loans.payment_type = 'App\\Models\\CashPayment'";
                break;
            case InputPaymentTypes.CARD:
                paymentType = " and loans.payment_type = 'App\\Models\\CardToCardPayment'";
                break;
            case InputPaymentTypes.IDRAM:
                paymentType = " and loans.payment_type = 'App\\Models\\IdramWalletPayment'";
                break;
            case InputPaymentTypes.PRODUCT_PROVISION:
                paymentType = " and loans.payment_type = 'App\\Models\\ProductProvision'";
                break;
        }

        query.append(paymentType);
    }
}
