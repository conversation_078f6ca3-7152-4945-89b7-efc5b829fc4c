import MediaView from '../../../MediaView/resources/js/components/Tool';
import SellerInfo from './components/SellerInfo';
import MortgageSubjectInfo from './components/MortgageSubjectInfo';
import flatPickr from 'vue-flatpickr-component';
import VueTabs from 'vue-nav-tabs/dist/vue-tabs.js';
import VueFormulate from '@braid/vue-formulate';
import VueMask from 'v-mask';
import VueNumeric from 'vue-numeric';
import NumericInput from './components/NumericInput';

import 'vue-nav-tabs/themes/vue-tabs.css';

Nova.booting((Vue, router, store) => {
  Vue.component('evaluation-company-form', require('./components'));
  Vue.component('loading', require('./components/Loading'));
  Vue.component('confirm-modal', require('./components/ConfirmModal'));
  Vue.component('media-view', MediaView);
  Vue.component('date-picker', flatPickr);
  Vue.component('seller-info', SellerInfo);
  Vue.component('mortgage-subject-info', MortgageSubjectInfo);
  Vue.component('vue-numeric', VueNumeric);
  Vue.component('numeric-input', NumericInput);

  Vue.use(VueTabs);
  Vue.use(VueMask);
  Vue.use(VueFormulate, {
    library: {
      numeric: {
        classification: 'text',
        component: 'numeric-input',
        slotProps: {
          component: ['currency', 'precision'],
        },
      },
    },
    rules: {
      size: (context, ...args) => {
        if (context.value) {
          return context.value.length === Number(args[0]);
        }

        return true;
      },
    },
    locales: {
      en: {
        required() {
          return `Դաշտը պարտադիր է:`;
        },
        max({ name, args }) {
          return `${name}-ը պետք է պարունակի մինչև ${args[0]} նիշ։`;
        },
        size({ name, args }) {
          return `${name}-ը պետք է պարունակի ${args[0]} նիշ։`;
        },
      },
    },
  });
});
