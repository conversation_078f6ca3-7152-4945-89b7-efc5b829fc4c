<?php

namespace Globalcredit\ApproveArpiSolarLoan\Http\Controllers;

use App\Jobs\GenerateLawyerDocument;
use App\Jobs\GenerateLoanDocuments;
use App\Jobs\SendConfirmationSMS;
use App\Models\Loan;
use Illuminate\Routing\Controller;
use Laravel\Nova\Http\Requests\NovaRequest;

class ApproveArpiSolarLoanController extends Controller
{
    public function confirmLoan(NovaRequest $request)
    {
        $loan = Loan::find($request->id);

        $loanService = resolve('\App\Services\LoanServiceOASL');

        $loanService->validateConfirmation($loan);

        $loan = $loanService->setSignDate($loan);

        $this->regenerateDocuments($loan);

        $loanService->processLoanPayment($loan);
        $loanService->saveLoanToHC($loan);

        SendConfirmationSMS::dispatch($loan);

        return response()->json($loan);
    }

    protected function regenerateDocuments($loan)
    {
        GenerateLoanDocuments::dispatch($loan, false);
        GenerateLawyerDocument::dispatch($loan);
    }

    public function rejectLoan(NovaRequest $request)
    {
        $loan = $this->updateLoanStatus($request->id, Loan::REJECTED);

        return response()->json($loan);
    }

    private function updateLoanStatus($id, $status)
    {
        $loan = Loan::find($id);

        return $loan->update(['status' => $status]);
    }
}
