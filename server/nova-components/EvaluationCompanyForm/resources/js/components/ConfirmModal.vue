<template>
  <modal @modal-close="handleClose" class="flex confirm-modal">
    <form
      @submit.prevent="handleConfirm"
      class="bg-white rounded-lg shadow-lg overflow-hidden"
      style="min-width: 500px"
    >
      <div class="bg-30 modal-container">
        <h4 class="mb-2">
          {{ __('There Are Fields Not Filled In') }}
        </h4>
        <div class="my-4">
          {{ __('Are You Sure That You Want To Continue This Operation') }}
        </div>
        <div class="mx-auto actions">
          <button
            @click.prevent="handleClose"
            class="btn btn-default font-normal"
          >
            {{ __('Cancel') }}
          </button>
          <button class="btn btn-default btn-primary w-24 ml-4 font-normal">
            {{ __('Continue') }}
          </button>
        </div>
      </div>
    </form>
  </modal>
</template>

<script>
export default {
  methods: {
    handleClose() {
      this.$emit('close');
    },

    handleConfirm() {
      this.$emit('confirm');
    },
  },
};
</script>
