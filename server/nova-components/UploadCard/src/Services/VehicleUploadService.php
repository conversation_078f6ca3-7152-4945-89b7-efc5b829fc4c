<?php

namespace Globalcredit\UploadCard\Services;

use Carbon\Carbon;

class VehicleUploadService implements IUploadCardService
{
    public function storeModels($file)
    {
        $now = Carbon::now();
        $file_name = "vehicle_models_$now.".$file->extension();

        $file->storeAs('/', $file_name, 'vehicle_models_upload');

        $vehicleService = resolve('App\Services\VehicleService');
        $vehicleService->updateVehicleModels("vehicle_models/$file_name");
    }
}
