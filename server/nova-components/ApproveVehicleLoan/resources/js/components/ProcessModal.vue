<template>
  <modal @modal-close="handleClose">
    <form
      @submit.prevent="handleConfirm"
      class="bg-white rounded-lg shadow-lg overflow-hidden"
      style="width: 600px"
    >
      <slot>
        <div class="p-8">
          <heading :level="2" class="mb-6">{{ __('Police File') }}</heading>

          <span class="form-file mr-4">
            <input
              ref="fileField"
              class="form-file-input"
              type="file"
              id="policeFile"
              name="name"
              @change="fileChange"
              v-on:click="clearErrors()"
            />
            <label
              for="policeFile"
              class="form-file-btn btn btn-default btn-primary"
            >
              {{ __('Choose File') }}
            </label>
          </span>

          <span class="text-gray-50"> {{ currentLabel }} </span>
          <p v-if="hasError" class="text-xs mt-2 text-danger">
            {{ firstError.code || firstError }}
          </p>
        </div>

        <loading v-if="loading" large="true" />
      </slot>

      <div class="bg-30 px-6 py-3 flex">
        <div class="ml-auto">
          <button
            type="button"
            @click.prevent="handleClose"
            class="btn text-80 font-normal h-9 px-3 mr-3 btn-link"
          >
            {{ __('Cancel') }}
          </button>
          <button
            type="submit"
            :disabled="loading"
            class="btn btn-default btn-primary"
          >
            {{ __('Submit') }}
          </button>
        </div>
      </div>
    </form>
  </modal>
</template>

<script>
import { Errors } from 'laravel-nova';

export default {
  props: ['resourceId'],

  data: () => ({
    file: null,
    fileName: '',
    label: '',
    uploadErrors: new Errors(),
    loading: false,
  }),

  methods: {
    handleClose() {
      this.$emit('close');
    },

    handleConfirm() {
      this.submitFile();
    },

    clearErrors() {
      this.uploadErrors = new Errors();
    },

    fileChange(event) {
      const path = event.target.value;
      const fileName = path.match(/[^\\/]*$/)[0];
      this.fileName = fileName;
      this.file = this.$refs.fileField.files[0];
    },

    async submitFile() {
      this.loading = true;
      const formData = new FormData();
      formData.append('police_file', this.file);

      try {
        this.$emit('process-modal-loading', this.loading);
        const resp = await Nova.request().post(
          `/nova-vendor/approve-vehicle-loan/loan/${this.resourceId}/police-file`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          }
        );

        this.loading = false;

        this.$router.push('/resources/vehicle-loans');
      } catch (error) {
        this.loading = false;
        this.$emit('process-modal-loading', this.loading);
        if (error.response.status == 422) {
          this.uploadErrors = new Errors(error.response.data.errors);
        } else if (error && error.response) {
          this.uploadErrors = new Errors({
            police_file: [this.__('Invalid File')],
          });
        }
      }
    },
  },

  mounted() {
    this.label = this.__('no file selected');
    this.$refs.fileField.focus();
  },

  computed: {
    currentLabel() {
      return this.fileName || this.label;
    },

    hasError() {
      return this.uploadErrors.has('police_file');
    },

    firstError() {
      if (this.hasError) {
        return this.uploadErrors.first('police_file');
      }
    },
  },
};
</script>
