package reports.loansReport.loansRepository;

import java.util.ArrayList;
import java.util.Map;

public interface ILoansRepository {
    public Map<Integer, Map<String, Object>> getLoansByParameters(Map<String, Object> parameters);

    public Map<Integer, Map<String, Object>> getCashLoansByParameters(Map<String, Object> parameters);

    public Map<Integer, Map<String, Object>> getCardToCardLoansByParameters(Map<String, Object> parameters);

    public Map<Integer, Map<String, Object>> getIdramLoansByParameters(Map<String, Object> parameters);

    public Map<Integer, Map<String, Object>> getContextInfo(Map<String, Object> parameters, int payment_type);

    public Map<Integer, Map<String, Object>> getFetchCitizen(Map<String, Object> parameters, int payment_type);

    public Map<Integer, Map<String, Object>> getCitizenInfo(Map<String, Object> parameters, int payment_type);
}
