USE [appbuffer]
GO
/****** Object:  StoredProcedure [dbo].[fix_payment50Perc]    Script Date: 10/02/21 6:46:32 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROCEDURE [dbo].[fix_payment50Perc] (@loanid AS varchar(30), @nmm AS integer, @totSF AS decimal(18, 2), @ind AS datetime)
AS

  DECLARE @div AS integer,
          @ms AS decimal(18, 2),
          @i AS integer,
          @inqsvf integer


  SET @ms = @totSF / @nmm
  DECLARE @Table TABLE (
    -- Add the column definitions for the TABLE variable here
    duedate date,
    principle decimal(18, 2),
    interes decimal(18, 2),
    sfee decimal(18, 2),
    mtot decimal(18, 2),
    rownm integer

  )
  DECLARE @inTable TABLE (

    duedate date,
    principle decimal(18, 2),
    interes decimal(18, 2),
    sfee decimal(18, 2),
    mtot decimal(18, 2),
    rownm integer

  )
  INSERT INTO @Table
    SELECT
      *,
      ROW_NUMBER() OVER (ORDER BY fdate) rownm
    FROM [dbo].[HC_graf](@loanid, GETDATE())




  INSERT INTO @inTable
    SELECT
      *,
      ROW_NUMBER() OVER (ORDER BY fdate) rownm
    FROM [dbo].[HC_graf](@loanid, @ind)
  --Select * from @Table

  --select * from @Table 

  SET @div = (SELECT
    COUNT(*)
  FROM @Table)
  SET @i = 1
  WHILE @i <= @div
  BEGIN
    SET @inqsvf = 0
    SELECT
      @inqsvf = sfee
    FROM @inTable
    WHERE duedate = (SELECT
      duedate
    FROM @Table
    WHERE rownm = @i)
    IF @inqsvf = 0
    BEGIN
      UPDATE @Table
      SET sfee = 0
      WHERE rownm = @i
      UPDATE @Table
      SET mtot = principle + interes + sfee
      WHERE rownm = @i
    END
    ELSE
    BEGIN


      UPDATE @Table
      SET sfee = @ms
      WHERE rownm = @i
      UPDATE @Table
      SET mtot = principle + interes + sfee
      WHERE rownm = @i
    END
    SET @i = @i + 1
  END
  --lday=0

  --select * from @Table

  --select * from @Table
  DECLARE @ldate varchar(20),
          @counter AS integer
  SET @ldate = (SELECT TOP 1
    duedate
  FROM @Table
  ORDER BY duedate DESC)

  SET @ldate = REPLACE(@ldate, '/', '-')
  --select @ldate
  SET @counter = 0
  SET @counter = (SELECT
    COUNT(*) AS rc
  FROM [appbuffer].dbo.LoansCredUpdates
  WHERE NEW_OUTER_CODE = @loanid
  AND ASAGR IS NULL)
  SELECT
    @counter
  IF @counter = 0
    INSERT INTO [appbuffer].dbo.LoansCredUpdates (NEW_OUTER_CODE, CODE)
      SELECT
        @loanid,
        @loanid

  SET @counter = (SELECT
    COUNT(*) AS rc
  FROM dbo.LoansCredServiceFeeSchedules
  WHERE OUTER_CODE = @loanid
  AND ASAGR IS NULL)

  IF @counter = 0
  BEGIN
    --Insert into [appbuffer].dbo.LoansCredRepSchedules (CODE,OUTER_CODE,DATE,DATEAGR,ISPROLONG,AGRDATESFILLTYPE,AGRPERIOD , COMMENT	)  	select  @loanid,@loanid,getdate(),@ldate,0,2,'0/30','FIX 50% discount schedule'
    INSERT INTO [appbuffer].dbo.LoansCredServiceFeeSchedules (OUTER_CODE, CODE, DATE, DATEAGR, COMMENT)
      SELECT
        @loanid,
        @loanid,
        GETDATE(),
        @ldate,
        'FIX 50% discount schedule'
  --select 5
  END

  --	delete [appbuffer].dbo.LoansCredRepSchedulesRows where OUTER_CODE=@loanid
  DELETE [appbuffer].dbo.LoansCredServiceFeeSchedulesRows
  WHERE OUTER_CODE = @loanid
  DECLARE @ddate AS varchar(20),
          @totaldue AS decimal(18, 2) = 0,
          @pouts AS decimal(18, 2),
          @mp AS decimal(18, 2),
          @lmpam AS decimal(18, 2),
          @msfe AS decimal(18, 2),
          @intm AS decimal(18, 2)
  SET @lmpam = (SELECT
    SUM(principle)
  FROM @Table)
  SET @i = 1

  WHILE @i <= @div
  BEGIN
    SET @mp = (SELECT
      principle
    FROM @Table
    WHERE rownm = @i)
    SET @totaldue = @totaldue + @mp
    SET @ddate = (SELECT
      duedate
    FROM @Table
    WHERE rownm = @i)
    SET @msfe = (SELECT
      sfee
    FROM @Table
    WHERE rownm = @i)
    SET @intm = (SELECT
      interes
    FROM @Table
    WHERE rownm = @i)
    SET @ddate = REPLACE(@ddate, '/', '-')
    --if @i=1 set @fdate=@ddate

    IF @i = @div
    BEGIN
      --select @loanid,@loanid,@ddate ,@msfe as sf,@mp as mp,@intm as int
      SET @pouts = @lmpam - @totaldue
      INSERT INTO [appbuffer].dbo.LoansCredServiceFeeSchedulesRows (CODE, OUTER_CODE, DATEAGR, AMOUNT)
        SELECT
          @loanid,
          @loanid,
          @ddate,
          @msfe
    --If @pouts > 0 
    --begin
    --Insert into [intermediateDb].dbo.LoansCredRepSchedulesRows (CODE,OUTER_CODE,DATEAGR,AMOUNT,[TYPE]) select @loanid,@loanid,@ddate, @mp + @pouts ,1
    --Insert into [intermediateDb].dbo.LoansCredRepSchedulesRows (CODE,OUTER_CODE,DATEAGR,AMOUNT,[TYPE]) select @loanid,@loanid,@ddate, @intm, 2
    --end

    --If @pouts < 0 
    --begin
    --Insert into [intermediateDb].dbo.LoansCredRepSchedulesRows (CODE,OUTER_CODE,DATEAGR,AMOUNT,[TYPE]) select @loanid,@loanid,@ddate, @mp + @pouts ,1
    --Insert into [intermediateDb].dbo.LoansCredRepSchedulesRows (CODE,OUTER_CODE,DATEAGR,AMOUNT,[TYPE]) select @loanid,@loanid,@ddate, @intm, 2
    --end
    --If @pouts = 0 
    --begin
    --Insert into [intermediateDb].dbo.LoansCredRepSchedulesRows (CODE,OUTER_CODE,DATEAGR,AMOUNT,[TYPE]) select @loanid,@loanid,@ddate, @mp  ,1
    --Insert into [intermediateDb].dbo.LoansCredRepSchedulesRows (CODE,OUTER_CODE,DATEAGR,AMOUNT,[TYPE]) select @loanid,@loanid,@ddate, @intm, 2
    --end
    END
    ELSE
    BEGIN
      --select @loanid,@loanid,@ddate ,@msfe as sf,@mp as mp,@intm as int
      INSERT INTO [appbuffer].dbo.LoansCredServiceFeeSchedulesRows (CODE, OUTER_CODE, DATEAGR, AMOUNT)
        SELECT
          @loanid,
          @loanid,
          @ddate,
          @msfe
    --Insert into [intermediateDb].dbo.LoansCredRepSchedulesRows (CODE,OUTER_CODE,DATEAGR,AMOUNT,[TYPE]) select @loanid,@loanid,@ddate,@mp,1
    --Insert into [intermediateDb].dbo.LoansCredRepSchedulesRows (CODE,OUTER_CODE,DATEAGR,AMOUNT,[TYPE]) select @loanid,@loanid,@ddate,@intm,2
    END
    SET @i = @i + 1
  END


--	end