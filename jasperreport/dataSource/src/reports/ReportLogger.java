package reports;

import org.apache.log4j.Logger;

public enum ReportLogger {
    logger;

    private final String INFO_LOGGER = "reports";
    private final Logger error;
    private final Logger info;

    ReportLogger() {
        error = Logger.getRootLogger();
        info = Logger.getLogger(INFO_LOGGER);
    }

    public void error(String msg) {
        error.error(msg);
    }

    public void error(String msg, Throwable e) {
        error.error(msg, e);
    }

    public void info(String msg) {
        info.info(msg);
    }

    public void info(String msg, Throwable e) {
        info.info(msg, e);
    }
}
