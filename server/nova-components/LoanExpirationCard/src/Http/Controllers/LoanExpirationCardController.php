<?php

namespace Globalcredit\LoanExpirationCard\Http\Controllers;

use Illuminate\Routing\Controller;
use Laravel\Nova\Http\Requests\NovaRequest;

class LoanExpirationCardController extends Controller
{
    public function changeExpirationDate(NovaRequest $request)
    {
        $request->validate([
            'dates' => 'required|array',
            'days' => 'required|numeric|min:1',
        ]);

        $data = $request->only('dates', 'days');

        $expiration_days_service = resolve('Globalcredit\LoanExpirationCard\Services\LoanExpirationService');
        $expiration_days_service->updateLoanExpirationDate($data['dates'], $data['days']);

        return response()->json(true);
    }
}
