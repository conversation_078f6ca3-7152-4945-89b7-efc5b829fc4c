package reports.service.acra;

import org.json.JSONException;
import org.json.JSONObject;
import reports.ReportLogger;
import reports.helpers.DateHelper;
import reports.helpers.Helper;
import reports.helpers.MathHelper;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

import static org.apache.commons.math.util.MathUtils.round;

public class MonthlyPayment {

    double acraMonthlyPayment = 0;
    double gcMonthlyPayment = 0;

    MonthlyPayment(Map value, List<JSONObject> acraLoans) {
        Date signDate = (Date) value.get("sign_date");

        this.acraMonthlyPayment = this.monthlyTotalPayment(signDate, acraLoans);
        this.gcMonthlyPayment = this.gcLoanMonthlyTotalPayment(value);
    }

    public double getAcraMonthlyPayment() {
        return this.acraMonthlyPayment;
    }

    public double getGcMonthlyPayment() {
        return this.gcMonthlyPayment;
    }

    public String getProfitMarginRatioBySalary(double salary) {
        return this.profitMarginRatio(salary);
    }

    /**
     * յուրաքանչյուր պարտավորության գծով  ամսական հավասարաչափ վճարի հաշվարկ
     *
     * @return double
     */
    protected double monthlyTotalPayment(Date sign_date, List<JSONObject> acraLoans) {
        double totalPayment = 0;

        for (JSONObject loan : acraLoans) {
            try {
                if (loan.getString("CreditStatus").equals(AcraData.ACRA_ACTIVE_LOAN)) {
                    long months = this.getRoundUpMonths(sign_date, getDate(loan.getString("CloseDate")), getDate(loan.getString("CreditStart")));
                    double balance = loan.getDouble("Balance");
                    // Skip loans without a positive balance
                    if (balance <= 0) {
                        continue;
                    }
                    double rate = this.getInterestRate(loan.getDouble("Interest")) / 100;

                    totalPayment += this.calculateMonthlyTotalPayment(months, balance, rate);
                }
            } catch (JSONException e) {
                ReportLogger.logger.error("Monthly Total Payment JSONException", e);
            }
        }

        return round(totalPayment, 0);
    }

    protected double gcLoanMonthlyTotalPayment(Map<String, Object> gcLoan) {
        double totalPayment = 0;

        if (gcLoan.get("status").equals("CONFIRMED")) {
            int months = (int) gcLoan.get("months");
            double balance = (double) gcLoan.get("amount");
            double rate = (double) gcLoan.get("apr") / 100;

            totalPayment = this.calculateMonthlyTotalPayment(months, balance, rate);
        }

        return round(totalPayment, 0);
    }

    /**
     * յուրաքանչյուր պարտավորության գծով  ամսական հավասարաչափ վճարի բանաձև
     *
     * @param n  Loan closing and GC_loan crediting date difference
     * @param D  Loan balance
     * @param rt Loan interest or GC_loan real interest rate
     * @return
     */
    protected double calculateMonthlyTotalPayment(long n, double D, double rt) {
        return (D * rt / 12) / (1 - Math.pow((1 + rt / 12), -n));
    }

    protected int getRoundUpMonths(Date gcLoanSignDate, Date acraCloseDate, Date acraStartDate) {

        int monthsDiff = DateHelper.monthsRoundUpDiff(gcLoanSignDate, acraCloseDate);
        if (monthsDiff <= 0) {
            //If the loan is expired we prolonged it according to the crediting period
            int loanDuration = DateHelper.monthsRoundUpDiff(acraStartDate, acraCloseDate);
            LocalDate prolongedDate = acraCloseDate.toInstant().atZone(ZoneId.of(DateHelper.ARM_TIME_ZONE)).toLocalDate();
            prolongedDate = prolongedDate.plusMonths(loanDuration);

            return DateHelper.monthsRoundUpDiff(gcLoanSignDate, Date.from(prolongedDate.atStartOfDay(ZoneId.of(DateHelper.ARM_TIME_ZONE)).toInstant()));
        }

        return monthsDiff;
    }

    protected double getInterestRate(double interest_rate) {
        return interest_rate > 0 ? interest_rate : AcraData.DEFAULT_INTEREST_RATE;
    }

    /**
     * Profit Margin Ratio to string
     *
     * @param salary nork average salary
     * @return String
     */
    protected String profitMarginRatio(double salary) {
        double ratioPercentage = this.calculateProfitMarginRatio(salary);

        if (ratioPercentage > 0 && ratioPercentage <= 0.25) {
            return "0-25%";
        } else if (ratioPercentage > 0.25 && ratioPercentage <= 0.35) {
            return "26-35%";
        } else if (ratioPercentage > 0.35 && ratioPercentage <= 0.40) {
            return "36-40%";
        } else if (ratioPercentage > 0.40 && ratioPercentage <= 0.45) {
            return "41-45%";
        } else if (ratioPercentage > 0.45 && ratioPercentage <= 0.50) {
            return "46-50%";
        } else if (ratioPercentage > 0.50 && ratioPercentage <= 0.60) {
            return "51-60%";
        } else if (ratioPercentage > 0.60 && ratioPercentage <= 0.75) {
            return "61-75%";
        } else if (ratioPercentage > 0.75) {
            return "75%-ից բարձր";
        }

        return "առկա չէ";
    }

    /**
     * ՊԵՀ
     *
     * @param salary
     * @return double
     */
    protected double calculateProfitMarginRatio(double salary) {
        return MathHelper.getRatio(this.gcMonthlyPayment + this.acraMonthlyPayment, salary);
    }

    private Date getDate(String date) {
        return DateHelper.convertToDate(date, DateHelper.ACRA_DATE);
    }

    public Map<String, Object> getSalaryEstimationParams(Date birthDate, int gender) {
        List<Integer> ageRange = this.getAgeRang(DateHelper.convertToDate(birthDate, DateHelper.DATE));
        List<Double> monthlyPaymentRange = this.getMonthlyPaymentRang(this.acraMonthlyPayment + this.gcMonthlyPayment);

        Map<String, Object> params = new HashMap<>();
        params.put("gender", gender);
        params.put("age_min", ageRange.get(0));
        params.put("age_max", ageRange.get(1));
        params.put("monthly_payment_min", monthlyPaymentRange.get(0));
        params.put("monthly_payment_max", monthlyPaymentRange.get(1));

        return params;
    }

    private List<Integer> getAgeRang(Date birthDate) throws NoSuchElementException {
        Date now = new Date();
        int age = DateHelper.yearsRoundDownDiff(birthDate, now);

        return Helper.ageRangs.stream().filter(rang -> (rang.get(0) <= age && rang.get(1) >= age)).findAny().get();
    }

    private List<Double> getMonthlyPaymentRang(Double monthlyPayment) throws NoSuchElementException {
        return Helper.monthlyPaymentRangs.stream().filter(rang -> (rang.get(0) <= monthlyPayment && rang.get(1) >= monthlyPayment)).findAny().get();
    }
}
