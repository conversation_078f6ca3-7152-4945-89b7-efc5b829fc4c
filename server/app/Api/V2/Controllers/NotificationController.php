<?php

namespace App\Api\V2\Controllers;

use App\Api\V2\Requests\StoreUserDeviceRequest;
use App\Exceptions\AppDownException;
use App\Http\Controllers\Controller;
use App\Interfaces\Notifier\IInAppNotificationService;
use App\Interfaces\Notifier\IPushNotificationService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Throwable;

class NotificationController extends Controller
{
    private $push_notification_service;
    private $in_app_notification_service;

    public function __construct(IPushNotificationService $push_notification_service, IInAppNotificationService $in_app_notification_service)
    {
        $this->push_notification_service = $push_notification_service;
        $this->in_app_notification_service = $in_app_notification_service;
    }

    public function storeUserDevice(StoreUserDeviceRequest $request)
    {
        try {
            $payload = $request->only([
                'device_uuid',
                'device_token',
                'device_os',
                'device_brand',
                'device_model',
                'action',
            ]);

            $payload['device_ip'] = $request->ip();

            if (!$request->header('X-Device-ID')) {
                Log::info('Store user device, old app', ['payload' => $payload]);
                throw new AppDownException();
            }
            Log::info('Store user device', ['payload' => $payload]);

            $this->push_notification_service->storeUserDevice($payload);

            return response()->json(['success' => true]);
        } catch (Exception $e) {
            Log::error('Store user device, Exception', ['message' => $e->getMessage()]);
        }
    }

    public function getInAppNotifications()
    {
        try {
            $in_app_notifications = $this->in_app_notification_service->getAll();

            return response()->json($in_app_notifications);
        } catch (Throwable $e) {
            Log::error('Create in-app notification, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);

            throw $e;
        }
    }

    public function createInApp(Request $request)
    {
        try {
            $payload = $request->get('messages');

            Log::info('Create in-app notification', ['payload' => $payload]);

            $inserted_notifications = $this->in_app_notification_service->create($payload);

            Log::info('In-app notifications created', ['inserted_notifications' => $inserted_notifications]);

            return response()->json(['messages' => $inserted_notifications]);
        } catch (Throwable $e) {
            Log::error('Create in-app notification, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);

            throw $e;
        }
    }

    public function markAsRead(Request $request)
    {
        try {
            $all = $request->input('all', false);
            $notification_id = $request->input('notification_id');

            Log::info('Mark in-app notification as read', ['payload' => $request->all()]);

            $marked_record_ids = $this->in_app_notification_service->markAsRead($notification_id, $all);

            Log::info('In-app Notifications marked as read', ['marked_record_ids' => $marked_record_ids]);

            return response()->json(['updated' => true]);
        } catch (Throwable $e) {
            Log::error('Mark in-app notification as read, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);

            throw $e;
        }
    }
}
