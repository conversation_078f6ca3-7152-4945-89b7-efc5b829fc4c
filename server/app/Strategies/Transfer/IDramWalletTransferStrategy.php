<?php

namespace App\Strategies\Transfer;

class IDramWalletTransferStrategy extends WalletTransferStrategy
{
    public function __construct($loan, $phone_number)
    {
        parent::__construct($loan);
        $this->phone_number = $phone_number;
    }

    public function storeTransfer()
    {
        $payment_service = resolve('App\Interfaces\IPaymentService');

        return $payment_service->createIdramWalletPayment($this->loan, $this->phone_number);
    }

    public function makeTransfer()
    {
        $idram_service = resolve('App\Interfaces\IIDramService');
        $idram_service->makeTransfer($this->loan);

        parent::withdrawTransfer();

        $now = now();

        return $this->loan->payment->where('id', $this->loan->payment->id)->update([
            'paid' => $now,
            'withdrawn' => $now,
        ]);
    }
}
