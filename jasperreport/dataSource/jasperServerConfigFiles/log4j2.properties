#
# Copyright © 2005 - 2018 TIBCO Software Inc.
# http://www.jaspersoft.com.
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU Affero General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU Affero General Public License for more details.
#
# You should have received a copy of the GNU Affero General Public License
# along with this program.  If not, see <https://www.gnu.org/licenses/>.
#

# Global logging configuration

# Console output...
appender.stdout.type=Console
appender.stdout.name=stdout
appender.stdout.layout.type=PatternLayout
# Valid date format specifiers for the conversionPattern include "ABSOLUTE", "DATE" and "ISO8601"
appender.stdout.layout.pattern=%d{ISO8601} %5p %c{1},%t:%L - %m%ex%n
appender.stdout.layout.charset=UTF-8

# Rolling log file output...
# jasperserver.root is used only by JasperServer Community Edition.
# JasperServer Professional users should look for jasperserver-pro.root lower in this file.
appender.fileout.type=RollingFile
appender.fileout.name=fileout
appender.fileout.fileName=${log4j:configParentLocation}/logs/jasperserver.log
appender.fileout.filePattern=${log4j:configParentLocation}/logs/jasperserver.%i.log.gz
appender.fileout.policies.type = Policies
appender.fileout.policies.size.type = SizeBasedTriggeringPolicy
appender.fileout.policies.size.size=1024KB
appender.fileout.strategy.type = DefaultRolloverStrategy
appender.fileout.strategy.max = 1
appender.fileout.layout.type=PatternLayout
# Valid date format specifiers for the conversionPattern include "ABSOLUTE", "DATE" and "ISO8601"
appender.fileout.layout.pattern=%d{ISO8601} %5p %c{1},%t:%L - %m%ex%n
appender.fileout.layout.charset=UTF-8

# Mondrian generated SQL and MDX with timing
# jasperserver.root is used only by JasperServer Community Edition.
# JasperServer Professional users should look for jasperserver-pro.root lower in this file.
appender.jasperanalysis.type=RollingFile
appender.jasperanalysis.name=jasperanalysis
appender.jasperanalysis.fileName=${log4j:configParentLocation}/logs/jasperanalysis.log
appender.jasperanalysis.filePattern=${log4j:configParentLocation}/logs/jasperanalysis.%i.log.gz
appender.jasperanalysis.policies.type = Policies
appender.jasperanalysis.policies.size.type = SizeBasedTriggeringPolicy
appender.jasperanalysis.policies.size.size=1024KB
appender.jasperanalysis.strategy.type = DefaultRolloverStrategy
appender.jasperanalysis.strategy.max = 1
appender.jasperanalysis.layout.type=PatternLayout
# Valid date format specifiers for the conversionPattern include "ABSOLUTE", "DATE" and "ISO8601"
appender.jasperanalysis.layout.pattern=%d{ISO8601} %5p %c{1},%t:%L - %m%ex%n
appender.jasperanalysis.layout.charset=UTF-8

# JDBC Connection Logging
# logger.comjaspersoftjasperserverapienginejasperreportsserviceimplJdbcDataSourceService.name=com.jaspersoft.jasperserver.api.engine.jasperreports.service.impl.JdbcDataSourceService
# logger.comjaspersoftjasperserverapienginejasperreportsserviceimplJdbcDataSourceService.level=DEBUG
# logger.comjaspersoftjasperserverapienginejasperreportsserviceimplJndiJdbcDataSourceService.name=com.jaspersoft.jasperserver.api.engine.jasperreports.service.impl.JndiJdbcDataSourceService
# logger.comjaspersoftjasperserverapienginejasperreportsserviceimplJndiJdbcDataSourceService.level=DEBUG
# logger.comjaspersoftjasperserverwaractionReportDataSourceAction.name=com.jaspersoft.jasperserver.war.action.ReportDataSourceAction
# logger.comjaspersoftjasperserverwaractionReportDataSourceAction.level=DEBUG
# logger.comjaspersoftcommonsdataratorJdbcDataSet.name=com.jaspersoft.commons.datarator.JdbcDataSet
# logger.comjaspersoftcommonsdataratorJdbcDataSet.level=DEBUG


# logger.comjaspersoftjasperserverapimetadatacommonutilValidationUtil.name=com.jaspersoft.jasperserver.api.metadata.common.util.ValidationUtil
# logger.comjaspersoftjasperserverapimetadatacommonutilValidationUtil.level=DEBUG
# logger.comjaspersoftcommonssemanticdsimplJdbcDataSetFactory.name=com.jaspersoft.commons.semantic.dsimpl.JdbcDataSetFactory
# logger.comjaspersoftcommonssemanticdsimplJdbcDataSetFactory.level=DEBUG
# logger.comjaspersoftcommonssemanticmetaapiimpljdbcBaseJdbcMetaDataFactoryImpl.name=com.jaspersoft.commons.semantic.metaapi.impl.jdbc.BaseJdbcMetaDataFactoryImpl
# logger.comjaspersoftcommonssemanticmetaapiimpljdbcBaseJdbcMetaDataFactoryImpl.level=DEBUG
# logger.comjaspersoftjasperserverwarvalidationReportDataSourceValidator.name=com.jaspersoft.jasperserver.war.validation.ReportDataSourceValidator
# logger.comjaspersoftjasperserverwarvalidationReportDataSourceValidator.level=DEBUG

# For Apache only:  Apache Connection Pool Logging
# logger.orgapachetomcatdbcp.name=logger.org.apache.tomcat.dbcp
# logger.orgapachetomcatdbcp.level=DEBUG

# enable Mondrian MDX and SQL logging
#logger.mondrianmdx.name=mondrian.mdx
#logger.mondrianmdx.level=DEBUG
#logger.mondrianmdx.appenderRef.rolling.ref=jasperanalysis
#logger.mondriansql.name=mondrian.sql
#logger.mondriansql.level=DEBUG
#logger.mondriansql.appenderRef.rolling.ref=jasperanalysis
#logger.jasperanalysisdrillthroughSQL.name=jasperanalysis.drillthroughSQL
#logger.jasperanalysisdrillthroughSQL.level=DEBUG
#logger.jasperanalysisdrillthroughSQL.appenderRef.rolling.ref=jasperanalysis

### enable to see XMLA server messages
#logger.comtonbellerjpivotxmlaXMLA_SOAP.name=com.tonbeller.jpivot.xmla.XMLA_SOAP
#logger.comtonbellerjpivotxmlaXMLA_SOAP.level=DEBUG
#logger.com_jaspersoft_jasperserver_war_xmla_XmlaServletImpl.name=com.jaspersoft.jasperserver.war.xmla.XmlaServletImpl
#logger.com_jaspersoft_jasperserver_war_xmla_XmlaServletImpl.level=DEBUG
#logger.mondrian_xmla_XmlaServlet.name=mondrian.xmla.XmlaServlet
#logger.mondrian_xmla_XmlaServlet.level=DEBUG
#logger.mondrian_xmla_impl_DefaultXmlaServlet.name=mondrian.xmla.impl.DefaultXmlaServlet
#logger.mondrian_xmla_impl_DefaultXmlaServlet.level=DEBUG
#logger.mondrian_xmla_XmlaHandler.name=mondrian.xmla.XmlaHandler
#logger.mondrian_xmla_XmlaHandler.level=DEBUG

### jpivot loggers
#logger.com_tonbeller_jpivot_mondrian_MondrianDrillThrough.name=com.tonbeller.jpivot.mondrian.MondrianDrillThrough
#logger.com_tonbeller_jpivot_mondrian_MondrianDrillThrough.level=debug
#logger.com_tonbeller_jpivot_mondrian_MondrianModel.name=com.tonbeller.jpivot.mondrian.MondrianModel
#logger.com_tonbeller_jpivot_mondrian_MondrianModel.level=debug
#logger.com_jaspersoft_jasperserver_war_OlapPrint.name=com.jaspersoft.jasperserver.war.OlapPrint
#logger.com_jaspersoft_jasperserver_war_OlapPrint.level=debug
#logger.com_jaspersoft_jasperserver_war_PrintServlet.name=com.jaspersoft.jasperserver.war.PrintServlet
#logger.com_jaspersoft_jasperserver_war_PrintServlet.level=debug
#logger.com_jaspersoft_jasperserver_war_ChartComponent.name=com.jaspersoft.jasperserver.war.ChartComponent
#logger.com_jaspersoft_jasperserver_war_ChartComponent.level=debug
#logger.com_jaspersoft_jasperserver_war_MondrianDrillThroughTableModel.name=com.jaspersoft.jasperserver.war.MondrianDrillThroughTableModel
#logger.com_jaspersoft_jasperserver_war_MondrianDrillThroughTableModel.level=debug
#logger.com_tonbeller_jpivot_olap_query_ExpandAllExt.name=com.tonbeller.jpivot.olap.query.ExpandAllExt
#logger.com_tonbeller_jpivot_olap_query_ExpandAllExt.level=debug


### wcf loggers
#logger.com_tonbeller_wcf_controller_RequestFilter.name=com.tonbeller.wcf.controller.RequestFilter
#logger.com_tonbeller_wcf_controller_RequestFilter.level=debug

### mondrian loggers
#logger.mondriani18nLocalizingDynamicSchemaProcessor.name=mondrian.i18n.LocalizingDynamicSchemaProcessor
#logger.mondriani18nLocalizingDynamicSchemaProcessor.level=debug
#logger.mondrianrolapsqlSqlQuery.name=mondrian.rolap.sql.SqlQuery
#logger.mondrianrolapsqlSqlQuery.level=debug
#logger.mondrianrolapRolapSchema.name=mondrian.rolap.RolapSchema
#logger.mondrianrolapRolapSchema.level=debug

### JasperReports loggers
#logger.net_sf_jasperreports_engine_query_JRJdbcQueryExecuter.name=net.sf.jasperreports.engine.query.JRJdbcQueryExecuter
#logger.net_sf_jasperreports_engine_query_JRJdbcQueryExecuter.level=debug

# Suppress irrelevant warnings
logger.net_sf_ehcache_CacheManager.name=net.sf.ehcache.CacheManager
logger.net_sf_ehcache_CacheManager.level=ERROR
logger.org_hibernate_engine_StatefulPersistenceContext_ProxyWarnLog.name=org.hibernate.engine.StatefulPersistenceContext.ProxyWarnLog
logger.org_hibernate_engine_StatefulPersistenceContext_ProxyWarnLog.level=ERROR


# Logging permissions errors
#logger.com_jaspersoft_jasperserver_api_metadata_user_service_impl_ObjectPermissionServiceImpl.name=com.jaspersoft.jasperserver.api.metadata.user.service.impl.ObjectPermissionServiceImpl
#logger.com_jaspersoft_jasperserver_api_metadata_user_service_impl_ObjectPermissionServiceImpl.level=DEBUG

# Domain loggers
#logger.com_jaspersoft_commons_semantic_dsimpl_JdbcBaseDataSet.name=com.jaspersoft.commons.semantic.dsimpl.JdbcBaseDataSet
#logger.com_jaspersoft_commons_semantic_dsimpl_JdbcBaseDataSet.level=debug

# REST Loggers
#logger.com_jaspersoft_jasperserver_rest.name=com.jaspersoft.jasperserver.rest
#logger.com_jaspersoft_jasperserver_rest.level=DEBUG
#logger.com_jaspersoft_jasperserver_remote.name=com.jaspersoft.jasperserver.remote
#logger.com_jaspersoft_jasperserver_remote.level=DEBUG

# Security Logging
#logger.com_jaspersoft_jasperserver_api_security.name=com.jaspersoft.jasperserver.api.security
#logger.com_jaspersoft_jasperserver_api_security.level=INFO

### Control OWASP ESAPI logging
logger.EnterpriseSecurityException.name=EnterpriseSecurityException
logger.EnterpriseSecurityException.level=ERROR
logger.IntrusionDetector.name=IntrusionDetector
logger.IntrusionDetector.level=ERROR

### Cached engine service
#logger.com_jaspersoft_jasperserver_war_cascade_CachedEngineService.name=com.jaspersoft.jasperserver.war.cascade.CachedEngineService
#logger.com_jaspersoft_jasperserver_war_cascade_CachedEngineService.level=cDEBUG

#License Manager logger
#logger.com_jaspersoft_ji_license.name=com.jaspersoft.ji.license.name=com.jaspersoft.ji.license=com.jaspersoft.ji.license
#logger.com_jaspersoft_ji_license.name=com.jaspersoft.ji.license.level=DEBUG

### Diagnostic loggers
#logger.com_jaspersoft_jasperserver_api_logging_diagnostic_helper_DiagnosticAttributeBuilderder.name=com.jaspersoft.jasperserver.api.logging.diagnostic.helper.DiagnosticAttributeBuilderder
#logger.com_jaspersoft_jasperserver_api_logging_diagnostic_helper_DiagnosticAttributeBuilderder.level=DEBUG
#logger.com_jaspersoft_jasperserver_api_logging_diagnostic_service_impl_DiagnosticDataProvider.name=com.jaspersoft.jasperserver.api.logging.diagnostic.service.impl.DiagnosticDataProvider
#logger.com_jaspersoft_jasperserver_api_logging_diagnostic_service_impl_DiagnosticDataProvider.level=DEBUG

### Import/Export loggers
#logger.com_jaspersoft_jasperserver_jaxrs_importexport_ImportJaxrsService.name=com.jaspersoft.jasperserver.jaxrs.importexport.ImportJaxrsService
#logger.com_jaspersoft_jasperserver_api_logging_diagnostic_service_impl_DiagnosticDataProvider.level=DEBUG

# reports logger
logger.reports.name = reports
logger.reports.level = INFO

rootLogger.level = ERROR
rootLogger.appenderRef.stdout.ref = stdout
rootLogger.appenderRef.rolling.ref = fileout
