<template>
  <div
    :class="`formulate-input-element formulate-input-element--${context.class}`"
    :data-type="context.type"
  >
    <vue-numeric
      v-model="context.model"
      separator=","
      v-bind="context.attributes"
      @blur="context.blurHandler"
      :currency="context.slotProps.component.currency"
      :precision="context.slotProps.component.precision"
    />
  </div>
</template>

<script>
export default {
  props: {
    context: {
      type: Object,
      required: true,
    },
  },
};
</script>
