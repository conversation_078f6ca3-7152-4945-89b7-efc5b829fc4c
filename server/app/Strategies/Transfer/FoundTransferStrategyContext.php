<?php

namespace App\Strategies\Transfer;

class FoundTransferStrategyContext
{
    private $strategy = null;
    private $loan = null;

    public function __construct($loan, $params = null)
    {
        $this->loan = $loan;
        $card_number = $params['card_number'] ?? null;
        $year = $params['year'] ?? null;
        $month = $params['month'] ?? null;
        $phone_number = $params['phone_number'] ?? null;

        $bank_account = $params['bank_account'] ?? null;

        switch ($params['transfer_type']) {
            case constants('TRANSFER_TYPES.CASH_PAYMENT'):
                $this->strategy = new CashTransferStrategy($loan);
                break;
            case constants('TRANSFER_TYPES.IDRAM_WALLET'):
                $this->strategy = new IDramWalletTransferStrategy($loan, $phone_number);
                break;
            case constants('TRANSFER_TYPES.WALLET_LOAN'):
                $this->strategy = new WalletLoanTransferStrategy($loan);
                break;
            case constants('TRANSFER_TYPES.CARD_TO_CARD'):
                $this->strategy = new CardToCardTransferStrategy($loan, $card_number, $year, $month);
                break;
            case constants('TRANSFER_TYPES.WIRE_TRANSFER'):
                $this->strategy = new WireTransferStrategy($loan, $bank_account);
                break;
            case constants('TRANSFER_TYPES.EASYPAY_WALLET'):
                $this->strategy = new EasypayWalletTransferStrategy($loan);
                break;
            case constants('TRANSFER_TYPES.TELCELL_WALLET'):
                $this->strategy = new TelcellWalletTransferStrategy($loan, $phone_number);
                break;
            case constants('TRANSFER_TYPES.PRODUCT_PROVISION'):
                $this->strategy = new ProductProvisionStrategy($loan);
                break;
            case constants('TRANSFER_TYPES.VELOX_TRANSFER'):
                $this->strategy = new VeloxTransferStrategy($loan);
                break;
            case constants('TRANSFER_TYPES.MANUAL_TRANSFER'):
                $this->strategy = new ManualTransferStrategy($loan);
                break;
        }
    }

    public function storeTransfer()
    {
        $this->strategy->storeTransfer();
    }

    public function makeTransfer()
    {
        $this->strategy->makeTransfer();
    }

    public function withdrawTransfer($user)
    {
        return $this->strategy->withdrawTransfer($user);
    }

    public function setStatus($status)
    {
        return $this->strategy->setStatus($status);
    }

    public function getStatus()
    {
        return $this->strategy->getStatus();
    }
}
