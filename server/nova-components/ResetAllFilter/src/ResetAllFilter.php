<?php

namespace Globalcredit\ResetAllFilter;

use Illuminate\Http\Request;
use Laravel\Nova\Filters\Filter;

class ResetAllFilter extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'reset-all-filter';

    /**
     * Get the displayable name of the resource tool.
     *
     * @return string
     */
    public function name()
    {
        return __('Reset All');
    }

    /**
     * Apply the filter to the given query.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param mixed                                 $value
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(Request $request, $query, $value)
    {
        return $query;
    }

    /**
     * Get the filter's available options.
     *
     * @return array
     */
    public function options(Request $request)
    {
        return [];
    }
}
