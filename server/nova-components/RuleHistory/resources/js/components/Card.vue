<template>
  <card class="flex flex-col items-center justify-center">
    <div class="px-3 py-3">
      <h1 class="text-center text-3xl text-80 font-light">
        {{ __('Rule History') }}
      </h1>
      <div class="loading">
        <loading v-if="loading" />
      </div>
      <div class="block mx-auto button-block text-center">
        <select
          class="bg-white inline-block border"
          v-model="selected"
          @change="change()"
        >
          <option :value="0" disabled>{{ __('Select Option') }}</option>
          <option
            v-for="loanType in loanTypes"
            :key="loanType.id"
            :value="loanType.id"
          >
            {{ loanType.name }}
          </option>
        </select>
        <button
          class="btn btn-default btn-primary inline-block ml-4"
          :disabled="seedDisabled"
          @click="seed()"
        >
          {{ __('Seed') }}
        </button>
      </div>
    </div>
  </card>
</template>

<script>
export default {
  props: ['card'],

  async mounted() {
    this.loanTypes = await this.getLoanTypes();
  },

  data: () => ({
    selected: 0,
    loanTypes: [],
    seedDisabled: true,
    loading: false,
  }),

  methods: {
    async getLoanTypes() {
      try {
        const loanTypes = await Nova.request().get(
          `/nova-vendor/rule-history/loan-types`
        );

        return loanTypes.data;
      } catch (error) {
        const message =
          this.__(error.response.data.message) ||
          this.__('Something went wrong');
        this.$toasted.show(message, { type: 'error' });
      }
    },

    async change() {
      if (this.selected) {
        this.seedDisabled = false;
      }
    },

    async seed() {
      try {
        this.seedDisabled = true;
        this.loading = true;

        await Nova.request().post(
          `/nova-vendor/rule-history/seed?loan_type=${this.selected}`
        );

        this.$toasted.show(this.__('The action ran successfully!'), {
          type: 'success',
        });
        window.location.reload();
      } catch (error) {
        this.seedDisabled = false;
        this.loading = false;
        const message =
          this.__(error.response.data.message) ||
          this.__('Something went wrong');
        this.$toasted.show(message, { type: 'error' });
      }
    },
  },
};
</script>
