<?php

namespace Globalcredit\AcraMonitoring\Services;

use App\Exceptions\InvalidExcelFormatException;
use App\Factory\AcraServiceFactory;
use App\Helpers\ArrayHelper;
use App\Helpers\PassportHelper;
use App\Models\CitizenMonitoring;
use App\Models\CitizenMonitoringRequest;
use Carbon\Carbon;
use Exception;
use function Functional\first;
use function Functional\reduce_left;
use Illuminate\Support\Facades\Log;
use Rap2hpoutre\FastExcel\Facades\FastExcel;
use Throwable;

class CitizenMonitoringService
{
    protected $redis_service;

    public function __construct()
    {
        $this->redis_service = resolve('App\Interfaces\IRedisService');
    }

    public function parseCitizensExcel($file): array
    {
        $citizens = FastExcel::import($file);
        $excel_keys = array_keys($citizens[0]);

        Log::info('Parse Citizens Excels', ['excel_keys' => $excel_keys]);

        if (count(array_diff(constants('ACRA_MONITORING.EXCEL_KEYS'), $excel_keys))) {
            throw new InvalidExcelFormatException();
        }

        $file_name = now()->format(constants('FILE_MONITORING_FORMAT')).'.xlsx';
        $file->storeAs('/', $file_name, 'monitoring_uploaded_files');

        return [
            'citizens' => $citizens,
            'filename' => $file_name,
        ];
    }

    public function composeAcraCitizensList($citizens)
    {
        $acra_citizens = reduce_left($citizens, function ($acra_citizen, $key, $collection, $reduction) {
            if (!empty($acra_citizen[constants('ACRA_MONITORING.EXCEL_KEYS.CONTRACT_NUMBER')])) {
                array_push($reduction, [
                    'ContractNumber' => $acra_citizen[constants('ACRA_MONITORING.EXCEL_KEYS.CONTRACT_NUMBER')],
                    'PassportNumber' => $acra_citizen[constants('ACRA_MONITORING.EXCEL_KEYS.DOCUMENT_NUMBER')],
                    'IdCardNumber' => PassportHelper::isIdCard($acra_citizen[constants('ACRA_MONITORING.EXCEL_KEYS.DOCUMENT_NUMBER')]) ?
                        $acra_citizen[constants('ACRA_MONITORING.EXCEL_KEYS.DOCUMENT_NUMBER')] : null,
                    'SocCardNumber' => $acra_citizen[constants('ACRA_MONITORING.EXCEL_KEYS.SSN')],
                    'FirstName' => $acra_citizen[constants('ACRA_MONITORING.EXCEL_KEYS.FIRST_NAME')],
                    'LastName' => $acra_citizen[constants('ACRA_MONITORING.EXCEL_KEYS.LAST_NAME')],
                    'DateofBirth' => $this->composeDateOfBirth($acra_citizen[constants('ACRA_MONITORING.EXCEL_KEYS.BIRTH_DATE')]),
                ]);
            }

            return $reduction;
        }, []);

        Log::info('Get Citizens From List', ['citizens', $acra_citizens]);

        return $acra_citizens;
    }

    private function composeDateOfBirth($birth_date)
    {
        if (gettype($birth_date) === 'string') {
            return Carbon::parse($birth_date)->format(constants('STANDARD_DASHED_DATE_FORMAT'));
        }

        return $birth_date->format(constants('STANDARD_DASHED_DATE_FORMAT'));
    }

    public function monitoring($citizens, $report_type, $batch_id, $filename)
    {
        $monitoring_request = $this->createMonitoringRequest($citizens, $report_type, $batch_id, $filename);

        $acra_citizens = $this->callAcraMonitoring($report_type, $citizens, $monitoring_request);
        if (is_null($acra_citizens)) {
            return;
        }

        if (ArrayHelper::is_assoc($acra_citizens['PARTICIPIENT'])) {
            $acra_citizens['PARTICIPIENT'] = [$acra_citizens['PARTICIPIENT']];
        }

        foreach ($acra_citizens['PARTICIPIENT'] ?? [] as $acra_citizen) {
            try {
                if (!isset($acra_citizen['SocCardNumber']) || empty($acra_citizen['SocCardNumber'])) {
                    Log::info("Monitoring $report_type Acra Error", ['acra_citizen' => $acra_citizen]);

                    continue;
                }

                $ssn = $acra_citizen['SocCardNumber'];
                $citizen = first($citizens, function ($citizen) use ($ssn) {
                    return str_contains($ssn, $citizen['SocCardNumber']);
                });

                $result = [
                    'request_target' => constants('ACRA_LOGIN.MONITORING_VALUES.RequestTarget'),
                    'contract_number' => $citizen['ContractNumber'],
                    'ssn' => $ssn,
                    'content' => array_merge(['acra' => $acra_citizen], [
                        'timestamp' => Carbon::now()->timestamp,
                    ]),
                    'batch_id' => $batch_id,
                    'citizen_monitoring_request_id' => $monitoring_request->id,
                ];
            } catch (Exception $e) {
                Log::error("Monitoring $report_type, Exception", ['message' => $e->getMessage(), 'acra_citizen' => $acra_citizen]);
                // TODO: collect data in excel and show in case of failed monitoring
                continue;
            }

            Log::info('Citizen Monitoring Service Monitoring', ['report_type' => $report_type]);

            CitizenMonitoring::create($result);
        }
    }

    /**
     * Call ACRA monitoring service and handle errors.
     *
     * @param string                   $report_type
     * @param array                    $citizens
     * @param CitizenMonitoringRequest $monitoring_request
     */
    private function callAcraMonitoring($report_type, $citizens, $monitoring_request): ?array
    {
        try {
            $acra_monitoring_service = AcraServiceFactory::build(
                constants('ACRA.TYPE.MONITORING'),
                $report_type
            );

            $acra_citizens = $acra_monitoring_service->getCitizens($citizens);
            $monitoring_request->markAsProcessed();

            return $acra_citizens;
        } catch (Throwable $e) {
            Log::warning('Monitoring failed for citizens chunk', [
                'message' => $e->getMessage(),
                'monitoring_request_id' => $monitoring_request->id,
            ]);

            $monitoring_request->markAsFailed();

            return null;
        }
    }

    /**
     * Create a new monitoring request record.
     *
     * @param array       $citizens
     * @param string      $report_type
     * @param string      $batch_id
     * @param string|null $filename
     */
    private function createMonitoringRequest($citizens, $report_type, $batch_id, $filename): CitizenMonitoringRequest
    {
        $monitoring_request = CitizenMonitoringRequest::create([
            'chunk_payload' => $citizens,
            'batch_id' => $batch_id,
            'status' => CitizenMonitoringRequest::PENDING,
            'metadata' => [
                'filename' => $filename,
                'chunk_size' => count($citizens),
                'report_type' => $report_type,
            ],
        ]);

        Log::info('Created citizen acra monitoring request', [
            'request_id' => $monitoring_request->id,
            'batch_id' => $batch_id,
        ]);

        return $monitoring_request;
    }

    public function startMonitoring($file_name)
    {
        Log::info('Start monitoring', ['file name' => $file_name]);

        $this->redis_service->update(constants('ACRA_MONITORING.REDIS_KEY'), [
            'status' => constants('NOVA.MONITORING_STATUSES.PENDING'),
            'name' => $file_name,
            ],
            constants('ONE_MONTH_IN_MINUTES')
        );
    }

    public function stopMonitoring($status)
    {
        $monitoring = $this->getMonitoringState();

        $this->redis_service->update(constants('ACRA_MONITORING.REDIS_KEY'), array_merge($monitoring, ['status' => $status]), constants('ONE_MONTH_IN_MINUTES'));

        Log::info('Stop monitoring', ['status' => $status]);
    }

    public function getMonitoringState()
    {
        return $this->redis_service->get(constants('ACRA_MONITORING.REDIS_KEY'));
    }
}
