<?php

namespace Globalcredit\EvaluationCompanyForm\Http\Controllers;

use App\Exceptions\InternalErrorException;
use App\Factory\DocumentServiceFactory;
use App\Jobs\GenerateLawyerDocument;
use App\Jobs\GenerateRemlReportDocument;
use App\Jobs\SendConfirmationSMS;
use App\Jobs\SendLoanProcessedByECAdminSms;
use App\Jobs\SendReviewedSMS;
use App\Models\Loan;
use App\Models\PredefinedRealEstate;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Log;

class EvaluationCompanyFormController extends Controller
{
    public function checkLoan(Request $request)
    {
        try {
            $loan = $this->getLoan($request);

            $this->updateLoanPayload($loan, $request);

            $this->updateLoanStatus($loan, Loan::PROCESSED);

            $this->notifyGCSubAdmin($loan);

            return response()->json(['checked' => true]);
        } catch (Exception $e) {
            Log::error('Check Loan, Exception', ['error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function reviewLoan(Request $request)
    {
        try {
            $loan = $this->getLoan($request);
            $loan->real_estate_mortgage()->update(['gc_sub_admin_notes' => $request->get('gc_sub_admin_notes')]);
            $this->updateLoanStatus($loan, $loan::REVIEW);
            $this->notifyGCAdmin($loan);

            return response()->json(['reviewed' => true]);
        } catch (Exception $e) {
            Log::error('Review Loan, Exception', ['error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function pledgeLoan(Request $request)
    {
        try {
            $loan = $this->getLoan($request);

            $this->updateLoanPayload($loan, $request);
            $this->updateLoanStatus($loan, $loan::PLEDGED);

            $this->processHcFlow($loan);

            return response()->json(['pledge' => true]);
        } catch (Exception $e) {
            Log::error('Pledge Loan, Exception', ['error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function confirmLoan(Request $request)
    {
        try {
            $loan = $this->getLoan($request);

            $this->updateLoanStatus($loan, $loan::CONFIRMED, now());

            $this->notifyCitizen($loan);

            $this->updateApartment($loan);

            return response()->json(['confirmed' => true]);
        } catch (Exception $e) {
            Log::error('Confirm Loan, Exception', ['error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function rejectLoan(Request $request)
    {
        try {
            $loan = $this->getLoan($request);

            $this->updateLoanStatus($loan, $loan::REJECTED, now());

            return response()->json(['rejected' => true]);
        } catch (Exception $e) {
            Log::error('Reject Loan, Exception', ['error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    private function updateLoanPayload($loan, $request)
    {
        $loan->loan_security->real_estate_details()->update($this->composeEstateDetailsPayload($request));
        $loan->real_estate_mortgage()->update($this->composeMortgagePayload($request));
        $loan->real_estate_mortgage->real_estate_seller()->updateOrCreate(
            ['real_estate_mortgage_id' => $loan->real_estate_mortgage->id],
            $this->composeSellerPayload($request)
        );
    }

    private function processHcFlow($loan)
    {
        $loan_service = resolve('\App\Services\LoanServiceREML');

        $loan = $loan_service->setSignDateAndApr($loan);
        $loan = $loan_service->regenerateSchedule($loan);
        $this->regenerateLoanDocuments($loan);

        $loan_service->saveLoanToHC($loan);
    }

    private function regenerateLoanDocuments($loan)
    {
        Log::info('Start reml documents regeneration');
        $loan_document_service = DocumentServiceFactory::build($loan->loan_type_id);

        $loan_document_service->getLoanDocumentsJob($loan)->dispatch($loan)->allOnConnection('sync');
        GenerateRemlReportDocument::dispatchNow($loan);
        GenerateLawyerDocument::dispatchNow($loan);

        Log::info('Reml documents regenerated successfully');
    }

    protected function notifyGCSubAdmin($loan)
    {
        Log::info('Send loan checked by EC Admin SMS');
        SendLoanProcessedByECAdminSms::dispatch($loan);
    }

    private function notifyCitizen($loan)
    {
        Log::info('Send loan confirmed SMS');
        SendConfirmationSMS::dispatch($loan);
    }

    private function notifyGCAdmin($loan)
    {
        Log::info('Send loan reviewed SMS');
        SendReviewedSMS::dispatch($loan);
    }

    private function getLoan($request)
    {
        return Loan::find($request->loan_id);
    }

    private function updateLoanStatus($loan, $status, $now = null): void
    {
        $data = ['status' => $status];
        if (isset($now)) {
            $data['confirmed_at'] = $now;
        }

        $loan->update($data);
    }

    public function save(Request $request)
    {
        try {
            $loan = $this->getLoan($request);

            $this->updateLoanPayload($loan, $request);

            return response()->json(['saved' => true]);
        } catch (Exception $e) {
            Log::error('Save, Exception', ['error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function regenerateDocuments(Request $request)
    {
        try {
            $loan = $this->getLoan($request);

            $this->regenerateLoanDocuments($loan);

            return response()->json(['regenerated' => true]);
        } catch (Exception $e) {
            Log::error('Regenerate Documents, Exception', ['error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    private function composeEstateDetailsPayload($request)
    {
        return $request->only([
            'sreml_mortgage_contract_quantity',
            'sreml_mortgage_contract_notes',
        ]);
    }

    private function composeSellerPayload($request)
    {
        return $request->only([
            'first_name',
            'last_name',
            'middle_name',
            'phone_number',
            'birthday',
            'passport_number',
            'given_date',
            'from',
            'registration_address',
            'bank',
            'bank_account',
        ]);
    }

    private function composeMortgagePayload($request)
    {
        return $request->only([
            'evaluated_price',
            'evaluated_date',
            'subject_type',
            'subject_address',
            'subject_description',
            'ownership_cert_given_date',
            'ownership_cert_number',
            'ownership_cert_password',
            'unified_cadastral_ref_given_date',
            'unified_cadastral_ref_number',
            'unified_cadastral_ref_password',
            'ec_admin_notes',
            'gc_admin_notes',
            'gc_sub_admin_notes',
        ]);
    }

    private function updateApartment($loan)
    {
        if ($token = $loan->loan_security->re_token) {
            $luyser_service = resolve('App\Interfaces\ILuyserService');

            $real_estate = PredefinedRealEstate::whereToken($token)->first();
            $real_estate->status = PredefinedRealEstate::SOLD;
            $real_estate->save();

            $luyser_service->updateApartment(
                $real_estate->id,
                array_search(PredefinedRealEstate::SOLD, constants('DEVELOPER_COMPANIES.LUYSER.APPARTMENT_STATUSES'))
            );
        }
    }
}
