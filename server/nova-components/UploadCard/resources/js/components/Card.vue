<template>
  <div class="main-container">
    <card class="flex flex-col items-center justify-center">
      <div class="upload-section">
        <span class="form-file mr-4">
          <input
            type="file"
            id="vehicleModel"
            ref="file"
            class="form-file-input"
            v-on:change="handleFileUpload()"
          />
          <label
            for="vehicleModel"
            class="form-file-btn btn btn-default btn-primary"
          >
            {{ __('Choose File') }}
          </label>
        </span>
        <span class="file-name"> {{ fileName }} </span>
      </div>
      <loading v-if="loading" />
      <div class="upload-section">
        <button
          type="button"
          v-on:click="handleCancel()"
          class="btn text-80 font-normal h-9 px-3 mr-3 btn-link"
        >
          {{ __('Cancel') }}
        </button>
        <button
          :disabled="disabled"
          class="btn btn-default btn-primary"
          v-on:click="uploadFile()"
        >
          {{ __('Upload') }}
        </button>
        <p v-if="hasError" class="text-xs mt-2 text-danger">
          {{ firstError.code || firstError }}
        </p>
      </div>
    </card>
  </div>
</template>

<script>
import { Errors } from 'laravel-nova';

export default {
  props: ['card'],

  mounted() {
    this.fileName = this.__('no file selected');
  },

  data: () => ({
    disabled: true,
    fileName: '',
    uploadErrors: new Errors(),
    loading: false,
  }),

  methods: {
    handleFileUpload() {
      this.fileName = this.resolveFileName();
      this.files = this.$refs.file.files;
      this.uploadErrors = new Errors();
      this.disabled = false;
    },

    resolveFileName() {
      if (this.$refs.file.files.length > 1) {
        return `${this.$refs.file.files.length} files`;
      }

      return this.$refs.file.files[0].name;
    },

    handleCancel() {
      this.disabled = true;
      this.resetFileInput();
      this.uploadErrors = new Errors();
    },

    async uploadFile() {
      this.loading = true;
      this.disabled = true;

      const formData = new FormData();
      formData.append('media', this.files[0]);
      formData.append('loan_type', this.card.loanTypeId);

      try {
        await Nova.request().post(
          `/nova-vendor/upload-card/loan/models-upload`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          }
        );

        this.$toasted.show(this.__('The action ran successfully!'), {
          type: 'success',
        });

        setTimeout(() => window.location.reload(), 2000);
      } catch (error) {
        this.uploadErrors = new Errors(error.response.data.errors);
      } finally {
        this.resetFileInput();
      }
    },

    resetFileInput() {
      this.loading = false;
      this.$refs.file.value = null;
      this.fileName = this.__('no file selected');
    },
  },

  computed: {
    hasError() {
      return (
        this.uploadErrors.has('media') || this.uploadErrors.has('formatting')
      );
    },

    firstError() {
      if (this.hasError) {
        return (
          this.uploadErrors.first('media') ||
          this.uploadErrors.first('formatting')
        );
      }
    },
  },
};
</script>
