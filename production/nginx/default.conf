server {

    listen 80 default_server;
    listen [::]:80 default_server ipv6only=on;

    server_name localhost;
    root /var/www/globalcredit/server/public/;

    location /nova {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    location /api {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    location /logs {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    location /gc-view {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    location /gc-verify-email {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    location / {
        try_files $uri $uri/ /index.html;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php-fpm/php-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }

    location /.well-known/acme-challenge/ {
        root /var/www/letsencrypt/;
        log_not_found off;
    }
