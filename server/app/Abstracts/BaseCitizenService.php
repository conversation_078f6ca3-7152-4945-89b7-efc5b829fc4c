<?php

namespace App\Abstracts;

use App\Exceptions\CitizenNotFoundException;
use App\Helpers\ObjectMerger;
use App\Models\Loan;
use App\RuleEngine\InternalRuleEngine;
use App\Schemas\DataRobotSchema;
use App\Schemas\PersonalInfoSchema;
use function Functional\first;
use function Functional\reduce_left;
use Illuminate\Support\Facades\Log;

abstract class BaseCitizenService
{
    protected $aggregator_service;
    protected $ekeng_service;

    public function __construct()
    {
        $this->aggregator_service = resolve('App\Interfaces\IAggregatorService');
        $this->ekeng_service = resolve('App\Interfaces\IEkengService');
    }

    abstract protected function calculateCredit($citizen, $calculate_allowance = null);

    public function getCitizen($payload, $calculate_allowance = null)
    {
        $security_service = resolve('App\Interfaces\ISecurityService');

        $citizen = $this->collectCitizenData($payload);

        $security_service->recordMetaOnce($citizen, constants('META_STEPS.CITIZEN_INFO'));

        $citizen = $this->collectDataRobotScore($citizen);

        return $this->prepareCitizen($citizen, $payload, $calculate_allowance);
    }

    public function collectDataRobotScore($citizen)
    {
        return $citizen;
    }

    public function getPredictionValues($loan_security, $loan_type, $is_new_customer, $amount, $citizen)
    {
        $this->storeDataRobotData($citizen);

        $data_robot_service = resolve('App\Interfaces\IDataRobotService');

        $prediction = $data_robot_service->getPrediction($loan_security->id, [
            'type' => $loan_type,
            'new_customer' => $is_new_customer,
            'amount' => $amount,
        ]);

        return $prediction['data'][0]['predictionValues'] ?? [];
    }

    public function getCitizenDrScore($prediction_values)
    {
        return reduce_left($prediction_values, function ($prediction_value, $index, $collection, $reduction) {
            //We get value from data whose label is "label":1.0
            if ($prediction_value['label'] == 1) {
                $reduction = round($prediction_value['value'], AbstractDataRobotService::PREDICTION_VALUE_PRECISION);
            }

            return $reduction;
        }, AbstractDataRobotService::INITIAL_PREDICTION_VALUE);
    }

    public function collectCitizenData($payload)
    {
        [
            'document_number' => $document_number,
        ] = $payload;

        return $this->aggregateCitizenData($document_number, []);
    }

    protected function aggregateCitizenData($document_number)
    {
        $citizen['ekeng'] = $this->aggregator_service->getEkengData($document_number);
        $citizen['nork'] = $this->aggregator_service->getNorkData($citizen['ekeng']['passport_data']);

        $citizen = $this->ekeng_service->cutEkengPhoto($citizen);

        $citizen['loanTotal'] = $this->getLoanTotal($citizen['ekeng']['SSN']);

        return $citizen;
    }

    protected function storeDataRobotData($citizen)
    {
        $security_service = resolve('App\Interfaces\ISecurityService');
        $dr_data = $this->prepareDataRobotMetaInfo([], $citizen);
        $security_service->recordMetaOnce($dr_data, constants('META_STEPS.DATA_ROBOT_INFO'));
    }

    protected function prepareDataRobotMetaInfo($data, $citizen)
    {
        Log::debug('Preparing DR info meta');
        $schema = new DataRobotSchema();

        $data_robot_schema = $schema->get();
        Log::debug('Merge data by data robot schema');
        $object_merger = new ObjectMerger($data_robot_schema);
        $meta = $object_merger->merge($citizen);

        Log::debug('Merged data', ['meta' => $meta]);

        return array_merge($data, $meta);
    }

    protected function getLoanTotal($ssn)
    {
        return $this->getExistingFicoScores($ssn);
    }

    protected function getExistingFicoScores($ssn)
    {
        $loans = Loan::getLoanBySSN($ssn);

        $fico_scores = $loans->filter(function ($item) {
            return $item->fico_score != constants('N/A_FICO');
        })
            ->pluck('fico_score')
            ->toArray();

        return ['existing_fico_scores' => $fico_scores];
    }

    public function prepareCitizen($citizen, $payload, $calculate_allowance)
    {
        $credit = $this->prepareCredit($citizen, $calculate_allowance);

        return $this->preparePersonalInfo($citizen, $credit, $payload['document_number']);
    }

    protected function prepareCredit($citizen, $calculate_allowance)
    {
        return ['credit' => $this->calculateCredit($citizen, $calculate_allowance)];
    }

    protected function isCreditAvailable($credit)
    {
        return $credit['amount'] != 0.0 && $credit['duration'] != 0.0 && $this->sufficientScore($credit['score']);
    }

    public function preparePersonalInfo($citizen, $credit, $document_number)
    {
        Log::debug('Preparing personal info');
        $schema = new PersonalInfoSchema();

        $personal_info_schema = $schema->get();
        Log::debug('Merge data by personal info schema');
        $objectMerger = new ObjectMerger($personal_info_schema);
        $personal_info = $objectMerger->merge($citizen);
        Log::debug('Merged data', ['citizen_context' => $personal_info]);

        $is_valid_document = $this->isValidDocument($document_number, $personal_info['passports']);

        if (!$is_valid_document) {
            Log::error('Preparing personal info, CitizenNotFoundException');
            throw new CitizenNotFoundException();
        }

        $personal_info = $this->setYerevanRegion($personal_info);

        $personal_info = $this->makeDocumentPrimary($document_number, $personal_info);
        $prepared = array_merge($personal_info, $credit);
        Log::debug('Personal info prepared', ['citizen_context' => $prepared]);

        return $prepared;
    }

    protected function makeDocumentPrimary($document_number, $personal_info)
    {
        foreach ($personal_info['passports'] as $key => $document) {
            if ($document['passport_number'] == strtoupper($document_number)) {
                $personal_info['passports'][$key]['primary'] = true;
            }
        }

        return $personal_info;
    }

    protected function isValidDocument($document_number, $passports)
    {
        return first($passports, function ($document) use ($document_number) {
            if (strtoupper($document_number) === $document['passport_number']) {
                return $document;
            }
        });
    }

    protected function sufficientScore($score)
    {
        return $score === null || $score >= constants('SCORE_LIMIT');
    }

    public function checkInternalCredits($ssn, $loan_type_id, $document_numbers)
    {
        $warehouse_service = resolve('App\Interfaces\IWarehouseService');
        $security_service = resolve('App\Interfaces\ISecurityService');

        $internal_credits = collect($warehouse_service->getLoansBySsn($ssn, true));

        Log::info('Fetched citizen internal credits', ['ssn' => $ssn, 'loan_type_id' => $loan_type_id, 'internal_credits' => $internal_credits]);

        $loan_security_id = optional($security_service->tryResolveLoanSecurity())->id;

        $details = [
            'ssn' => $ssn,
            'document_numbers' => (array) $document_numbers, // If document_numbers is not an array, cast it to an array
            'loan_type_id' => $loan_type_id,
            'loan_security_id' => $loan_security_id,
        ];
        $this->processCheckInternalCredits($internal_credits, $details);

        Log::info('Citizen internal credits checks passed successfully', ['ssn' => $ssn, 'loan_type_id' => $loan_type_id]);
    }

    public function processCheckInternalCredits($credits, $details = [])
    {
        $internal_rule_engine = new InternalRuleEngine($credits, $details);

        return $internal_rule_engine
            ->checkIsHcBlacklisted()
            ->checkExistingOverdueCredit()
            ->checkGuaranteeOverdueDays()
            ->checkInvalidHCLoanClass()
            ->executeResult();
    }

    protected function setYerevanRegion($payload)
    {
        // In case of ԵՐԵՎԱՆ we got "Region = ԵՐԵՎԱՆ" and e.g. "Community = ՇԵՆԳԱՎԻԹ"
        // But with other regions we got "Region = RegionName", "Community = CityName"
        // So in case of ԵՐԵՎԱՆ we check "Region" and replace "city"
        if ($payload['region'] == 'ԵՐԵՎԱՆ') {
            $payload['city'] = 'ԵՐԵՎԱՆ';
        }

        return $payload;
    }

    protected function getAcraBusinessData($ekeng_data, $is_monitoring = false)
    {
        if ($is_monitoring) {
            return $this->aggregator_service->getAcraBusinessMonitoringData($ekeng_data, constants('ACRA_REPORT_TYPE.CREDIT_WITH_BUSINESS'));
        }

        return $this->aggregator_service->getAcraData($ekeng_data, constants('ACRA_REPORT_TYPE.CREDIT_WITH_BUSINESS'));
    }

    protected function getAcraData($ekeng_data, $report_type = null, $is_monitoring = false)
    {
        $ssn = $ekeng_data['passport_data']->PNum;

        if ($is_monitoring) {
            Log::info('Get ACRA data called for monitoring.', ['ssn' => $ssn]);

            return $this->aggregator_service->getAcraMonitoringData($ekeng_data, $report_type);
        }

        Log::info('Get ACRA data called', ['ssn' => $ssn]);
        $warehouse_service = resolve('App\Interfaces\IWarehouseService');
        $is_eligible_for_acra_monitoring = $warehouse_service->isEligibleForAcraMonitoring($ssn);
        Log::info('Is eligible for ACRA monitoring', ['ssn' => $ssn, 'is_eligible_for_acra_monitoring' => $is_eligible_for_acra_monitoring]);

        if (!$is_eligible_for_acra_monitoring) {
            Log::info('Not eligible for ACRA monitoring, calling Acra Data', ['ssn' => $ssn]);

            return $this->aggregator_service->getAcraData($ekeng_data, $report_type);
        }

        // TODO: This is a temporary solution. We should remove it later.
        $count = $this->getAcraMonitoringCount();
        Log::info('ACRA monitoring count', ['ssn' => $ssn, 'count' => $count]);

        if ($count > env('ACRA_MONITORING_DAILY_SWITCH_LIMIT', 300)) {
            Log::info('ACRA monitoring count exceeded 300, calling Acra Data.', ['ssn' => $ssn]);

            return $this->aggregator_service->getAcraData($ekeng_data, $report_type);
        }

        Log::info('Calling Acra Monitoring Data', ['ssn' => $ssn]);

        $acra_monitoring_data = $this->aggregator_service->getAcraMonitoringData($ekeng_data, $report_type);
        // Increment ACRA monitoring daily switch count only for non-cached responses
        if (isset($acra_monitoring_data['cached']) && !$acra_monitoring_data['cached']) {
            Log::info('Called Acra Monitoring Data', ['ssn' => $ssn]);

            $this->incrementAcraMonitoringCount();
        }

        return $acra_monitoring_data;
    }

    private function incrementAcraMonitoringCount(): void
    {
        $redis_service = resolve('App\Interfaces\IRedisService');
        $date = date(constants('STANDARD_DATE_FORMAT'));
        $redis_key = 'acra_monitoring_count_'.$date;

        $ttl = (int) ceil((strtotime('tomorrow') - time()) / 60);
        $count = $redis_service->get($redis_key);
        Log::info('Redis GET key (for increment)', ['key' => $redis_key, 'count' => $count ?? 'null']);

        $count = (int) $count + 1;
        $redis_service->update($redis_key, $count, $ttl);
        Log::info('Redis UPDATE key', ['key' => $redis_key, 'new_count' => $count, 'ttl' => $ttl]);
    }

    private function getAcraMonitoringCount()
    {
        $redis_service = resolve('App\Interfaces\IRedisService');
        $date = date('Y-m-d');
        $redis_key = 'acra_monitoring_count_'.$date;
        $count = $redis_service->get($redis_key);

        return (int) $count ?: 0;
    }
}
