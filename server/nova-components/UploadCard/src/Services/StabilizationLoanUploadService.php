<?php

namespace Globalcredit\UploadCard\Services;

use App\Models\Stabilization\Loan as StabilizationLoan;
use Carbon\Carbon;
use Exception;
use FastExcel;
use Log;

class StabilizationLoanUploadService implements IUploadCardService
{
    public function storeModels($file)
    {
        $now = Carbon::now();
        $file_name = "stabilization_loan_$now.".$file->extension();

        $file->storeAs('/', $file_name, 'stabilization_loan_upload');

        $this->insertStabilizationLoans("stabilization_loans/$file_name");
    }

    protected function insertStabilizationLoans($file)
    {
        $loans = FastExcel::import($file)->toArray();

        foreach ($loans as $loan) {
            try {
                StabilizationLoan::create($loan);
            } catch (Exception $e) {
                Log::info('Insert Support Loan Exception', ['loan' => $loan]);
            }
        }
    }
}
