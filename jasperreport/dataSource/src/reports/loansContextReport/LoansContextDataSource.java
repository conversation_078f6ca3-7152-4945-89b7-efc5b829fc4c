package reports.loansContextReport;

import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JRField;

import reports.ReportLogger;
import reports.db.Database;
import reports.exception.ReportEmptyException;
import reports.helpers.DateHelper;
import reports.helpers.Helper;
import reports.loansContextReport.loanContextRepository.LoanContextRepository;
import reports.service.acra.AcraService;
import reports.service.acra.MonthlyPayment;
import reports.service.ekeng.EkengService;
import reports.service.nork.NorkService;
import reports.service.nork.WorkData;

import java.sql.Connection;
import java.text.ParseException;
import java.util.*;

public class LoansContextDataSource implements JRDataSource {

    private Database db;
    private Map<Integer, Map<String, Object>> report;
    private Object[] reportKeySet;
    private Integer reportLoanCount = -1;
    private Integer reportLoanMaxCount = 0;

    public LoansContextDataSource() {
        this.db = new Database();
    }

    public void composeLoansContextReport(Map parameterValues) {
        try (Connection conn = db.getConnection()) {
            LoanContextRepository loanContextRepository = new LoanContextRepository(conn);

            composeLoanSecuritiesData(loanContextRepository, parameterValues);
            composeLoanSecuritiesContextData(loanContextRepository, parameterValues);
            composeLoanCitizenInfoData(loanContextRepository, parameterValues);
        } catch (ReportEmptyException e) {
            this.setReportEmpty();
            ReportLogger.logger.error("Loans Context Data Source ReportEmptyException", e);
        } catch (Exception e) {
            ReportLogger.logger.error("Loans Context Data Source Exception", e);
        }
    }

    private void composeLoanSecuritiesData(LoanContextRepository loanContextRepository, Map parameterValues) throws ReportEmptyException {
        Map<Integer, Map<String, Object>> loanSecurities = loanContextRepository.getLoanSecuritiesByParameters(parameterValues);
        if (loanSecurities.size() == 0) {
            ReportLogger.logger.error("Compose Loan Securities Data ReportEmptyException");
            throw new ReportEmptyException();
        }

        for (Map.Entry<Integer, Map<String, Object>> entry : loanSecurities.entrySet()) {
            Integer loanSecurityId = entry.getKey();
            Map value = entry.getValue();
            Map loanSecurity = loanSecurities.get(loanSecurityId);

            loanSecurity.put("created_at", DateHelper.formatDateStringByTimeZone(value.get("created_at"), DateHelper.DATE_TIME));
            loanSecurity.put("birth_date", DateHelper.formatDateString(value.get("birth_date"), DateHelper.DATE));
            loanSecurity.put("gender", Helper.genderTypeMap.get(value.get("gender")));

            loanSecurities.put(loanSecurityId, loanSecurity);
        }

        putToReport(loanSecurities);
    }

    private void composeLoanSecuritiesContextData(LoanContextRepository loanContextRepository, Map parameterValues) throws ReportEmptyException {
        Map<Integer, Map<String, Object>> loanSecurities = loanContextRepository.getLoanSecuritiesContextByParameters(parameterValues);
        if (loanSecurities == null) {
            ReportLogger.logger.error("Compose Loan Securities Context Data ReportEmptyException");
            throw new ReportEmptyException();
        }

        for (Map.Entry<Integer, Map<String, Object>> entry : loanSecurities.entrySet()) {
            Integer loanSecurityId = entry.getKey();
            Map value = entry.getValue();
            Map loanSecurity = loanSecurities.get(loanSecurityId);

            loanSecurity.put("has_long_history", value.get("has_long_history") != null);
            loanSecurity.put("is_blocked", value.get("is_blocked") != null);
            loanSecurity.put("has_credit", value.get("has_credit") != null);

            loanSecurities.put(loanSecurityId, loanSecurity);
        }

        putToReport(loanSecurities);
    }

    private void composeLoanCitizenInfoData(LoanContextRepository loanContextRepository, Map parameterValues) throws ReportEmptyException, ParseException {
        Map<Integer, Map<String, Object>> loanSecurities = loanContextRepository.getLoanCitizenInfoByParameters(parameterValues);

        for (Map.Entry<Integer, Map<String, Object>> entry : loanSecurities.entrySet()) {
            Integer loanSecurityId = entry.getKey();
            Map value = entry.getValue();
            Map loanSecurity = loanSecurities.get(loanSecurityId);

            try {
                EkengService ekengService = new EkengService((String) value.get("ekeng"));
                AcraService acraService = new AcraService((String) value.get("acra_loans"), (String) value.get("acra_participent"), (String) value.get("acra_guarantees"));
                NorkService norkService = new NorkService((String) value.get("work_data"));

                Map<String, WorkData> workPlace = norkService.getWorkPlaces();
                WorkData current = workPlace.get("current");
                loanSecurity.put("highest_salary_hvvh", current.getHvhh());
                loanSecurity.put("highest_salary_workName", current.getWorkName());
                loanSecurity.put("work_entry_date", DateHelper.formatDateString(current.getPajmanData(), DateHelper.DATE));
                loanSecurity.put("highest_salary", current.getSalary());

                WorkData penult = workPlace.get("penult");
                loanSecurity.put("penult_work_entry_date", DateHelper.formatDateString(penult.getPajmanData(), DateHelper.DATE));
                loanSecurity.put("penult_work_expiry_date", DateHelper.formatDateString(penult.getExpiryDate(), DateHelper.DATE));

                loanSecurity.put("work_experience", norkService.getWorkExperience(
                        DateHelper.convertToDate(loanSecurity.get("loan_securities_created_at"), DateHelper.NORK_DATE))
                );
                loanSecurity.put("workplaces_count", norkService.getCurrentWorkPlacesCount());

                loanSecurity.put("merital_status", Helper.maritalStatus.get(ekengService.getMaritalStatus()));
                loanSecurity.put("vehicles_count", ekengService.getVehicleCount());

                loanSecurity.put("acra_start_date", acraService.acraHistoryStartDate());
                loanSecurity.put("acra_active_loans_count", acraService.activeLoansCount());
                loanSecurity.put("acra_loans_count", acraService.loansCount());
                loanSecurity.put("request_quantity", acraService.requestQuantity());
                loanSecurity.put("acra_overdue_loans_count", acraService.overdueLoansCount());
                loanSecurity.put("acra_biggest_amount", acraService.loanBiggestAmount());
                loanSecurity.put("acra_have_real_estate", acraService.haveRealEstate());
                loanSecurity.put("acra_have_mortgage", acraService.haveMortgage());
                loanSecurity.put("acra_delay_payment_in_year", acraService.delayPaymentQuantityInYear());
                loanSecurity.put("acra_average_expired_days_in_year", acraService.averageExpiredDaysInYear());
                loanSecurity.put("acra_worst_class_loan_12", acraService.worstClassLoanInYear());
                loanSecurity.put("acra_worst_class_guarantee_12", acraService.worstClassGuaranteeInYear());
                loanSecurity.put("acra_non_pledge_and_total_ratio", acraService.nonPledgeAndTotalBalanceRatio());

                // Calculate field only if loan CONFIRMED
                if (value.get("gc_loan_status").equals("CONFIRMED")) {
                    Map<String, Object> gcLoan = this.getGcLoan(value);
                    MonthlyPayment monthlyPayment = acraService.monthlyPayment(gcLoan);

                    Double acraMonthlyTotal = monthlyPayment.getAcraMonthlyPayment();
                    loanSecurity.put("acra_monthly_total_payment", acraMonthlyTotal);

                    Double gcLoanMonthlyTotal = monthlyPayment.getGcMonthlyPayment();
                    loanSecurity.put("gc_loan_monthly_total_payment", gcLoanMonthlyTotal);

                    Double salary = (Double) loanSecurity.get("citizen_salary");
                    loanSecurity.put("profit_margin_ratio", monthlyPayment.getProfitMarginRatioBySalary(salary));

                    try {
                        Map<String, Object> incomeParameters = monthlyPayment.getSalaryEstimationParams((Date) value.get("citizen_birth_date"), (int) value.get("citizen_gender"));
                        ArrayList<Map<String, Object>> estimatedSalaryList = loanContextRepository.getCitizenEstimatedIncomeByParameters(incomeParameters);
                        Double estimatedSalary = (Double) estimatedSalaryList.get(0).get("estimated_salary");
                        loanSecurity.put("estimated_salary", estimatedSalary);
                        loanSecurity.put("profit_margin_ratio_by_estimated_salary", monthlyPayment.getProfitMarginRatioBySalary(estimatedSalary));
                    } catch (Exception e) {
                        ReportLogger.logger.error("Profit Margin Ratio By Estimated Salary Exception".concat(loanSecurityId.toString()), e);
                    }
                }
            } catch (Exception e) {
                ReportLogger.logger.error("composeLoanCitizenInfoData", e);
            }
            loanSecurities.put(loanSecurityId, loanSecurity);
            this.cleanUpUnnecessaryKeys(loanSecurity, Arrays.asList("ekeng", "work_data", "acra_loans", "acra_participent", "acra_guarantees"));
        }

        putToReport(loanSecurities);
    }

    private Map<String, Object> getGcLoan(Map value) {
        Map<String, Object> gcLoan = new HashMap<String, Object>();
        gcLoan.put("amount", value.get("gc_loan_amount"));
        gcLoan.put("status", value.get("gc_loan_status"));
        gcLoan.put("months", value.get("gc_loan_months"));
        gcLoan.put("apr", value.get("gc_loan_apr"));
        String gcLoanSignDate = DateHelper.formatDateStringByTimeZone(value.get("sign_date"), DateHelper.ACRA_DATE);
        gcLoan.put("sign_date", DateHelper.convertToDate(gcLoanSignDate, DateHelper.ACRA_DATE));

        return gcLoan;
    }

    /**
     * Removing values which unnecessary in jrxml
     *
     * @param map
     * @param keyList
     */
    private void cleanUpUnnecessaryKeys(Map map, List keyList) {
        map.keySet().removeAll(keyList);
    }

    private void putToReport(Map<Integer, Map<String, Object>> report) {
        if (this.report == null) {
            this.report = report;
        } else {
            this.report.forEach((key, value) -> report.merge(key, value, (v1, v2) -> {
                v2.putAll(v1);
                return v2;
            }));
        }

        this.reportLoanMaxCount = this.report.size();
        this.reportKeySet = this.report.keySet().toArray();
    }

    private void setReportEmpty() {
        this.reportLoanMaxCount = 0;
    }

    /**
     * Getting count of report_row
     */
    public boolean next() throws JRException {
        return (++reportLoanCount < reportLoanMaxCount);
    }

    /**
     * Getting values from data map by key
     */
    public Object getFieldValue(JRField field) throws JRException {
        try {
            return this.report.get(this.reportKeySet[reportLoanCount]).get(field.getName());
        } catch (NullPointerException e) {
            return " ";
        }
    }
}
