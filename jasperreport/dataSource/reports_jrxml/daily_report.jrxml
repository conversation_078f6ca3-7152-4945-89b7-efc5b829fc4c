<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.11.0.final using JasperReports Library version 6.11.0-0c4056ccaa4d25a5a8c45672d2f764ea3498bebb  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="daily_report" pageWidth="2250" pageHeight="4052" orientation="Landscape" columnWidth="2210" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="36b8676a-16a7-4d6f-b11a-7960231b6fd0">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="Sample DB"/>
	<property name="ireport.jasperserver.url" value="http://192.168.11.229:8080/jasperserver/"/>
	<property name="ireport.jasperserver.user" value="jasperadmin"/>
	<property name="ireport.jasperserver.reportUnit" value="/Reports/Report/daily_report"/>
	<property name="net.sf.jasperreports.export.xls.break.before.row" value="true"/>
	<property name="net.sf.jasperreports.export.xls.break.after.row" value="true"/>
	<property name="net.sf.jasperreports.export.xls.one.page.per.sheet" value="true"/>
	<property name="net.sf.jasperreports.export.xls.ignore.graphics" value="false"/>
	<property name="net.sf.jasperreports.page.break.no.pagination" value="apply"/>
	<property name="ireport.jasperserver.report.resource" value="/Reports/Report/daily_report_files/main_jrxml"/>
	<propertyExpression name="net.sf.jasperreports.export.xls.sheet.names.sheet1"><![CDATA["Միասնական"]]></propertyExpression>
	<propertyExpression name="net.sf.jasperreports.export.xls.sheet.names.sheet2"><![CDATA["Միասնական ըստ տոկոսի"]]></propertyExpression>
	<propertyExpression name="net.sf.jasperreports.export.xls.sheet.names.sheet3"><![CDATA["Միասնական ըստ քանակի"]]></propertyExpression>
	<propertyExpression name="net.sf.jasperreports.export.xls.sheet.names.sheet4"><![CDATA["Միասնական ըստ գումարի"]]></propertyExpression>
	<propertyExpression name="net.sf.jasperreports.export.xls.sheet.names.sheet5"><![CDATA["Միասնական Դիմում"]]></propertyExpression>
	<propertyExpression name="net.sf.jasperreports.export.xls.sheet.names.sheet6"><![CDATA["Իդրամ"]]></propertyExpression>
	<propertyExpression name="net.sf.jasperreports.export.xls.sheet.names.sheet7"><![CDATA["Իդրամ ըստ գումարի"]]></propertyExpression>
	<propertyExpression name="net.sf.jasperreports.export.xls.sheet.names.sheet8"><![CDATA["Իդրամ ըստ քանակի"]]></propertyExpression>
	<propertyExpression name="net.sf.jasperreports.export.xls.sheet.names.sheet9"><![CDATA["TelCell"]]></propertyExpression>
	<propertyExpression name="net.sf.jasperreports.export.xls.sheet.names.sheet10"><![CDATA["TelCell ըստ գումարի"]]></propertyExpression>
	<propertyExpression name="net.sf.jasperreports.export.xls.sheet.names.sheet11"><![CDATA["TelCell ըստ քանակի"]]></propertyExpression>
	<propertyExpression name="net.sf.jasperreports.export.xls.sheet.names.sheet12"><![CDATA["Easy Pay"]]></propertyExpression>
	<propertyExpression name="net.sf.jasperreports.export.xls.sheet.names.sheet13"><![CDATA["Easy Pay ըստ գումարի"]]></propertyExpression>
	<propertyExpression name="net.sf.jasperreports.export.xls.sheet.names.sheet14"><![CDATA["Easy Pay ըստ քանակի"]]></propertyExpression>
	<propertyExpression name="net.sf.jasperreports.export.xls.sheet.names.sheet15"><![CDATA["Ըստ Վճարման"]]></propertyExpression>
	<queryString language="SQL">
		<![CDATA[SELECT date_trunc('day', lm.created_at) as day,
  count(rejected_ocl.id) AS rejected_ocl , sum(rejected_ocl.amount) as rejected_amount_ocl,
  count(confirmed_ocl.id) AS confirmed_ocl, sum(confirmed_ocl.amount) as confirmed_amount_ocl,
  count(confirmed_ovl.id) AS confirmed_ovl, sum(confirmed_ovl.amount) as confirmed_amount_ovl,
  count(processing_ovl.id) AS processing_ovl, sum(processing_ovl.amount) as processing_amount_ovl,
  count(processed_ovl.id) AS processed_ovl, sum(processed_ovl.amount) as processed_amount_ovl,
  count(rejected_ovl.id) AS rejected_ovl, sum(rejected_ovl.amount) as rejected_amount_ovl,
  count(all_ocl.id) as all_ocl,
  count(all_ovl.id) as all_ovl,
  count(pending_ocl.id) as pending_ocl,
  count(pending_ovl.id) as pending_ovl,
  count(fail_ocl.id) as fail_ocl,
  count(distinct applied.ssn) as applied,
  count(identified.id) as identified,
  case 
  	when count(confirmed_ocl.id) + count(rejected_ocl.id) = 0 then 0 
  	else count(confirmed_ocl.id) * 100/(count(confirmed_ocl.id) + count(rejected_ocl.id))
  end as confirmed_ocl_all_ratio,
  case 
  	when count(confirmed_ocl.id) + count(confirmed_ovl.id) = 0 then 0 
  	else (((count(confirmed_ocl.id) + (count(confirmed_ovl.id)))) * 100) / count(identified.id)
  end as confirmd_identified_ratio
FROM loan_securities as lm
left outer join loans as rejected_ocl on rejected_ocl.id=lm.loan_id and rejected_ocl.status = 'REJECTED' and rejected_ocl.loan_type_id = 1
left outer join loans as all_ocl on all_ocl.id=lm.loan_id and all_ocl.loan_type_id = 1
left outer join loans as all_ovl on all_ovl.id=lm.loan_id and all_ovl.loan_type_id = 2
left outer join loans as confirmed_ocl on confirmed_ocl.id=lm.loan_id and confirmed_ocl.status = 'CONFIRMED' and confirmed_ocl.loan_type_id = 1
left outer join loans as confirmed_ovl on confirmed_ovl.id=lm.loan_id and (confirmed_ovl.status = 'CONFIRMED' or confirmed_ovl.status = 'PLEDGED') and confirmed_ovl.loan_type_id = 2
left outer join loans as pending_ocl on pending_ocl.id=lm.loan_id and (pending_ocl.status = 'PENDING' or pending_ocl.status = 'FINISHED') and pending_ocl.loan_type_id = 1
left outer join loans as pending_ovl on pending_ovl.id=lm.loan_id and (pending_ovl.status = 'PENDING') and pending_ovl.loan_type_id = 2
left outer join loans as processing_ovl on processing_ovl.id=lm.loan_id and (processing_ovl.status = 'PROCESSING') and processing_ovl.loan_type_id = 2
left outer join loans as processed_ovl on processed_ovl.id=lm.loan_id and (processed_ovl.status = 'PROCESSED') and processed_ovl.loan_type_id = 2
left outer join loans as rejected_ovl on rejected_ovl.id=lm.loan_id and (rejected_ovl.status = 'REJECTED') and rejected_ovl.loan_type_id = 2
left outer join loans as fail_ocl on fail_ocl.id=lm.loan_id and (fail_ocl.status = 'FAILED') and fail_ocl.loan_type_id = 1
left outer join loan_securities as applied on applied.id = lm.id and (applied.loan_type_id = 1 or applied.loan_type_id = 2 or applied.loan_type_id = 3)
left outer join 
(select distinct loan_securities.id from loan_securities 
 inner join citizen_face_recognitions as cfr 
 on loan_securities.id = cfr.loan_security_id
 where (loan_securities.loan_type_id = 1 or loan_securities.loan_type_id = 2 or loan_securities.loan_type_id = 3) and cfr.status='MATCHED'
 ) as identified on identified.id = lm.id
WHERE lm.created_at > (current_date - interval '30' day)
AND lm.created_at < (current_date - interval '1' day + time '23:59')
GROUP BY 1
ORDER BY 1;]]>
	</queryString>
	<field name="rejected_ocl" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="rejected"/>
	</field>
	<field name="day" class="java.sql.Date">
		<property name="com.jaspersoft.studio.field.label" value="day"/>
	</field>
	<field name="rejected_amount_ocl" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="rejected_amount"/>
	</field>
	<field name="confirmed_ocl" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="confirmed"/>
	</field>
	<field name="confirmed_amount_ocl" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="confirmed_amount"/>
	</field>
	<field name="all_ocl" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="all"/>
	</field>
	<field name="pending_ocl" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="pending"/>
	</field>
	<field name="fail_ocl" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="payment_fails"/>
	</field>
	<field name="confirmed_ocl_all_ratio" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="percentage"/>
	</field>
	<field name="applied" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="rejected"/>
	</field>
	<field name="identified" class="java.lang.Long"/>
	<field name="confirmed_ovl" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="confirmed"/>
	</field>
	<field name="confirmed_amount_ovl" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="confirmed_amount"/>
	</field>
	<field name="pending_ovl" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="pending"/>
	</field>
	<field name="all_ovl" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="all"/>
	</field>
	<field name="confirmd_identified_ratio" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="percentage"/>
	</field>
	<field name="processing_ovl" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="confirmed"/>
	</field>
	<field name="processing_amount_ovl" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="confirmed_amount"/>
	</field>
	<field name="processed_ovl" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="confirmed"/>
	</field>
	<field name="processed_amount_ovl" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="confirmed_amount"/>
	</field>
	<field name="rejected_ovl" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="confirmed"/>
	</field>
	<field name="rejected_amount_ovl" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="confirmed_amount"/>
	</field>
	<group name="day">
		<groupExpression><![CDATA[$F{day}]]></groupExpression>
	</group>
	<group name="rejected">
		<groupExpression><![CDATA[$F{rejected_ocl}]]></groupExpression>
	</group>
	<group name="amount">
		<groupExpression><![CDATA[$F{rejected_amount_ocl}]]></groupExpression>
	</group>
	<group name="confirmed">
		<groupExpression><![CDATA[$F{confirmed_ocl}]]></groupExpression>
	</group>
	<group name="confirmed_amount">
		<groupExpression><![CDATA[$F{confirmed_amount_ocl}]]></groupExpression>
	</group>
	<group name="all">
		<groupExpression><![CDATA[$F{all_ocl}]]></groupExpression>
	</group>
	<group name="pending">
		<groupExpression><![CDATA[$F{pending_ocl}]]></groupExpression>
	</group>
	<group name="payment_fails">
		<groupExpression><![CDATA[$F{fail_ocl}]]></groupExpression>
	</group>
	<group name="percentage">
		<groupExpression><![CDATA[$F{confirmed_ocl_all_ratio}]]></groupExpression>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="55">
			<textField>
				<reportElement x="0" y="0" width="100" height="30" uuid="6fe84f31-f611-411c-a467-4b3eede29237"/>
				<textElement textAlignment="Center">
					<font size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["CashMe"]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="55">
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.spreadsheet.SpreadsheetLayout"/>
			<staticText>
				<reportElement x="0" y="0" width="100" height="55" uuid="59e02e07-9495-478c-a846-41d3153970ac">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="28d6305a-1489-49c3-b598-7518143b23d3"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Ամսաթիվ]]></text>
			</staticText>
			<staticText>
				<reportElement x="100" y="0" width="100" height="55" uuid="aa19346e-82c1-4767-a65f-a33935991426">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="30f7b931-3430-43f4-ad1c-0c3891c3a1a0"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Հաստատված: Ավտոգրավ]]></text>
			</staticText>
			<staticText>
				<reportElement x="200" y="0" width="100" height="55" uuid="7db3b78f-b18e-4aca-b010-0afcdc2de78c">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="ed7c23a1-d48a-49b1-aca6-00d332a030ec"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Հաստատված Գումար։ Ավտոգրավ]]></text>
			</staticText>
			<staticText>
				<reportElement x="300" y="0" width="100" height="55" uuid="098ed98f-69f5-42a6-a623-9ee51a4dc0e3">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="300cc5c8-16dd-4ce1-8ea9-1904ba0f32bd"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="SansSerif" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Մերժված։ Ավտոգրավ]]></text>
			</staticText>
			<staticText>
				<reportElement x="400" y="0" width="100" height="55" uuid="0fe7dbda-29a5-49a8-a2b7-35056a201b15">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="c4a0429a-f247-478a-8129-************"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="SansSerif" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Մերժված Գումար։ Ավտոգրավ]]></text>
			</staticText>
			<staticText>
				<reportElement x="500" y="0" width="100" height="55" uuid="5e7e0a56-3c9c-494f-9fbf-f607cd048638">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="885a66fc-6170-4775-aaaf-b9b1f7cfb1fd"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="SansSerif" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Պատրաստ Դիտարկման։ Ավտոգրավ]]></text>
			</staticText>
			<staticText>
				<reportElement x="600" y="0" width="100" height="55" uuid="f3721ad1-2503-4610-bc6d-f2a0d728669a">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="65a4ffc0-2c92-42d5-9195-ef701746d6cf"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="SansSerif" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Պատրաստ Դիտարկման Գումար։ Ավտոգրավ]]></text>
			</staticText>
			<staticText>
				<reportElement x="700" y="0" width="100" height="55" uuid="24dd0176-71cb-494a-9d79-fcf9596cbe43">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="dbc2b382-4446-427f-9fa2-b38dab0680c9"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="SansSerif" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Դիտարկված։Ավտոգրավ]]></text>
			</staticText>
			<staticText>
				<reportElement x="800" y="0" width="100" height="55" uuid="20c8fb0e-b57d-45c3-bb74-a182033e5a83">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="8ab5afe2-fe4c-45b7-8d1d-b0c6b164bc37"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="SansSerif" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Դիտարկված Գումար։Ավտոգրավ]]></text>
			</staticText>
			<staticText>
				<reportElement x="900" y="0" width="100" height="55" uuid="869f7d13-9114-4861-843f-a88aa3e5999b">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="bf6a7c09-4b78-4027-9017-27f86b769604"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="SansSerif" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Հրաժարվել են։ Ավտոգրավ]]></text>
			</staticText>
			<staticText>
				<reportElement x="1000" y="0" width="100" height="55" uuid="66d90e11-b86d-4d34-874a-3b6b45b1e1ec">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="6c77079c-454a-41c2-8740-24254fda402b"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="SansSerif" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Բոլորը։
Ավտոգրավ]]></text>
			</staticText>
			<staticText>
				<reportElement x="1100" y="0" width="100" height="55" uuid="44e4baa7-4f0a-4b78-8245-f8cc416ef150">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="0dd7f8aa-7ef4-4291-90c4-f30a8ce24d0c"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Հաստատված: Անգրավ]]></text>
			</staticText>
			<staticText>
				<reportElement x="1200" y="0" width="100" height="55" uuid="c3d0bbe5-3b53-468e-8911-b8d630d55f58">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="7fe83e0d-9573-4e72-b432-53d08ff872c3"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Հաստատված Գումար։ Անգրավ]]></text>
			</staticText>
			<staticText>
				<reportElement x="1300" y="0" width="100" height="55" uuid="a5be760a-c258-4a5c-b852-034cad67ccc6">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="c1204464-db3e-4228-bcd7-f5e8422369ae"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Մերժված որպես կասկածելի: Անգրավ]]></text>
			</staticText>
			<staticText>
				<reportElement x="1400" y="0" width="100" height="55" uuid="030ef56e-e9f6-4c34-b4fd-1989e57264bc">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="d902bf9b-dc77-4b20-b922-3bc9978cf3ea"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Մերժված Գումար։ Անգրավ]]></text>
			</staticText>
			<staticText>
				<reportElement x="1500" y="0" width="100" height="55" uuid="386237a6-b929-41ab-b500-9d44edb7421f">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="bd7e4c68-e5b2-4c4f-a5b9-fca1091f60dd"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Հրաժարվել են։ Անգրավ]]></text>
			</staticText>
			<staticText>
				<reportElement x="1600" y="0" width="100" height="55" uuid="dae04796-e7f9-4049-a903-d40e432c0e56">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="2c8a8c76-9088-46b5-b292-6e068af27852"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Փոխանցման Ձախողում։ Անգրավ]]></text>
			</staticText>
			<staticText>
				<reportElement x="1700" y="0" width="100" height="55" uuid="99aa44e1-a74f-4a35-ba50-3516eeb82466">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="72147912-f62b-40e5-a49b-ec90828551b0"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Բոլորը։
Անգրավ]]></text>
			</staticText>
			<staticText>
				<reportElement x="1800" y="0" width="100" height="55" uuid="76e7306a-49f3-46bc-93cc-a6c38952d3d0">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4bdaec5d-0049-4077-944d-a2be61b4c509"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Տոկոս]]></text>
			</staticText>
			<staticText>
				<reportElement x="1900" y="0" width="100" height="55" uuid="865cd65d-d239-4001-8eb6-f33158619ede">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="187c1ff5-3acb-4213-9910-510df62b415f"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Acceptance rate]]></text>
			</staticText>
			<staticText>
				<reportElement x="2000" y="0" width="100" height="55" uuid="98d6ea8d-77be-43b9-975e-78c7b10e5b76">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="e9e6eeac-8773-4c92-902c-058714890df8"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Դիմումներ]]></text>
			</staticText>
			<staticText>
				<reportElement x="2100" y="0" width="100" height="55" uuid="a9612d8b-808c-42e2-b433-03786096d6ea">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="18d877c7-c773-4db5-93b9-331da5502284"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Վարկային Դիմումներ]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="67">
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.spreadsheet.SpreadsheetLayout"/>
			<textField pattern="d/MM/yyyy h:mm a" isBlankWhenNull="true">
				<reportElement x="0" y="0" width="100" height="67" uuid="9ba3bc8f-b0ce-4d6a-9e5d-816f35586b48">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="28d6305a-1489-49c3-b598-7518143b23d3"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{day}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="100" y="0" width="100" height="67" uuid="c8ed8fb0-55c9-42b8-8fcd-3c9735441210">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="30f7b931-3430-43f4-ad1c-0c3891c3a1a0"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{confirmed_ovl}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="200" y="0" width="100" height="67" uuid="e77919f5-11dc-486f-b73e-ad6357645a05">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="ed7c23a1-d48a-49b1-aca6-00d332a030ec"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{confirmed_amount_ovl}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="300" y="0" width="100" height="67" uuid="23d6ab30-338b-40cf-b36b-100c1560d9d6">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="300cc5c8-16dd-4ce1-8ea9-1904ba0f32bd"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{rejected_ovl}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="400" y="0" width="100" height="67" uuid="59cd1611-37aa-411a-99f7-b76687873570">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="c4a0429a-f247-478a-8129-************"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{rejected_amount_ovl}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="500" y="0" width="100" height="67" uuid="d3f11c18-d893-4f0f-942c-4d5420bf962d">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="885a66fc-6170-4775-aaaf-b9b1f7cfb1fd"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{processing_ovl}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="600" y="0" width="100" height="67" uuid="850e48d3-cb29-4968-a01b-bf8752d7cd34">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="65a4ffc0-2c92-42d5-9195-ef701746d6cf"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{processing_amount_ovl}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="700" y="0" width="100" height="67" uuid="579a5e8f-4aee-4ea7-a085-31f0b20a0674">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="dbc2b382-4446-427f-9fa2-b38dab0680c9"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{processed_ovl}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="800" y="0" width="100" height="67" uuid="0b25de49-746d-4823-adf8-5ad1137ac7d5">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="8ab5afe2-fe4c-45b7-8d1d-b0c6b164bc37"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{processed_amount_ovl}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="900" y="0" width="100" height="67" uuid="3bef5b86-a695-4149-9f9f-a613b69ce444">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="bf6a7c09-4b78-4027-9017-27f86b769604"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{pending_ovl}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1000" y="0" width="100" height="67" uuid="f9f7ff1e-0974-49cc-b755-5a0eb7f61a29">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="6c77079c-454a-41c2-8740-24254fda402b"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{all_ovl}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1100" y="0" width="100" height="67" uuid="dd35dde0-d783-4875-8620-3bcb07d77b0c">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="0dd7f8aa-7ef4-4291-90c4-f30a8ce24d0c"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{confirmed_ocl}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1200" y="0" width="100" height="67" uuid="c5e42ede-070e-4c7e-931e-d87dcb52e527">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="7fe83e0d-9573-4e72-b432-53d08ff872c3"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{confirmed_amount_ocl}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1300" y="0" width="100" height="67" uuid="5b1439b6-fc33-4ea6-b6cf-0cc5fdc2595e">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="c1204464-db3e-4228-bcd7-f5e8422369ae"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{rejected_ocl}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1400" y="0" width="100" height="67" uuid="0ef7d65d-b9de-47d0-ad41-c2e72ced2813">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="d902bf9b-dc77-4b20-b922-3bc9978cf3ea"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{rejected_amount_ocl}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1500" y="0" width="100" height="67" uuid="139f540c-cf0b-462d-8219-41d9fe6e5bea">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="bd7e4c68-e5b2-4c4f-a5b9-fca1091f60dd"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{pending_ocl}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1600" y="0" width="100" height="67" uuid="55fbdb19-4e9c-4e52-b0d0-62aa1b1a2806">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="2c8a8c76-9088-46b5-b292-6e068af27852"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{fail_ocl}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1700" y="0" width="100" height="67" uuid="fd839301-4597-4657-88bf-4de5bf146fd5">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="72147912-f62b-40e5-a49b-ec90828551b0"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{all_ocl}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1800" y="0" width="100" height="67" uuid="296c87e8-8bfd-4786-aa51-cd657773e302">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4bdaec5d-0049-4077-944d-a2be61b4c509"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{confirmed_ocl_all_ratio}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1900" y="0" width="100" height="67" uuid="e40598b7-9a85-4c2f-ad37-afc8d396f001">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="187c1ff5-3acb-4213-9910-510df62b415f"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{confirmd_identified_ratio}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2000" y="0" width="100" height="67" uuid="214af4c6-8340-48d4-b206-21b866837257">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="e9e6eeac-8773-4c92-902c-058714890df8"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{applied}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2100" y="0" width="100" height="67" uuid="d92ffead-d890-4761-a9b7-9ffb240682e2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="18d877c7-c773-4db5-93b9-331da5502284"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{identified}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="2248">
			<property name="com.jaspersoft.studio.layout"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<lineChart>
				<chart evaluationTime="Report" renderType="draw">
					<reportElement x="10" y="20" width="1270" height="400" isPrintInFirstWholeBand="true" uuid="749b8e46-bb77-4a2e-8cee-e6fae772c0e7">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<chartTitle>
						<titleExpression><![CDATA["Ըստ տոկոսի"]]></titleExpression>
					</chartTitle>
					<chartSubtitle color="#030202"/>
					<chartLegend/>
				</chart>
				<categoryDataset>
					<dataset resetType="Report"/>
					<categorySeries>
						<seriesExpression><![CDATA["տոկոս"]]></seriesExpression>
						<categoryExpression><![CDATA[$F{day}]]></categoryExpression>
						<valueExpression><![CDATA[$F{confirmed_ocl_all_ratio}]]></valueExpression>
					</categorySeries>
				</categoryDataset>
				<linePlot>
					<plot labelRotation="341.0"/>
					<categoryAxisFormat labelRotation="341.0">
						<axisFormat labelColor="#000000" tickLabelColor="#0D0C0C" axisLineColor="#000000">
							<labelFont>
								<font size="10"/>
							</labelFont>
							<tickLabelFont>
								<font size="8"/>
							</tickLabelFont>
						</axisFormat>
					</categoryAxisFormat>
					<valueAxisFormat>
						<axisFormat labelColor="#000000" tickLabelColor="#000000" axisLineColor="#000000"/>
					</valueAxisFormat>
				</linePlot>
			</lineChart>
			<lineChart>
				<chart evaluationTime="Report">
					<reportElement x="-1" y="860" width="1281" height="400" uuid="4021f58a-b419-4b62-a22d-52d9836aa737">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<chartTitle>
						<titleExpression><![CDATA["Ըստ գումարի"]]></titleExpression>
					</chartTitle>
					<chartSubtitle/>
					<chartLegend/>
				</chart>
				<categoryDataset>
					<dataset resetType="Report"/>
					<categorySeries>
						<seriesExpression><![CDATA["Մերժված"]]></seriesExpression>
						<categoryExpression><![CDATA[$F{day}]]></categoryExpression>
						<valueExpression><![CDATA[$F{rejected_amount_ocl}]]></valueExpression>
					</categorySeries>
					<categorySeries>
						<seriesExpression><![CDATA["Հաստատված"]]></seriesExpression>
						<categoryExpression><![CDATA[$F{day}]]></categoryExpression>
						<valueExpression><![CDATA[$F{confirmed_amount_ocl}]]></valueExpression>
					</categorySeries>
				</categoryDataset>
				<linePlot>
					<plot labelRotation="341.0"/>
					<categoryAxisFormat labelRotation="341.0">
						<axisFormat labelColor="#000000" tickLabelColor="#000000" verticalTickLabels="false" axisLineColor="#000000">
							<tickLabelFont>
								<font size="8"/>
							</tickLabelFont>
						</axisFormat>
					</categoryAxisFormat>
					<valueAxisFormat>
						<axisFormat labelColor="#000000" tickLabelColor="#000000" axisLineColor="#000000"/>
					</valueAxisFormat>
				</linePlot>
			</lineChart>
			<lineChart>
				<chart evaluationTime="Report">
					<reportElement x="10" y="440" width="1270" height="400" isPrintInFirstWholeBand="true" uuid="439cb5ce-45d8-4acc-9ba4-8d9690cee437">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<chartTitle>
						<titleExpression><![CDATA["Ըստ քանակի"]]></titleExpression>
					</chartTitle>
					<chartSubtitle/>
					<chartLegend/>
				</chart>
				<categoryDataset>
					<dataset resetType="Report"/>
					<categorySeries>
						<seriesExpression><![CDATA["Հաստատված"]]></seriesExpression>
						<categoryExpression><![CDATA[$F{day}]]></categoryExpression>
						<valueExpression><![CDATA[$F{confirmed_ocl}]]></valueExpression>
					</categorySeries>
					<categorySeries>
						<seriesExpression><![CDATA["Հրաժարվել են"]]></seriesExpression>
						<categoryExpression><![CDATA[$F{day}]]></categoryExpression>
						<valueExpression><![CDATA[$F{pending_ocl}]]></valueExpression>
					</categorySeries>
					<categorySeries>
						<seriesExpression><![CDATA["Փոխանցումը ձախողվել է"]]></seriesExpression>
						<categoryExpression><![CDATA[$F{day}]]></categoryExpression>
						<valueExpression><![CDATA[$F{fail_ocl}]]></valueExpression>
					</categorySeries>
					<categorySeries>
						<seriesExpression><![CDATA["Բոլորը"]]></seriesExpression>
						<categoryExpression><![CDATA[$F{day}]]></categoryExpression>
						<valueExpression><![CDATA[$F{all_ocl}]]></valueExpression>
					</categorySeries>
					<categorySeries>
						<seriesExpression><![CDATA["Մերժված որպես կասկածելի"]]></seriesExpression>
						<categoryExpression><![CDATA[$F{day}]]></categoryExpression>
						<valueExpression><![CDATA[$F{rejected_ocl}]]></valueExpression>
					</categorySeries>
				</categoryDataset>
				<linePlot>
					<plot labelRotation="341.0"/>
					<categoryAxisFormat labelRotation="341.0">
						<axisFormat labelColor="#000000" tickLabelColor="#000000" axisLineColor="#000000">
							<tickLabelFont>
								<font size="8"/>
							</tickLabelFont>
						</axisFormat>
					</categoryAxisFormat>
					<valueAxisFormat>
						<axisFormat labelColor="#000000" tickLabelColor="#000000" axisLineColor="#000000"/>
					</valueAxisFormat>
				</linePlot>
			</lineChart>
			<break>
				<reportElement x="0" y="430" width="735" height="1" uuid="eea04526-4948-4bb7-b1f3-f2049853f6f1"/>
			</break>
			<break>
				<reportElement x="0" y="850" width="695" height="1" uuid="a87bb451-4a96-4649-aa6d-abe03aa5e225"/>
			</break>
			<break>
				<reportElement x="0" y="10" width="746" height="1" uuid="c41ef5e3-fc65-45e3-a797-9892e45b5b8a"/>
			</break>
			<break>
				<reportElement x="0" y="1280" width="808" height="1" uuid="5fb3ebcc-3899-484f-ba4c-7e69b17b1a38"/>
			</break>
			<lineChart>
				<chart evaluationTime="Report">
					<reportElement x="0" y="1310" width="1281" height="400" uuid="1f1eef2e-4e86-4da2-80b3-98c52eec854d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<chartTitle>
						<titleExpression><![CDATA["Ըստ դիմումների"]]></titleExpression>
					</chartTitle>
					<chartSubtitle/>
					<chartLegend/>
				</chart>
				<categoryDataset>
					<dataset resetType="Report"/>
					<categorySeries>
						<seriesExpression><![CDATA["Դիմում"]]></seriesExpression>
						<categoryExpression><![CDATA[$F{day}]]></categoryExpression>
						<valueExpression><![CDATA[$F{applied}]]></valueExpression>
					</categorySeries>
				</categoryDataset>
				<linePlot>
					<plot labelRotation="341.0"/>
					<categoryAxisFormat labelRotation="341.0">
						<axisFormat labelColor="#000000" tickLabelColor="#000000" axisLineColor="#000000">
							<tickLabelFont>
								<font size="8"/>
							</tickLabelFont>
						</axisFormat>
					</categoryAxisFormat>
					<valueAxisFormat>
						<axisFormat labelColor="#000000" tickLabelColor="#000000" axisLineColor="#000000"/>
					</valueAxisFormat>
				</linePlot>
			</lineChart>
			<break>
				<reportElement x="0" y="1730" width="878" height="1" uuid="ab328121-593c-469c-96df-f4307238c4aa"/>
			</break>
			<frame>
				<reportElement x="35" y="1741" width="815" height="49" uuid="f996ca46-cc1e-47d8-b0df-b7f2cf1cd8b5"/>
				<subreport overflowType="Stretch">
					<reportElement positionType="Float" x="54" y="9" width="696" height="21" uuid="cc09a33b-5a46-4d9e-8528-457199fc2cf6"/>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA["idram_daily_report.jasper"]]></subreportExpression>
				</subreport>
				<break>
					<reportElement x="0" y="0" width="648" height="1" uuid="ef512658-1e35-4de3-a0bd-b77a0b5ee93d"/>
				</break>
			</frame>
			<frame>
				<reportElement positionType="Float" x="0" y="1790" width="877" height="50" uuid="75461d82-5938-4ed3-8c56-f88c66e0a1df"/>
				<subreport isUsingCache="false" runToBottom="false" overflowType="Stretch">
					<reportElement positionType="Float" x="10" y="20" width="419" height="30" uuid="b424e0cc-cc6b-439c-8212-7023d61a2da3">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA["telcell_daily_report.jasper"]]></subreportExpression>
				</subreport>
			</frame>
			<frame>
				<reportElement positionType="Float" x="-1" y="1910" width="877" height="50" uuid="ecc0e613-c19d-43c9-98b4-654f8050bf52"/>
				<break>
					<reportElement x="0" y="10" width="544" height="1" uuid="d6a287c2-ab48-4034-a48e-bec067dc9d9c"/>
				</break>
				<subreport>
					<reportElement x="10" y="20" width="407" height="36" uuid="c4c436c0-dd69-4279-80dc-f8f1fc8f10c4"/>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA["easy_daily_report.jasper"]]></subreportExpression>
				</subreport>
			</frame>
			<break>
				<reportElement x="0" y="10" width="410" height="1" uuid="f787917f-dd46-4627-83a9-7217c6b1bf07"/>
			</break>
			<frame>
				<reportElement positionType="Float" x="0" y="2050" width="876" height="67" uuid="e99ea5c9-01fc-4405-9dff-184605916918"/>
				<break>
					<reportElement x="0" y="18" width="807" height="1" uuid="664954e8-e358-4726-bd32-2b3e53c505e3"/>
				</break>
				<subreport>
					<reportElement x="6" y="20" width="334" height="49" uuid="aa7008d7-c245-4504-9b26-3572ae320471"/>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA["loan_payment_report.jasper"]]></subreportExpression>
				</subreport>
			</frame>
		</band>
	</summary>
</jasperReport>
