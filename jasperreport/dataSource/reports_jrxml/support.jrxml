<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.11.0.final using JasperReports Library version 6.11.0-0c4056ccaa4d25a5a8c45672d2f764ea3498bebb  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="DataSourceReport" pageWidth="595" pageHeight="842" columnWidth="515" leftMargin="40" rightMargin="40" topMargin="50" bottomMargin="50" uuid="030574cb-2d4b-4281-8294-0f87619f0d7f">
	<property name="net.sf.jasperreports.print.create.bookmarks" value="true"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="datasource\DataSource1.xml"/>
	<property name="ireport.jasperserver.url" value="http://192.168.11.229:8080/jasperserver/"/>
	<property name="ireport.jasperserver.user" value="jasperadmin"/>
	<property name="ireport.jasperserver.reportUnit" value="/Reports/Աջակցություն"/>
	<property name="ireport.jasperserver.report.resource" value="/Reports/Աջակցություն_files/main_jrxml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="Sans_Normal" isDefault="true" fontName="DejaVu Sans" fontSize="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false"/>
	<style name="Sans_Bold" fontName="DejaVu Sans" fontSize="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false"/>
	<style name="Sans_Italic" fontName="DejaVu Sans" fontSize="12" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false"/>
	<subDataset name="Dataset1" uuid="b0d17981-8a8e-42b1-b1df-2b406b6c79fb">
		<queryString>
			<![CDATA[]]>
		</queryString>
		<field name="Field_2" class="java.lang.String"/>
		<field name="Field_1" class="java.lang.String"/>
	</subDataset>
	<parameter name="contract_number" class="java.lang.String">
		<defaultValueExpression><![CDATA["CV19-MA010"]]></defaultValueExpression>
	</parameter>
	<parameter name="connection_host" class="java.lang.String">
		<defaultValueExpression><![CDATA["COVID_"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="first_name" class="java.lang.String"/>
	<field name="last_name" class="java.lang.String"/>
	<field name="address" class="java.lang.String"/>
	<field name="email" class="java.lang.String"/>
	<field name="work_data" class="java.lang.String"/>
	<field name="phone_number" class="java.lang.String"/>
	<field name="middle_name" class="java.lang.String"/>
	<field name="passport_number" class="java.lang.String"/>
	<field name="given_date" class="java.lang.String"/>
	<field name="expire_date" class="java.lang.String"/>
	<field name="ssn" class="java.lang.String"/>
	<field name="birth_date" class="java.lang.String"/>
	<field name="amount" class="java.lang.String"/>
	<field name="sign_date" class="java.lang.String"/>
	<field name="spouse_first_name" class="java.lang.String"/>
	<field name="spouse_last_name" class="java.lang.String"/>
	<field name="spouse_middle_name" class="java.lang.String"/>
	<field name="recording_date" class="java.lang.String"/>
	<field name="vin" class="java.lang.String"/>
	<field name="vehicle_number" class="java.lang.String"/>
	<field name="owner_cert_id" class="java.lang.String"/>
	<field name="vehicle_model" class="java.lang.String"/>
	<field name="payment_type" class="java.lang.String"/>
	<field name="mortgage_contract_number" class="java.lang.String"/>
	<field name="personal_sheet" class="java.lang.String"/>
	<field name="contract_ocl" class="java.lang.String"/>
	<field name="lawyer" class="java.lang.String"/>
	<field name="application" class="java.lang.String"/>
	<field name="mortgage_contract" class="java.lang.String"/>
	<field name="contract_oiql" class="java.lang.String"/>
	<field name="contract_ovl" class="java.lang.String"/>
	<field name="arbitration" class="java.lang.String"/>
	<field name="statement" class="java.lang.String"/>
	<field name="agreement" class="java.lang.String"/>
	<field name="power_of_attorney" class="java.lang.String"/>
	<field name="optimuslex" class="java.lang.String"/>
	<field name="payment_id" class="java.lang.String"/>
	<field name="cash_payment_type" class="java.lang.String"/>
	<field name="withdrawn" class="java.lang.String"/>
	<field name="withdrawn_user" class="java.lang.String"/>
	<field name="spouse" class="java.lang.String"/>
	<field name="withdraw_check" class="java.lang.String"/>
	<field name="contract" class="java.lang.String"/>
	<field name="image_path" class="java.lang.String"/>
	<field name="gnm_arbitration" class="java.lang.String"/>
	<field name="uba_arbitration" class="java.lang.String"/>
	<field name="loan_type_id" class="java.lang.String"/>
	<field name="vendor_file" class="java.lang.String"/>
	<field name="contract_oidl" class="java.lang.String"/>
	<field name="personal_sheet_oidl" class="java.lang.String"/>
	<group name="CityGroup" minHeightToStartNewPage="6">
		<groupExpression><![CDATA[$F{email}]]></groupExpression>
	</group>
	<detail>
		<band height="649">
			<property name="com.jaspersoft.studio.layout"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<textField textAdjust="StretchHeight">
				<reportElement positionType="Float" x="0" y="263" width="510" height="20" forecolor="#4A4848" uuid="aba73180-34ae-4277-8ca8-70bd90821144">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textFieldExpression><![CDATA[$F{email}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="183" width="510" height="20" forecolor="#4A4848" uuid="f3f2f70e-3138-4834-840c-a87253f1dc30">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textFieldExpression><![CDATA[$F{address}]]></textFieldExpression>
			</textField>
			<textField bookmarkLevel="2">
				<reportElement x="0" y="223" width="510" height="20" forecolor="#4A4848" uuid="bb195136-2738-4182-a1b0-00d1ca640bdd">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left"/>
				<textFieldExpression><![CDATA[$F{phone_number}]]></textFieldExpression>
				<anchorNameExpression><![CDATA[$F{last_name} + " (" + $F{first_name} + ")"]]></anchorNameExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement positionType="Float" x="0" y="20" width="510" height="20" forecolor="#4A4848" uuid="b98b57f4-ebed-4a29-b69f-b243445bff2d">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textFieldExpression><![CDATA[$F{first_name}+"   "+ $F{last_name}+ "  " + $F{middle_name}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement positionType="Float" x="0" y="63" width="510" height="20" forecolor="#4A4848" uuid="f9b61ca8-f032-49a9-bb49-b73af6721833">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textFieldExpression><![CDATA[$F{passport_number} +"   Տրված: "+$F{given_date} + "  Մինչև: " + $F{expire_date}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="103" width="510" height="20" forecolor="#4A4848" uuid="4d985779-b5d8-4fd3-88aa-837a10cd111b">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textFieldExpression><![CDATA[$F{ssn}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="143" width="510" height="20" forecolor="#4A4848" uuid="52c98de5-33da-4688-abc5-6b28522a73d2">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textFieldExpression><![CDATA[$F{birth_date}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement positionType="Float" x="1" y="380" width="510" height="20" forecolor="#4A4848" uuid="5d3e0045-dd1c-4fa5-a911-1aa45b7bcb3f">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textFieldExpression><![CDATA[$F{amount}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement positionType="Float" x="1" y="420" width="510" height="20" forecolor="#4A4848" uuid="fd25abae-f103-4975-9c19-bd777135e4ab">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textFieldExpression><![CDATA[$F{sign_date}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement positionType="Float" x="1" y="460" width="510" height="20" forecolor="#4A4848" uuid="a5132605-500f-4708-a543-19c9ef8df075">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textFieldExpression><![CDATA[$F{payment_type}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement positionType="Float" x="305" y="-20" width="205" height="15" uuid="69ff2a45-5e56-4511-a8cc-5a61ea4dc004">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["Պայմանագիր։ " +  $P{contract_number}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement positionType="Float" mode="Opaque" x="0" y="0" width="512" height="16" forecolor="#0D0C0C" backcolor="#CFCACA" uuid="3ffb6ca1-7e35-419d-957c-ef2e8504179b">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Անուն Ազգանուն Հայրանուն"]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement style="Sans_Italic" positionType="Float" mode="Opaque" x="0" y="43" width="510" height="16" forecolor="#0D0C0C" backcolor="#CFCACA" uuid="193d6a1c-fa22-4e84-9269-816beed698e2">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Անձնագիր"]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement positionType="Float" mode="Opaque" x="0" y="83" width="510" height="16" forecolor="#0D0C0C" backcolor="#CFCACA" uuid="a4f62aa7-be69-4e2d-8a4b-43c65235c847">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA["Սոց․ Քարտ"]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement positionType="Float" mode="Opaque" x="0" y="123" width="510" height="16" forecolor="#0D0C0C" backcolor="#CFCACA" uuid="8ffc2758-b347-4a18-acfd-4d3661292c46"/>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA["Ծնված"]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement positionType="Float" mode="Opaque" x="0" y="163" width="510" height="16" forecolor="#0D0C0C" backcolor="#CFCACA" uuid="083715ef-b82f-4c8c-941e-27a5acfa3a04"/>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA["Հասցե"]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement positionType="Float" mode="Opaque" x="0" y="203" width="510" height="16" forecolor="#0D0C0C" backcolor="#CFCACA" uuid="5cc252b0-7078-4cd2-a7ed-e5f0297280d9"/>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA["Հեռ․ համար"]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement positionType="Float" mode="Opaque" x="0" y="243" width="510" height="16" forecolor="#0D0C0C" backcolor="#CFCACA" uuid="a3545bc5-adc7-4088-9ae1-a6896415e883"/>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA["Էլ․ հասցե"]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement positionType="Float" mode="Opaque" x="1" y="360" width="510" height="16" forecolor="#0D0C0C" backcolor="#CFCACA" uuid="d9708bbc-039c-44a3-85df-7e4c33047c41"/>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA["Վարկի գումար"]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement positionType="Float" mode="Opaque" x="1" y="400" width="510" height="16" forecolor="#0D0C0C" backcolor="#CFCACA" uuid="d9d938db-6d45-464d-8a83-8e7054feaa99"/>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA["Կնքված"]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement positionType="Float" mode="Opaque" x="1" y="440" width="510" height="16" forecolor="#0D0C0C" backcolor="#CFCACA" uuid="79d39d87-4535-4b47-8c52-be762015b359"/>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA["Վճարման եղանակ"]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement positionType="Float" mode="Opaque" x="1" y="522" width="510" height="16" isRemoveLineWhenBlank="true" forecolor="#0D0C0C" backcolor="#CFCACA" uuid="4830eb37-440e-48ba-8ec6-d5c833ffe1f5">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA["Վճարված"]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement positionType="Float" x="1" y="584" width="510" height="20" forecolor="#4A4848" uuid="b81eeacd-3788-4aab-9dcb-adff0032e220">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA[$F{payment_type} == "Կանխիկ"]]></printWhenExpression>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textFieldExpression><![CDATA[$F{cash_payment_type}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement positionType="Float" mode="Opaque" x="1" y="565" width="510" height="16" isRemoveLineWhenBlank="true" forecolor="#0D0C0C" backcolor="#CFCACA" uuid="1407c3f7-8efd-40cd-8c82-1a5709385b8b">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<printWhenExpression><![CDATA[$F{payment_type} == "Կանխիկ"]]></printWhenExpression>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA["Վճարման գրասենյակ"]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement positionType="Float" x="3" y="543" width="510" height="20" forecolor="#4A4848" uuid="dc867bcc-c617-4141-8dac-5f69fbaec1d9">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<printWhenExpression><![CDATA[$F{withdrawn} != null]]></printWhenExpression>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textFieldExpression><![CDATA[$F{withdrawn}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement positionType="Float" mode="Opaque" x="1" y="607" width="510" height="16" isRemoveLineWhenBlank="true" forecolor="#0D0C0C" backcolor="#CFCACA" uuid="d7c3c3a9-2909-4fc0-af9b-20d880c680f3">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<printWhenExpression><![CDATA[$F{payment_type} == "Կանխիկ"]]></printWhenExpression>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA["Օպերատոր"]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement positionType="Float" x="1" y="629" width="510" height="20" forecolor="#4A4848" uuid="80c0fd8a-d1b2-43a6-9534-0156d1b997a9">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<printWhenExpression><![CDATA[$F{payment_type} == "Կանխիկ"]]></printWhenExpression>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textFieldExpression><![CDATA[$F{withdrawn_user}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement positionType="Float" mode="Opaque" x="2" y="280" width="510" height="16" forecolor="#0D0C0C" backcolor="#CFCACA" uuid="c57c5997-d0d6-4f93-ab37-c2433a3b0e25"/>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA["Աշխատավայր"]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="false">
				<reportElement positionType="Float" x="0" y="296" width="510" height="20" forecolor="#4A4848" uuid="f35f2e44-0f01-405c-baef-33d94c2ccdd2">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$F{payment_type} == "Կանխիկ"]]></printWhenExpression>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textFieldExpression><![CDATA[$F{work_data}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement positionType="Float" x="1" y="502" width="510" height="20" isRemoveLineWhenBlank="true" forecolor="#4A4848" uuid="0e21638d-a634-4bb1-88c7-6c0f1ca5cf24">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textFieldExpression><![CDATA[$F{payment_id}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement positionType="Float" mode="Opaque" x="1" y="480" width="510" height="16" isRemoveLineWhenBlank="true" forecolor="#0D0C0C" backcolor="#CFCACA" uuid="92300f77-1b0b-471c-b940-b17f244930ba">
					<printWhenExpression><![CDATA[$F{payment_id} != null]]></printWhenExpression>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA["Համար"]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement positionType="Float" mode="Opaque" x="3" y="320" width="510" height="16" forecolor="#0D0C0C" backcolor="#CFCACA" uuid="17284011-5b34-4cf3-a186-1afa1afa5f88"/>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA["Վարկի տեսակ"]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement positionType="Float" x="3" y="340" width="510" height="20" forecolor="#4A4848" uuid="c7fd2c79-54ea-4dac-b42b-935a23f6778d">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textFieldExpression><![CDATA[$F{loan_type_id}]]></textFieldExpression>
			</textField>
		</band>
		<band height="742">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<textField textAdjust="StretchHeight" hyperlinkType="Reference" hyperlinkTarget="Blank" bookmarkLevel="1">
				<reportElement positionType="Float" x="1" y="397" width="510" height="20" isRemoveLineWhenBlank="true" forecolor="#4D81DB" uuid="7ee658e9-322b-4138-973c-3f6b7b2ae70b">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<printWhenExpression><![CDATA[$F{contract_ocl} != null]]></printWhenExpression>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="12" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Պայմանագիր"]]></textFieldExpression>
				<anchorNameExpression><![CDATA[$F{personal_sheet}]]></anchorNameExpression>
				<hyperlinkReferenceExpression><![CDATA["https://gc-covid-loan-documents.s3.eu-central-1.amazonaws.com/pdfs/"+$F{contract_ocl}]]></hyperlinkReferenceExpression>
			</textField>
			<textField textAdjust="StretchHeight" hyperlinkType="Reference" hyperlinkTarget="Blank">
				<reportElement positionType="Float" mode="Opaque" x="1" y="350" width="508" height="16" forecolor="#0D0C0C" backcolor="#CFCACA" uuid="612ca186-f4bd-4398-8c86-ad1f4e6a2754">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA["Փաստաթղթեր"]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" hyperlinkType="Reference" hyperlinkTarget="Blank">
				<reportElement positionType="Float" x="0" y="430" width="510" height="20" isRemoveLineWhenBlank="true" forecolor="#4D81DB" uuid="30f55f3d-0697-43af-afa6-ccf8ee67b3bf">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<printWhenExpression><![CDATA[$F{application}!=null]]></printWhenExpression>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="12" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Դիմում"]]></textFieldExpression>
				<hyperlinkReferenceExpression><![CDATA["https://gc-covid-loan-documents.s3.eu-central-1.amazonaws.com/pdfs/"+$F{application}]]></hyperlinkReferenceExpression>
			</textField>
			<textField textAdjust="StretchHeight" hyperlinkType="Reference" hyperlinkTarget="Blank">
				<reportElement positionType="Float" x="0" y="460" width="510" height="20" isRemoveLineWhenBlank="true" forecolor="#4D81DB" uuid="6298b239-fee1-4c1a-80b6-9239caa9530d">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$F{personal_sheet}!=null]]></printWhenExpression>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="12" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Անհատական թերթիկ"]]></textFieldExpression>
				<hyperlinkReferenceExpression><![CDATA["https://gc-covid-loan-documents.s3.eu-central-1.amazonaws.com/pdfs/"+$F{personal_sheet}]]></hyperlinkReferenceExpression>
			</textField>
			<textField textAdjust="StretchHeight" hyperlinkType="Reference" hyperlinkTarget="Blank">
				<reportElement positionType="Float" x="0" y="483" width="510" height="20" isRemoveLineWhenBlank="true" forecolor="#4D81DB" uuid="71dba0b2-5491-4d2f-8ebe-8ca5b3535a85">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="12" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Իրավաբանի փաստաթուղթ"]]></textFieldExpression>
				<hyperlinkReferenceExpression><![CDATA["https://gc-covid-loan-documents.s3.eu-central-1.amazonaws.com/pdfs/"+$F{lawyer}]]></hyperlinkReferenceExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true" hyperlinkType="Reference" hyperlinkTarget="Blank">
				<reportElement positionType="Float" x="-2" y="649" width="511" height="20" isRemoveLineWhenBlank="true" forecolor="#4D81DB" uuid="83c3cf93-9641-498e-b956-666e0f902f2e">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$F{statement} != null]]></printWhenExpression>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="12" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Հայտարարություն"]]></textFieldExpression>
				<hyperlinkReferenceExpression><![CDATA["https://gc-covid-loan-documents.s3.eu-central-1.amazonaws.com/pdfs/"+$F{statement}]]></hyperlinkReferenceExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true" hyperlinkType="Reference" hyperlinkTarget="Blank">
				<reportElement positionType="Float" x="-1" y="649" width="511" height="20" isRemoveLineWhenBlank="true" forecolor="#4D81DB" uuid="e22416d1-213c-45bb-aa2e-2244d0f97d42">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<printWhenExpression><![CDATA[$F{agreement} != null]]></printWhenExpression>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="12" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Համաձայնություն"]]></textFieldExpression>
				<hyperlinkReferenceExpression><![CDATA["https://gc-covid-loan-documents.s3.eu-central-1.amazonaws.com/pdfs/"+$F{agreement}]]></hyperlinkReferenceExpression>
			</textField>
			<textField textAdjust="StretchHeight" hyperlinkType="Reference" hyperlinkTarget="Blank">
				<reportElement positionType="Float" x="0" y="619" width="511" height="20" isRemoveLineWhenBlank="true" forecolor="#4D81DB" uuid="fa61757f-4614-43ac-bd21-dfa63c7899dd">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$F{power_of_attorney} != null]]></printWhenExpression>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="12" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Լիազորագիր"]]></textFieldExpression>
				<hyperlinkReferenceExpression><![CDATA["https://gc-covid-loan-documents.s3.eu-central-1.amazonaws.com/pdfs/"+$F{power_of_attorney}]]></hyperlinkReferenceExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true" hyperlinkType="Reference" hyperlinkTarget="Blank">
				<reportElement positionType="Float" x="1" y="509" width="511" height="20" isRemoveLineWhenBlank="true" forecolor="#4D81DB" uuid="2a53dd78-f3e2-4350-939f-2aa687b58d1c">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[$F{arbitration} != null]]></printWhenExpression>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="12" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Վեճերի լուծման ընթացակարգ"]]></textFieldExpression>
				<hyperlinkReferenceExpression><![CDATA["https://gc-covid-loan-documents.s3.eu-central-1.amazonaws.com/pdfs/"+$F{arbitration}]]></hyperlinkReferenceExpression>
			</textField>
			<textField textAdjust="StretchHeight" hyperlinkType="Reference" hyperlinkTarget="Blank" bookmarkLevel="1">
				<reportElement positionType="Float" x="2" y="397" width="510" height="20" isRemoveLineWhenBlank="true" forecolor="#4D81DB" uuid="cfcd85da-ee5b-4061-8063-017c4f5227ff">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<printWhenExpression><![CDATA[$F{contract_ovl} != null]]></printWhenExpression>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="12" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Պայմանագիր"]]></textFieldExpression>
				<anchorNameExpression><![CDATA[$F{personal_sheet}]]></anchorNameExpression>
				<hyperlinkReferenceExpression><![CDATA["https://gc-covid-loan-documents.s3.eu-central-1.amazonaws.com/pdfs/"+$F{contract_ovl}]]></hyperlinkReferenceExpression>
			</textField>
			<textField textAdjust="StretchHeight" hyperlinkType="Reference" hyperlinkTarget="Blank" bookmarkLevel="1">
				<reportElement positionType="Float" x="0" y="397" width="510" height="20" isRemoveLineWhenBlank="true" forecolor="#4D81DB" uuid="11480d57-0aaa-4ba2-a2ba-312e9e63dbea">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<printWhenExpression><![CDATA[$F{contract_oiql} != null]]></printWhenExpression>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="12" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Պայմանագիր"]]></textFieldExpression>
				<anchorNameExpression><![CDATA[$F{personal_sheet}]]></anchorNameExpression>
				<hyperlinkReferenceExpression><![CDATA["https://gc-covid-loan-documents.s3.eu-central-1.amazonaws.com/pdfs/"+$F{contract_oiql}]]></hyperlinkReferenceExpression>
			</textField>
			<textField textAdjust="StretchHeight" hyperlinkType="Reference" hyperlinkTarget="Blank">
				<reportElement positionType="Float" x="0" y="589" width="511" height="20" isRemoveLineWhenBlank="true" forecolor="#4D81DB" uuid="06ff0f9a-67d2-4d74-8dd4-c57f1cec662f">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$F{mortgage_contract} != null]]></printWhenExpression>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="12" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Գրավի պայմանագիր"]]></textFieldExpression>
				<hyperlinkReferenceExpression><![CDATA["https://gc-covid-loan-documents.s3.eu-central-1.amazonaws.com/pdfs/"+$F{mortgage_contract}]]></hyperlinkReferenceExpression>
			</textField>
			<textField textAdjust="StretchHeight" hyperlinkType="Reference" hyperlinkTarget="Blank" bookmarkLevel="1">
				<reportElement positionType="Float" x="0" y="559" width="510" height="20" isRemoveLineWhenBlank="true" forecolor="#4D81DB" uuid="46246a86-b4d5-4396-a3ee-91168a8d43a0">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA[$F{withdraw_check} != null]]></printWhenExpression>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="12" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Կանխիկացման կտրոն"]]></textFieldExpression>
				<anchorNameExpression><![CDATA[$F{personal_sheet}]]></anchorNameExpression>
				<hyperlinkReferenceExpression><![CDATA["https://gc-covid-loan-documents.s3.eu-central-1.amazonaws.com/pdfs/"+$F{withdraw_check}]]></hyperlinkReferenceExpression>
			</textField>
			<textField textAdjust="StretchHeight" hyperlinkType="Reference" hyperlinkTarget="Blank" bookmarkLevel="1">
				<reportElement positionType="Float" x="-1" y="399" width="510" height="20" isRemoveLineWhenBlank="true" forecolor="#4D81DB" uuid="c4777f24-0365-4f60-8431-b314758f74ff">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<printWhenExpression><![CDATA[$F{contract} != null]]></printWhenExpression>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="12" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Պայմանագիր"]]></textFieldExpression>
				<anchorNameExpression><![CDATA[$F{personal_sheet}]]></anchorNameExpression>
				<hyperlinkReferenceExpression><![CDATA["https://gc-covid-loan-documents.s3.eu-central-1.amazonaws.com/pdfs/"+$F{contract}]]></hyperlinkReferenceExpression>
			</textField>
			<frame>
				<reportElement x="-1" y="10" width="518" height="326" isRemoveLineWhenBlank="true" backcolor="#F5403D" uuid="b4330e09-7c21-4dce-95fa-74a5684169ed">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[$F{vin} != null]]></printWhenExpression>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineColor="#F02E2B"/>
				</box>
				<textField textAdjust="StretchHeight" isBlankWhenNull="false">
					<reportElement positionType="Float" isPrintRepeatedValues="false" mode="Opaque" x="1" y="39" width="508" height="16" isRemoveLineWhenBlank="true" forecolor="#0D0C0C" backcolor="#CFCACA" uuid="f5898a84-d24d-47e7-a19e-49a381abb044">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<printWhenExpression><![CDATA[$F{vin} != null]]></printWhenExpression>
					</reportElement>
					<box leftPadding="10" rightPadding="10">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA["Գրավի պայմանագրի համար"]]></textFieldExpression>
				</textField>
				<textField textAdjust="StretchHeight" isBlankWhenNull="false">
					<reportElement positionType="Float" isPrintRepeatedValues="false" x="1" y="59" width="508" height="20" isRemoveLineWhenBlank="true" forecolor="#4A4848" uuid="f9386290-e740-4cb1-9b7e-b658fbcb9b59">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<printWhenExpression><![CDATA[$F{vin} != null]]></printWhenExpression>
					</reportElement>
					<box leftPadding="10" rightPadding="10">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textFieldExpression><![CDATA[$F{mortgage_contract_number}]]></textFieldExpression>
				</textField>
				<textField textAdjust="StretchHeight" isBlankWhenNull="false">
					<reportElement positionType="Float" mode="Opaque" x="1" y="79" width="508" height="16" isRemoveLineWhenBlank="true" forecolor="#0D0C0C" backcolor="#CFCACA" uuid="f6f1d94a-112c-407c-95c8-f39b5da1a35d">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<printWhenExpression><![CDATA[$F{vin} !=null]]></printWhenExpression>
					</reportElement>
					<box leftPadding="10" rightPadding="10">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA["Մեքենայի համարանիշ"]]></textFieldExpression>
				</textField>
				<textField textAdjust="StretchHeight" isBlankWhenNull="false">
					<reportElement positionType="Float" x="1" y="99" width="508" height="20" isRemoveLineWhenBlank="true" forecolor="#4A4848" uuid="dd466328-0516-4480-ad46-c2298168d66d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<printWhenExpression><![CDATA[$F{vin} != null]]></printWhenExpression>
					</reportElement>
					<box leftPadding="10" rightPadding="10">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textFieldExpression><![CDATA[$F{vehicle_number}]]></textFieldExpression>
				</textField>
				<textField textAdjust="StretchHeight" isBlankWhenNull="false">
					<reportElement positionType="Float" mode="Opaque" x="1" y="120" width="508" height="16" isRemoveLineWhenBlank="true" forecolor="#0D0C0C" backcolor="#CFCACA" uuid="accd8b28-3c08-4d3c-934e-a65e9801728a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<printWhenExpression><![CDATA[$F{vin} != null]]></printWhenExpression>
					</reportElement>
					<box leftPadding="10" rightPadding="10">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA["Գրավի պայմանագրի համար"]]></textFieldExpression>
				</textField>
				<textField textAdjust="StretchHeight" isBlankWhenNull="false">
					<reportElement positionType="Float" x="1" y="141" width="508" height="20" isRemoveLineWhenBlank="true" forecolor="#4A4848" uuid="100f7a2b-a009-4aad-8fe4-d2cb5d34f505">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<printWhenExpression><![CDATA[$F{vin} != null]]></printWhenExpression>
					</reportElement>
					<box leftPadding="10" rightPadding="10">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textFieldExpression><![CDATA[$F{mortgage_contract_number}]]></textFieldExpression>
				</textField>
				<textField textAdjust="StretchHeight" isBlankWhenNull="false">
					<reportElement positionType="Float" mode="Opaque" x="1" y="161" width="508" height="16" isRemoveLineWhenBlank="true" forecolor="#0D0C0C" backcolor="#CFCACA" uuid="3ff9141a-010d-4ae4-b9d9-a844f3cbdbb9">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<printWhenExpression><![CDATA[$F{vin} != null]]></printWhenExpression>
					</reportElement>
					<box leftPadding="10" rightPadding="10">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA["ՎԻՆ"]]></textFieldExpression>
				</textField>
				<textField textAdjust="StretchHeight" isBlankWhenNull="false">
					<reportElement positionType="Float" x="1" y="181" width="508" height="20" isRemoveLineWhenBlank="true" forecolor="#4A4848" uuid="3c9b21bb-f265-4384-a6c5-5d59728aae4e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<printWhenExpression><![CDATA[$F{vin} != null]]></printWhenExpression>
					</reportElement>
					<box leftPadding="10" rightPadding="10">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textFieldExpression><![CDATA[$F{vin}]]></textFieldExpression>
				</textField>
				<textField textAdjust="StretchHeight" isBlankWhenNull="false">
					<reportElement positionType="Float" mode="Opaque" x="1" y="201" width="508" height="16" isRemoveLineWhenBlank="true" forecolor="#0D0C0C" backcolor="#CFCACA" uuid="30d33fb7-4a5c-47cc-9e71-eac73c5483f8">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<printWhenExpression><![CDATA[$F{vin} != null]]></printWhenExpression>
					</reportElement>
					<box leftPadding="10" rightPadding="10">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA["Մոդել"]]></textFieldExpression>
				</textField>
				<textField textAdjust="StretchHeight" isBlankWhenNull="false">
					<reportElement positionType="Float" x="1" y="220" width="508" height="20" isRemoveLineWhenBlank="true" forecolor="#4A4848" uuid="04e7ccf0-3051-484d-8114-955b39885db6">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<printWhenExpression><![CDATA[$F{vin} != null]]></printWhenExpression>
					</reportElement>
					<box leftPadding="10" rightPadding="10">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textFieldExpression><![CDATA[$F{vehicle_model}]]></textFieldExpression>
				</textField>
				<textField textAdjust="StretchHeight" isBlankWhenNull="false">
					<reportElement positionType="Float" mode="Opaque" x="1" y="239" width="508" height="16" isRemoveLineWhenBlank="true" forecolor="#0D0C0C" backcolor="#CFCACA" uuid="78a4eac0-4805-4a99-abb7-f875fac40f36">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<printWhenExpression><![CDATA[$F{vin} != null]]></printWhenExpression>
					</reportElement>
					<box leftPadding="10" rightPadding="10">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA["Սեփականության ծագման ամսաթիվ"]]></textFieldExpression>
				</textField>
				<textField textAdjust="StretchHeight" isBlankWhenNull="false">
					<reportElement positionType="Float" x="1" y="258" width="508" height="20" forecolor="#4A4848" uuid="a8bf97b5-e091-41bd-b3d8-64a586eed8af">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<printWhenExpression><![CDATA[$F{vin} != null]]></printWhenExpression>
					</reportElement>
					<box leftPadding="10" rightPadding="10">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textFieldExpression><![CDATA[$F{recording_date}]]></textFieldExpression>
				</textField>
				<textField textAdjust="StretchHeight" isBlankWhenNull="false">
					<reportElement positionType="Float" mode="Opaque" x="1" y="281" width="508" height="16" isRemoveLineWhenBlank="true" forecolor="#0D0C0C" backcolor="#CFCACA" uuid="2d53412d-0cc6-472c-9e06-9566bc0de28b">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<printWhenExpression><![CDATA[$F{vin} != null]]></printWhenExpression>
					</reportElement>
					<box leftPadding="10" rightPadding="10">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA["Սեփականության վկայական"]]></textFieldExpression>
				</textField>
				<textField textAdjust="StretchHeight" isBlankWhenNull="false">
					<reportElement positionType="Float" x="1" y="300" width="508" height="20" isRemoveLineWhenBlank="true" forecolor="#4A4848" uuid="01593931-3dd4-40fc-94a8-e13b0125ab38">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<printWhenExpression><![CDATA[$F{vin} != null]]></printWhenExpression>
					</reportElement>
					<box leftPadding="10" rightPadding="10">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textFieldExpression><![CDATA[$F{owner_cert_id}]]></textFieldExpression>
				</textField>
				<textField textAdjust="StretchHeight" isBlankWhenNull="false">
					<reportElement positionType="Float" mode="Opaque" x="1" y="0" width="508" height="16" isRemoveLineWhenBlank="true" forecolor="#0D0C0C" backcolor="#CFCACA" uuid="100a8e1e-9ec0-48c3-9daf-23535f447292">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<printWhenExpression><![CDATA[$F{vin} !=null]]></printWhenExpression>
					</reportElement>
					<box leftPadding="10" rightPadding="10">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA["Ամուսին"]]></textFieldExpression>
				</textField>
				<textField textAdjust="StretchHeight" isBlankWhenNull="false">
					<reportElement positionType="Float" x="1" y="19" width="508" height="20" forecolor="#4A4848" uuid="fc251075-ee12-4edf-873d-21d138eeaaa7">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<printWhenExpression><![CDATA[$F{vin} !=null]]></printWhenExpression>
					</reportElement>
					<box leftPadding="10" rightPadding="10">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textFieldExpression><![CDATA[$F{spouse}]]></textFieldExpression>
				</textField>
			</frame>
			<textField textAdjust="StretchHeight" hyperlinkType="Reference" hyperlinkTarget="Blank">
				<reportElement positionType="Float" x="1" y="369" width="510" height="20" isRemoveLineWhenBlank="true" forecolor="#4D81DB" uuid="2ece7337-b438-4dce-8647-194d47ab9558">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<printWhenExpression><![CDATA[$F{image_path} != null]]></printWhenExpression>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="12" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Լուսանկար"]]></textFieldExpression>
				<hyperlinkReferenceExpression><![CDATA["https://gc-covid-citizens-recognition.s3.eu-central-1.amazonaws.com/"+$F{image_path}]]></hyperlinkReferenceExpression>
			</textField>
			<textField textAdjust="StretchHeight" hyperlinkType="Reference" hyperlinkTarget="Blank">
				<reportElement positionType="Float" x="0" y="534" width="509" height="20" isRemoveLineWhenBlank="true" forecolor="#4D81DB" uuid="2622f7da-9b4a-4082-8629-5cea846dca78">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<printWhenExpression><![CDATA[$F{optimuslex}!=null]]></printWhenExpression>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="12" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Արբիտրաժային համաձայնություն"]]></textFieldExpression>
				<hyperlinkReferenceExpression><![CDATA["https://gc-covid-loan-documents.s3.eu-central-1.amazonaws.com/pdfs/"+$F{optimuslex}]]></hyperlinkReferenceExpression>
			</textField>
			<textField textAdjust="StretchHeight" hyperlinkType="Reference" hyperlinkTarget="Blank">
				<reportElement positionType="Float" x="2" y="535" width="509" height="20" isRemoveLineWhenBlank="true" forecolor="#4D81DB" uuid="53e6bf69-9916-4fc3-9231-248dc6d9e331">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<printWhenExpression><![CDATA[$F{uba_arbitration}!=null]]></printWhenExpression>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="12" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Արբիտրաժային համաձայնություն"]]></textFieldExpression>
				<hyperlinkReferenceExpression><![CDATA["https://gc-covid-loan-documents.s3.eu-central-1.amazonaws.com/pdfs/"+$F{uba_arbitration}]]></hyperlinkReferenceExpression>
			</textField>
			<textField textAdjust="StretchHeight" hyperlinkType="Reference" hyperlinkTarget="Blank">
				<reportElement positionType="Float" x="1" y="534" width="509" height="20" isRemoveLineWhenBlank="true" forecolor="#4D81DB" uuid="0e1e6d43-9863-4c73-b694-1cfc51733033">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<printWhenExpression><![CDATA[$F{gnm_arbitration}!=null]]></printWhenExpression>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="12" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Արբիտրաժային համաձայնություն"]]></textFieldExpression>
				<hyperlinkReferenceExpression><![CDATA["https://gc-covid-loan-documents.s3.eu-central-1.amazonaws.com/pdfs/"+$F{gnm_arbitration}]]></hyperlinkReferenceExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true" hyperlinkType="Reference" hyperlinkTarget="Blank" bookmarkLevel="1">
				<reportElement positionType="Float" x="-1" y="679" width="510" height="20" isRemoveLineWhenBlank="true" forecolor="#4D81DB" uuid="aa7d7115-b880-4f5d-b462-b59dc8fca030">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA[$F{vendor_file} != null]]></printWhenExpression>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="12" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Կցված փաստաթուղթ"]]></textFieldExpression>
				<anchorNameExpression><![CDATA[$F{vendor_file}]]></anchorNameExpression>
				<hyperlinkReferenceExpression><![CDATA["https://gc-covid-loan-documents.s3.eu-central-1.amazonaws.com/pdfs/"+$F{vendor_file}]]></hyperlinkReferenceExpression>
			</textField>
			<textField textAdjust="StretchHeight" hyperlinkType="Reference" hyperlinkTarget="Blank">
				<reportElement positionType="Float" x="3" y="460" width="510" height="20" isRemoveLineWhenBlank="true" forecolor="#4D81DB" uuid="f8848ed4-2430-4700-982c-57302879ab01">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$F{personal_sheet_oidl}!=null]]></printWhenExpression>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="12" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Անհատական թերթիկ"]]></textFieldExpression>
				<hyperlinkReferenceExpression><![CDATA["https://gc-covid-loan-documents.s3.eu-central-1.amazonaws.com/pdfs/"+$F{personal_sheet_oidl}]]></hyperlinkReferenceExpression>
			</textField>
			<textField textAdjust="StretchHeight" hyperlinkType="Reference" hyperlinkTarget="Blank" bookmarkLevel="1">
				<reportElement positionType="Float" x="7" y="397" width="510" height="20" isRemoveLineWhenBlank="true" forecolor="#4D81DB" uuid="918e72e8-7c0e-45e9-be92-cfe31cebf282">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<printWhenExpression><![CDATA[$F{contract_oidl} != null]]></printWhenExpression>
				</reportElement>
				<box leftPadding="10" rightPadding="10">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="12" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Պայմանագիր"]]></textFieldExpression>
				<anchorNameExpression><![CDATA[$F{personal_sheet}]]></anchorNameExpression>
				<hyperlinkReferenceExpression><![CDATA["https://gc-covid-loan-documents.s3.eu-central-1.amazonaws.com/pdfs/"+$F{contract_oidl}]]></hyperlinkReferenceExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
