#deviated-schedules {
  .card {
    padding: 10px;
    min-height: 200px;

    .deviated-schedules-content {
      width: fit-content;
      margin: 25px auto;

      input {
        width: 250px;
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
        line-height: 1.5;
        color: #495057;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        transition: border-color 0.15s;
      }

      .deviated-schedules-button {
        color: #fff;
        background-color: #007bff;
        border-color: #007bff;
        display: inline-block;
        font-weight: 400;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        user-select: none;
        border: 1px solid transparent;
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: 0.25rem;
        transition: color 0.15s;
        margin: 3px 0;

        &:hover {
          background-color: #0069d9;
          border-color: #0062cc;
        }

        &.success {
          background-color: #67c23a;
          border-color: #67c23a;
        }
      }

      .empty-loan {
        margin-top: 20px;
        color: red;
      }

      .citizen-table {
        display: flex;
        margin-top: 50px;

        p {
          text-align: center;
          font-weight: 600;
          margin-bottom: 10px;
        }

        .corrected {
          margin-left: 50px;
          font-weight: 600;
          margin-bottom: 10px;
        }

        .table {
          width: 570px;
          margin-bottom: 1rem;

          tbody {
            tr {
              td {
                height: 37px;
              }

              &:last-child {
                &:hover {
                  td {
                    background: unset;
                  }
                }
              }

              &:nth-of-type(odd) {
                background-color: rgba(0, 0, 0, 0.05);
              }

              &.border-dotted {
                td {
                  border-top: 2px dotted;
                }
              }
            }

            td {
              &:first-child {
                width: 20px;
              }
              border-top: 1px solid #dee2e6;
            }
          }
        }
      }

      .correct-button {
        display: flex;
        justify-content: flex-end;
      }

      .document {
        text-align: center;
      }
    }
  }

  .success {
    background-color: #67c23a;
    border-color: #67c23a;
  }
}
