<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Tool API Routes
|--------------------------------------------------------------------------
|
| Here is where you may register API routes for your tool. These routes
| are loaded by the ServiceProvider of your tool. You're free to add
| as many additional routes to this file as your tool may require.
|
*/

Route::get('/loan/{id}/vehicle-condition', 'Globalcredit\ApproveVehicleLoan\Http\Controllers\ApproveVehicleLoanController@getVehicleCondition');

Route::post('/loan/{loan_id}/vehicle-condition', 'Globalcredit\ApproveVehicleLoan\Http\Controllers\ApproveVehicleLoanController@storeVehicleCondition');

Route::post('/loan/{id}/review', 'Globalcredit\ApproveVehicleLoan\Http\Controllers\ApproveVehicleLoanController@review');

Route::post('/loan/{id}/process', 'Globalcredit\ApproveVehicleLoan\Http\Controllers\ApproveVehicleLoanController@process');

Route::post('/loan/{id}/confirm', 'Globalcredit\ApproveVehicleLoan\Http\Controllers\ApproveVehicleLoanController@confirm');

Route::post('/loan/{id}/reject', 'Globalcredit\ApproveVehicleLoan\Http\Controllers\ApproveVehicleLoanController@reject');

Route::post('/loan/{id}/police-file', 'Globalcredit\ApproveVehicleLoan\Http\Controllers\ApproveVehicleLoanController@storePoliceFile');
