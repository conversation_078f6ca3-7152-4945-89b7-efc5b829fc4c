<?php

namespace Globalcredit\LoanExpirationCard\Services;

use App\Models\Loan;
use Carbon\Carbon;

class LoanExpirationService
{
    public function updateLoanExpirationDate($dates, $days)
    {
        $start_date = Carbon::createFromFormat('d/m/Y H:i:s', $dates['start'].' '.constants('EXPIRATION_TIME'))
            ->subDays(1);

        $end_date = Carbon::createFromFormat('d/m/Y H:i:s', $dates['end'].' '.constants('EXPIRATION_TIME'));

        Loan::where([
                ['loan_type_id', constants('LOAN_TYPES.OASL')],
                ['sign_date', '>=', $start_date],
                ['sign_date', '<=', $end_date],
            ])
            ->whereIn('status', [Loan::PENDING, Loan::PROCESSING])
            ->with('solar_panel')
            ->get()
            ->map(function ($loan) use ($days) {
                $loan->solar_panel->update([
                    'expiration_date' => $loan->sign_date->addDays($days),
                ]);
            });
    }
}
