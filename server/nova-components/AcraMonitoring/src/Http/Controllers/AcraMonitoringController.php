<?php

namespace Globalcredit\AcraMonitoring\Http\Controllers;

use App\Exceptions\InternalErrorException;
use App\Exceptions\InvalidExcelFormatException;
use App\Http\Controllers\Controller;
use App\Jobs\CitizenMonitoring;
use Exception;
use Globalcredit\AcraMonitoring\Services\CitizenMonitoringService;
use Laravel\Nova\Http\Requests\NovaRequest;
use Log;

class AcraMonitoringController extends Controller
{
    private $citizen_monitoring_service;

    public function __construct(CitizenMonitoringService $citizen_monitoring_service)
    {
        $this->citizen_monitoring_service = $citizen_monitoring_service;
    }

    public function start(NovaRequest $request)
    {
        try {
            $type = $request->get('type');
            $file = $request->file('file');
            $file_name = $file->getClientOriginalName();

            $this->citizen_monitoring_service->startMonitoring($file_name);

            $parse_result = $this->citizen_monitoring_service->parseCitizensExcel($file);
            $citizens_list = $parse_result['citizens'];
            $stored_filename = $parse_result['filename'];

            $citizens = $this->citizen_monitoring_service->composeAcraCitizensList($citizens_list);

            CitizenMonitoring::dispatch($citizens, $type, $stored_filename);

            return response()->json(['success' => true]);
        } catch (InvalidExcelFormatException $e) {
            $this->citizen_monitoring_service->stopMonitoring(constants('NOVA.MONITORING_STATUSES.FAILED'));

            Log::error('Monitoring Start, InvalidExcelFormatException', ['message' => $e->getMessage()]);
            throw $e;
        } catch (Exception $e) {
            $this->citizen_monitoring_service->stopMonitoring(constants('NOVA.MONITORING_STATUSES.FAILED'));

            Log::error('Exception', ['error' => $e->getMessage()]);
            throw new InternalErrorException();
        }
    }

    public function getMonitoring()
    {
        $monitoring = $this->citizen_monitoring_service->getMonitoringState();

        return response()->json([
            'status' => $monitoring['status'] ?? null,
            'name' => $monitoring['name'] ?? null,
        ]);
    }
}
